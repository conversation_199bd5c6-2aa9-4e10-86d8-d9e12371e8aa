from fastapi import APIRouter, HTTPException, Depends, Request
import sqlalchemy
import hashlib
import logging
from datetime import datetime, timezone, timedelta
import asyncio

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.models import (
        users,
        email_history,
        MicrosoftEmailRequest,
    )
    from api.db import get_db, with_db_retry, rate_limit
    from api.auth import get_current_user, get_client_ip
    from api.utils.common import get_request_host
    from api.utils.microsoft_email_api import (
        get_microsoft_emails_optimized,
        extract_verification_code,
    )
    from api.utils.microsoft_email_cache import MicrosoftEmailCacheManager
    from api.utils.startup_tasks import (
        ensure_startup_tasks_executed,
        run_periodic_cleanup_if_needed,
    )
    from api.utils.billing import record_consumption
except:
    from models import (
        users,
        email_history,
        MicrosoftEmailRequest,
    )
    from db import get_db, with_db_retry, rate_limit
    from auth import get_current_user, get_client_ip
    from utils.common import get_request_host
    from utils.microsoft_email_api import (
        get_microsoft_emails_optimized,
        extract_verification_code,
    )
    from utils.microsoft_email_cache import MicrosoftEmailCacheManager
    from utils.startup_tasks import (
        ensure_startup_tasks_executed,
        run_periodic_cleanup_if_needed,
    )
    from utils.billing import record_consumption

router = APIRouter(prefix="/api/v1", tags=["microsoft_email"])


@router.post("/microsoft-email")
async def get_microsoft_email(
    request: MicrosoftEmailRequest,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    """
    获取微软邮箱邮件（优化版）

    参数:
    - account: 微软邮箱账号
    - password: 微软邮箱密码
    - num: 获取邮件数量，默认"10"
    - sender_email: 发件人邮箱地址筛选（可选）
    - mailbox: 邮箱文件夹（可选）
    - proxy: 代理地址（可选）

    优化特性:
    - 登录状态缓存（1小时有效期）
    - 适配Vercel无服务器环境
    - 自动清理过期数据
    """
    # 确保启动任务已执行（Vercel冷启动优化）
    ensure_startup_tasks_executed()

    # 定期清理过期数据（10%概率执行）
    run_periodic_cleanup_if_needed()

    client_ip = get_client_ip(req) if req else "unknown"
    request_host = get_request_host(req) if req else None

    with rate_limit(f"microsoft_email:{client_ip}", max_requests=10, expire=60):
        # 获取微软邮件的逻辑
        @with_db_retry(max_retries=3)
        async def get_microsoft_emails_with_billing():
            with get_db() as db:
                try:
                    # 处理num参数，转换为整数
                    try:
                        num_count = int(request.num) if request.num else 10
                    except (ValueError, TypeError):
                        num_count = 10

                    # 调用优化版微软邮箱API获取邮件
                    result = get_microsoft_emails_optimized(
                        account=request.account,
                        password=request.password,
                        count=num_count,
                        sender_email=request.sender_email,
                        mailbox=request.mailbox,
                        proxy=request.proxy,
                        request_host=request_host,
                        username=current_user["username"],
                        fast_mode=request.fast_mode or False,  # 支持快速模式
                    )

                    if not result["status"]:
                        return {
                            "code": 400,
                            "status": False,
                            "data": {"message": result["msg"]},
                        }

                    emails = result.get("emails", [])

                    if not emails:
                        return {
                            "code": 200,
                            "status": True,
                            "data": {
                                "account": request.account,
                                "emails": [],
                                "count": 0,
                                "new_emails": 0,
                                "fee": 0,
                                "balance": current_user["balance"],
                                "message": "未获取到邮件",
                            },
                        }

                    # 为每封邮件添加格式化时间
                    for email in emails:
                        # 格式化时间
                        if "Time" in email and email["Time"]:
                            try:
                                timestamp = int(email["Time"])
                                utc8 = timezone(
                                    timezone.utc.utcoffset(None) + timedelta(hours=8)
                                )
                                email_time = datetime.fromtimestamp(timestamp, tz=utc8)
                                email["FormatTime"] = email_time.strftime(
                                    "%Y-%m-%d %H:%M:%S"
                                )
                            except Exception as e:
                                logger.error(f"时间转换错误: {str(e)}")
                                email["FormatTime"] = "时间格式错误"

                    # 查询用户之前接收的邮件内容哈希
                    with db.begin():
                        stmt = sqlalchemy.select(email_history.c.content_hash).where(
                            email_history.c.username == current_user["username"]
                        )
                        query_result = db.execute(stmt)
                        existing_hashes = {row[0] for row in query_result}

                        # 批量收集需要插入的数据
                        new_emails = []
                        insert_values = []

                        for email in emails:
                            content = email.get("Content", "")
                            content_hash = hashlib.md5(content.encode()).hexdigest()

                            # 检查是否为新内容
                            if content_hash not in existing_hashes:
                                new_emails.append(email)
                                # 收集批量插入数据
                                utc8 = timezone(
                                    timezone.utc.utcoffset(None) + timedelta(hours=8)
                                )
                                insert_values.append(
                                    {
                                        "username": current_user["username"],
                                        "email": request.account,
                                        "content_hash": content_hash,
                                        "received_at": datetime.now(tz=utc8),
                                    }
                                )

                        # 批量插入，减少数据库操作次数
                        if insert_values:
                            db.execute(email_history.insert(), insert_values)

                        # 计算扣费金额
                        new_email_count = len(new_emails)
                        fee = 50 * new_email_count if new_email_count > 0 else 0

                        # 检查是否使用了缓存
                        used_cache = result.get("cached", False)
                        cache_message = (
                            " (使用缓存登录)" if used_cache else " (完整登录)"
                        )

                        # 如果有新邮件且需要扣费
                        if fee > 0:
                            # 检查余额是否足够
                            if current_user["balance"] < fee:
                                raise HTTPException(
                                    status_code=400, detail=f"余额不足，需要{fee}点余额"
                                )

                            # 扣除余额
                            new_balance = current_user["balance"] - fee
                            update_stmt = (
                                users.update()
                                .where(users.c.username == current_user["username"])
                                .values(balance=new_balance)
                            )
                            db.execute(update_stmt)

                            # 记录消费
                            try:
                                asyncio.create_task(
                                    record_consumption(
                                        username=current_user["username"],
                                        details=f"获取微软邮箱 {request.account}，获取{new_email_count}封新邮件{cache_message}",
                                        amount=fee,
                                    )
                                )
                            except Exception as e:
                                logger.error(f"记录消费失败: {str(e)}")

                            response = {
                                "code": 200,
                                "status": True,
                                "data": {
                                    "account": request.account,
                                    "emails": emails,
                                    "count": len(emails),
                                    "new_emails": new_email_count,
                                    "fee": fee,
                                    "balance": new_balance,
                                    "cached": used_cache,
                                    "message": f"获取到{new_email_count}封新邮件，扣除{fee}点余额{cache_message}",
                                },
                            }
                            return response
                        else:
                            # 没有新邮件，不扣费
                            response = {
                                "code": 200,
                                "status": True,
                                "data": {
                                    "account": request.account,
                                    "emails": emails,
                                    "count": len(emails),
                                    "new_emails": 0,
                                    "fee": 0,
                                    "balance": current_user["balance"],
                                    "cached": used_cache,
                                    "message": f"没有新邮件，不扣费{cache_message}",
                                },
                            }
                            return response

                except Exception as e:
                    logger.error(f"获取微软邮箱邮件时发生错误: {str(e)}")
                    return {
                        "code": 500,
                        "status": False,
                        "data": {"message": f"获取邮件失败: {str(e)}"},
                    }

        return await get_microsoft_emails_with_billing()


@router.post("/microsoft-email/extract-code")
async def extract_code_from_email(
    request: MicrosoftEmailRequest,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    """
    从微软邮箱邮件中提取验证码（优化版）

    这是一个便捷接口，专门用于提取验证码
    优化特性:
    - 使用登录状态缓存
    - 适配Vercel无服务器环境
    """
    # 确保启动任务已执行
    ensure_startup_tasks_executed()

    client_ip = get_client_ip(req) if req else "unknown"

    with rate_limit(f"microsoft_email_code:{client_ip}", max_requests=20, expire=60):
        try:
            # 获取邮件
            request_host = get_request_host(req) if req else None
            result = get_microsoft_emails_optimized(
                account=request.account,
                password=request.password,
                count=5,  # 只获取最近5封邮件，这里固定为5
                sender_email=request.sender_email,
                proxy=request.proxy,
                request_host=request_host,
                username=current_user["username"],
            )

            if not result["status"]:
                return {
                    "code": 400,
                    "status": False,
                    "data": {"message": result["msg"]},
                }

            emails = result.get("emails", [])
            verification_codes = []

            # 从每封邮件中提取验证码
            for email in emails:
                content = email.get("Content", "")
                if content:
                    code = extract_verification_code(content)
                    if code:
                        verification_codes.append(
                            {
                                "subject": email.get("Subject", ""),
                                "from": email.get("From", ""),
                                "code": code,
                                "time": email.get("FormatTime", ""),
                            }
                        )

            # 检查是否使用了缓存
            used_cache = result.get("cached", False)
            cache_message = " (使用缓存登录)" if used_cache else " (完整登录)"

            return {
                "code": 200,
                "status": True,
                "data": {
                    "account": request.account,
                    "verification_codes": verification_codes,
                    "count": len(verification_codes),
                    "cached": used_cache,
                    "message": f"找到{len(verification_codes)}个验证码{cache_message}",
                },
            }

        except Exception as e:
            logger.error(f"提取验证码时发生错误: {str(e)}")
            return {
                "code": 500,
                "status": False,
                "data": {"message": f"提取验证码失败: {str(e)}"},
            }
