<template>
    <div class="modal" @click="closeModal">
        <div class="modal-content" @click.stop>
            <div class="modal-header">
                <h2>{{ api?.title }}</h2>
                <button class="modal-close" @click="closeModal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="api-docs">
                    <h3>请求接口</h3>
                    <div class="code-wrapper">
                        <button class="copy-btn" @click="copyCode(api?.apiInfo?.endpoint)">复制</button>
                        <pre><code><span class="method-tag">{{ api?.apiInfo?.method }}</span> {{ api?.apiInfo?.endpoint }}</code></pre>
                    </div>

                    <h3>请求参数</h3>
                    <div class="code-wrapper">
                        <button class="copy-btn"
                            @click="copyCode(cleanedParamsCode)">复制</button>
                        <pre><code>{{ cleanedParamsCode }}</code></pre>
                    </div>

                    <div class="params-table">
                        <div class="params-header">
                            <div class="param-name">参数名</div>
                            <div class="param-type">类型</div>
                            <div class="param-required">必填</div>
                            <div class="param-desc">说明</div>
                            <div class="param-example">示例值</div>
                        </div>
                        <div class="params-body">
                            <!-- 如果有结构化参数定义，优先使用 -->
                            <template v-if="api?.apiInfo?.paramsDef">
                                <div v-for="param in api.apiInfo.paramsDef" :key="param.name" class="param-row">
                                    <div class="param-name">{{ param.name }}</div>
                                    <div class="param-type">{{ param.type }}</div>
                                    <div class="param-required">
                                        <span :class="['required-tag', { required: param.required }]">
                                            {{ param.required ? '是' : '否' }}
                                        </span>
                                    </div>
                                    <div class="param-desc">{{ param.desc }}</div>
                                    <div class="param-example">
                                        <span v-if="param.example" class="example-tag">{{ param.example }}</span>
                                        <span v-else class="no-example-tag">无</span>
                                    </div>
                                </div>
                            </template>
                            <!-- 否则使用传统的注释解析方式 -->
                            <template v-else>
                                <div v-for="(param, name) in parsedParams" :key="name" class="param-row">
                                    <div class="param-name">{{ name }}</div>
                                    <div class="param-type">{{ param.type }}</div>
                                    <div class="param-required">
                                        <span :class="['required-tag', { required: param.required }]">
                                            {{ param.required ? '是' : '否' }}
                                        </span>
                                    </div>
                                    <div class="param-desc">{{ param.desc }}</div>
                                    <div class="param-example">
                                        <span v-if="param.example" class="example-tag">{{ param.example }}</span>
                                        <span v-else class="no-example-tag">无</span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <h3>请求协议头</h3>
                    <div class="code-wrapper">
                        <button class="copy-btn" @click="copyCode(api?.apiInfo?.auth)">复制</button>
                        <pre><code>{{ api?.apiInfo?.auth }}</code></pre>
                    </div>

                    <h3>响应示例</h3>
                    <div class="code-wrapper">
                        <button class="copy-btn" @click="copyCode(api?.apiInfo?.response)">复制</button>
                        <pre><code>{{ api?.apiInfo?.response }}</code></pre>
                    </div>

                    <h3>在线测试</h3>
                    <div class="api-test">
                        <div class="test-form">
                            <div v-for="(value, key) in testParams" :key="key" class="form-group">
                                <label>{{ key }}</label>
                                <input type="text" v-model="testParams[key]">
                            </div>
                        </div>
                        <div class="test-actions">
                            <LoadingButton class="test-btn" @click="testApi" :loading="testing">
                                发送请求
                            </LoadingButton>
                        </div>
                        <div v-if="testResult" class="test-result">
                            <h4>测试结果</h4>
                            <div class="code-wrapper">
                                <button class="copy-btn" @click="copyCode(JSON.stringify(testResult, null, 2))">复制</button>
                                <pre><code>{{ JSON.stringify(testResult, null, 2) }}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import LoadingButton from '../LoadingButton.vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
    api: {
        type: Object,
        required: true
    },
    testing: {
        type: Boolean,
        default: false
    },
    testResult: {
        type: Object,
        default: null
    },
    userInfo: {
        type: Object,
        default: null
    }
})

const emit = defineEmits(['close', 'copyCode', 'testApi'])

const testParams = ref({})

// 计算清理后的参数代码
const cleanedParamsCode = computed(() => {
    if (!props.api?.apiInfo?.params) return ''
    
    return props.api.apiInfo.params
        .replace(/\/\/.*$/gm, '')
        .replace(/\/\*[\s\S]*?\*\//g, '')
        .replace(/,(\s*[}\]])/g, '$1')
        .trim()
})

// 计算解析后的参数
const parsedParams = computed(() => {
    if (!props.api?.apiInfo?.params) return {}
    
    try {
        // 移除注释并解析JSON
        const cleanParams = props.api.apiInfo.params
            .replace(/\/\/.*$/gm, '') // 移除单行注释
            .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
            .replace(/,(\s*[}\]])/g, '$1') // 移除尾随逗号
            .trim()

        const params = JSON.parse(cleanParams)
        const result = {}

        // 解析每个参数
        Object.entries(params).forEach(([key, value]) => {
            // 从注释中提取类型和说明
            const comment = props.api.apiInfo.params.match(new RegExp(`"${key}":.*?//\\s*(.*?)$`, 'm'))
            const desc = comment ? comment[1] : ''

            // 提取示例值
            const exampleMatch = desc.match(/示例值:([^,\n}]*)/)
            const example = exampleMatch ? exampleMatch[1].trim() : ''

            // 判断是否必填
            const required = desc.includes('必填') || desc.includes('必传')

            // 提取类型
            let type = 'string'
            if (typeof value === 'number') type = 'number'
            else if (typeof value === 'boolean') type = 'boolean'
            else if (Array.isArray(value)) type = 'array'
            else if (typeof value === 'object') type = 'object'

            result[key] = {
                type,
                required,
                desc: desc.replace(/示例值:.*$/, '').replace(/必填|必传/, '').trim(),
                example: example || null
            }
        })

        return result
    } catch (e) {
        console.error('参数解析错误:', e)
        return {}
    }
})

// 监听API变更，初始化测试参数
watch(() => props.api, (newApi) => {
    if (newApi) {
        testParams.value = {}
        
        // 如果有结构化参数定义，优先使用
        if (newApi?.apiInfo?.paramsDef) {
            testParams.value = newApi.apiInfo.paramsDef.reduce((acc, param) => {
                acc[param.name] = param.example || ''
                return acc
            }, {})
        }
        // 否则使用传统的注释解析方式
        else if (newApi?.apiInfo?.params) {
            try {
                // 移除注释并解析JSON
                const cleanParams = newApi.apiInfo.params
                    .replace(/\/\/.*$/gm, '') // 移除单行注释
                    .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
                    .replace(/,(\s*[}\]])/g, '$1') // 移除尾随逗号
                    .trim()

                const params = JSON.parse(cleanParams)

                // 从注释中提取示例值
                const getExampleValue = (paramStr, key) => {
                    const match = paramStr.match(new RegExp(`"${key}".*?示例值:([^,\\n}]*)`, 'm'))
                    return match ? match[1].trim() : ''
                }

                testParams.value = Object.keys(params).reduce((acc, key) => {
                    acc[key] = getExampleValue(newApi.apiInfo.params, key)
                    return acc
                }, {})
            } catch (e) {
                console.error('参数解析错误:', e)
                testParams.value = {}
            }
        }
    }
}, { immediate: true })

const closeModal = () => {
    emit('close')
}

const copyCode = (text) => {
    emit('copyCode', text)
}

const testApi = () => {
    if (!props.userInfo?.token) {
        ElMessage.error('请先登录')
        return
    }

    // 过滤掉空字符串的参数
    const params = Object.entries(testParams.value).reduce((acc, [key, value]) => {
        if (value !== '') {
            acc[key] = value
        }
        return acc
    }, {})

    emit('testApi', params)
}
</script> 