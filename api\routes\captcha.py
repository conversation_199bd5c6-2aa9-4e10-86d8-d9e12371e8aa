import sqlalchemy
from fastapi import APIRouter, HTTPException, Request, Depends
import asyncio
import time
import logging

logger = logging.getLogger(__name__)

try:
    from api.db import engine, with_db_retry, rate_limit
    from api.models import users, Captcha3Request, Captcha4Request
    from api.auth import get_current_user
    from api.utils.common import get_client_ip

    from api.utils.billing import record_consumption
except:
    from db import engine, with_db_retry, rate_limit
    from models import users, Captcha3Request, Captcha4Request
    from auth import get_current_user
    from utils.common import get_client_ip

    from utils.billing import record_consumption

# 导入验证码处理项目
try:
    # 尝试使用相对导入（Vercel环境）
    from ..projects import __third_step4, __third_step
    from ..projects.geetest3_fullpage import geetest3_fullpage
    from ..projects.geetest3_slide import geetest3_slide
    from ..projects.geetest4_fullpage import geetest4_fullpage
    from ..projects.geetest4_slide import geetest4_slide
except:
    # 本地环境使用绝对导入
    from projects import __third_step4, __third_step
    from projects.geetest3_fullpage import geetest3_fullpage
    from projects.geetest3_slide import geetest3_slide
    from projects.geetest4_fullpage import geetest4_fullpage
    from projects.geetest4_slide import geetest4_slide

router = APIRouter(prefix="/api/v1/captcha", tags=["captcha"])


@router.post("/geetest3")
def process_captcha3_api(
    request: Captcha3Request,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    client_ip = get_client_ip(req) if req else "unknown"

    with rate_limit(f"captcha:{client_ip}", max_requests=10, expire=5):

        @with_db_retry(max_retries=3)
        def do_process_captcha():
            with engine.connect() as db:
                with db.begin():
                    # 检查余额
                    price = 100  # 验证码处理费用
                    if current_user["balance"] < price:
                        raise HTTPException(status_code=400, detail="余额不足")

                    try:
                        # 调用验证码处理函数，只传递需要的参数
                        status, result = __third_step(
                            request.gt, request.challenge, request.proxy
                        )
                        if status == "sense":
                            if result:
                                # 扣费
                                new_balance = current_user["balance"] - price
                                stmt = (
                                    users.update()
                                    .where(users.c.username == current_user["username"])
                                    .values(balance=new_balance)
                                )
                                update_result = db.execute(stmt)
                                if update_result.rowcount == 0:
                                    raise sqlalchemy.exc.OperationalError(
                                        "更新失败，请重试"
                                    )

                                # 记录消费
                                try:
                                    # 使用异步函数但同步调用，避免阻塞主流程
                                    asyncio.create_task(
                                        record_consumption(
                                            username=current_user["username"],
                                            details=f"处理极验3验证码",
                                            amount=price,
                                        )
                                    )
                                except Exception as e:
                                    print(f"记录消费失败: {str(e)}")
                                    # 继续执行，不因记录失败而中断流程

                                return {
                                    "code": 200,
                                    "status": True,
                                    "data": result,
                                    "balance": new_balance,
                                    "message": "验证码处理成功",
                                }
                            else:
                                result = geetest3_fullpage(
                                    request.gt, request.challenge, request.proxy
                                )
                        elif status == "error":
                            return {
                                "code": 200,
                                "status": False,
                                "message": f"获取验证码类型错误:{result}",
                                "balance": current_user["balance"],
                            }
                        elif status == "success" or result == "slide":
                            # 多次尝试滑动验证码识别
                            slide_result = None
                            for slide_attempt in range(3):
                                try:
                                    slide_result = geetest3_slide(
                                        request.gt, request.challenge, request.proxy
                                    )
                                    logger.info(
                                        f"滑动验证码尝试 {slide_attempt + 1}: {slide_result}"
                                    )

                                    # 检查识别结果
                                    if (
                                        slide_result
                                        and isinstance(slide_result, dict)
                                        and slide_result.get("success") == 1
                                    ):
                                        result = slide_result
                                        break

                                    if slide_attempt < 2:
                                        logger.warning(
                                            f"滑动验证码识别失败，重试 {slide_attempt + 1}/3"
                                        )
                                        time.sleep(1)

                                except Exception as e:
                                    logger.error(
                                        f"滑动验证码识别异常 (尝试 {slide_attempt + 1}/3): {str(e)}"
                                    )
                                    if slide_attempt < 2:
                                        time.sleep(1)

                            if (
                                not slide_result
                                or not isinstance(slide_result, dict)
                                or slide_result.get("success") != 1
                            ):
                                return {
                                    "code": 200,
                                    "status": False,
                                    "message": "验证码识别出错",
                                    "balance": current_user["balance"],
                                }
                            result = slide_result
                        else:
                            return {
                                "code": 200,
                                "status": False,
                                "message": "验证码类型错误",
                                "balance": current_user["balance"],
                            }
                        if not result or result == "失败":
                            return {
                                "code": 200,
                                "status": False,
                                "message": "验证码处理失败",
                                "balance": current_user["balance"],
                            }

                        # 扣费
                        new_balance = current_user["balance"] - price
                        stmt = (
                            users.update()
                            .where(users.c.username == current_user["username"])
                            .values(balance=new_balance)
                        )
                        update_result = db.execute(stmt)
                        if update_result.rowcount == 0:
                            raise sqlalchemy.exc.OperationalError("更新失败，请重试")

                        # 记录消费
                        try:
                            # 使用异步函数但同步调用，避免阻塞主流程
                            asyncio.create_task(
                                record_consumption(
                                    username=current_user["username"],
                                    details=f"处理极验3验证码",
                                    amount=price,
                                )
                            )
                        except Exception as e:
                            print(f"记录消费失败: {str(e)}")
                            # 继续执行，不因记录失败而中断流程

                        return {
                            "code": 200,
                            "status": True,
                            "data": result["validate"],
                            "balance": new_balance,
                            "message": "验证码处理成功",
                        }
                    except Exception as e:
                        print(f"验证码处理出错: {str(e)}")
                        raise HTTPException(
                            status_code=500, detail=f"验证码处理出错: {str(e)}"
                        )

        return do_process_captcha()


@router.post("/geetest4")
def process_captcha4_api(
    request: Captcha4Request,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    client_ip = get_client_ip(req) if req else "unknown"

    with rate_limit(f"captcha4:{client_ip}", max_requests=1, expire=5):

        @with_db_retry(max_retries=3)
        def do_process_captcha():
            with engine.connect() as db:
                with db.begin():
                    # 检查余额
                    price = 100  # 验证码处理费用
                    if current_user["balance"] < price:
                        raise HTTPException(status_code=400, detail="余额不足")

                    try:
                        # 调用验证码处理函数，只传递需要的参数
                        status, result = __third_step4(request.captcha_id)
                        if status == "success":
                            if result == "ai":
                                result = geetest4_fullpage(request.captcha_id)
                            elif result == "slide":
                                result = geetest4_slide(request.captcha_id)
                        elif status == "error":
                            return {
                                "code": 200,
                                "status": False,
                                "message": f"获取验证码类型错误:{result}",
                                "balance": current_user["balance"],
                            }
                        else:
                            return {
                                "code": 200,
                                "status": False,
                                "message": "验证码类型错误",
                                "balance": current_user["balance"],
                            }

                        if not result or result == "失败":
                            print(result)
                            return {
                                "code": 200,
                                "status": False,
                                "message": "验证码处理失败",
                                "balance": current_user["balance"],
                            }

                        # 扣费
                        new_balance = current_user["balance"] - price
                        stmt = (
                            users.update()
                            .where(users.c.username == current_user["username"])
                            .values(balance=new_balance)
                        )
                        update_result = db.execute(stmt)
                        if update_result.rowcount == 0:
                            raise sqlalchemy.exc.OperationalError(
                                "更新余额失败，请重试"
                            )

                        # 记录消费
                        try:
                            # 使用异步函数但同步调用，避免阻塞主流程
                            asyncio.create_task(
                                record_consumption(
                                    username=current_user["username"],
                                    details=f"处理极验4验证码",
                                    amount=price,
                                )
                            )
                        except Exception as e:
                            print(f"记录消费失败: {str(e)}")
                            # 继续执行，不因记录失败而中断流程

                        return {
                            "code": 200,
                            "status": True,
                            "data": result["data"],
                            "balance": new_balance,
                            "message": "验证码处理成功",
                        }
                    except Exception as e:
                        print(f"验证码处理出错: {str(e)}")
                        raise HTTPException(
                            status_code=500, detail=f"验证码处理出错: {str(e)}"
                        )

        return do_process_captcha()
