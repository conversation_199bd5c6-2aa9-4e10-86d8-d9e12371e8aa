<template>
    <div class="theme-toggle" @click="toggle">
        <Sunny style="height: 1.5em;" v-if="isDarkTheme" />
        <Moon style="height: 1.5em;" v-else />
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import Sunny from './icons/Sunny.vue'
import Moon from './icons/Moon.vue'

const store = useStore()

// 获取当前主题
const isDarkTheme = computed(() => {
    return store.getters.currentTheme === 'dark'
})

// 切换主题
const toggle = () => {
    const currentTheme = store.getters.currentTheme
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark'
    store.dispatch('setTheme', newTheme)

    // 立即更新 HTML 元素类，确保背景色立即更新
    if (newTheme === 'dark') {
        document.documentElement.classList.add('dark-theme')
    } else {
        document.documentElement.classList.remove('dark-theme')
    }
}
</script> 