<template>
    <div class="modal" @click="closeModal">
        <div class="modal-content consumption-modal" @click.stop :class="{ 'dark-theme': isDarkTheme }">
            <div class="modal-header">
                <h2>消费记录</h2>
                <button class="modal-close" @click="closeModal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <!-- 搜索和筛选区域 -->
                <div class="search-section">
                    <div class="search-group">
                        <label>搜索消费详情</label>
                        <input 
                            type="text" 
                            v-model="searchQuery" 
                            placeholder="输入关键词搜索消费记录..."
                            @input="filterRecords"
                        >
                    </div>
                    <div class="filter-group">
                        <label>消费类型</label>
                        <select v-model="selectedType" @change="filterRecords">
                            <option value="">全部类型</option>
                            <option value="AI对话">AI对话</option>
                            <option value="邮箱">邮箱相关</option>
                            <option value="二维码">二维码生成</option>
                            <option value="验证码">验证码识别</option>
                            <option value="充值">充值记录</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                </div>

                <!-- 记录列表 -->
                <div class="records-container">
                    <div v-if="loading" class="loading-state">
                        <div class="loading-spinner"></div>
                        <span>加载中...</span>
                    </div>
                    <div v-else-if="filteredRecords.length === 0" class="no-records">
                        <div class="empty-icon">📝</div>
                        <p>{{ searchQuery || selectedType ? '没有找到匹配的消费记录' : '暂无消费记录' }}</p>
                    </div>
                    <div v-else class="records-list">
                        <div class="record-header">
                            <span class="record-details">消费详情</span>
                            <span class="record-amount">金额</span>
                            <span class="record-time">时间</span>
                        </div>
                        <div 
                            v-for="(record, index) in paginatedRecords" 
                            :key="index" 
                            class="record-item"
                            :class="{ 'positive-amount': record.amount < 0 }"
                        >
                            <span class="record-details">{{ record.details }}</span>
                            <span class="record-amount">
                                {{ record.amount < 0 ? '+' : '-' }}{{ Math.abs(record.amount) }}
                            </span>
                            <span class="record-time">{{ formatDate(record.created_at) }}</span>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <div v-if="totalPages > 1" class="pagination">
                    <button 
                        class="page-btn" 
                        :disabled="currentPage === 1"
                        @click="currentPage = 1"
                    >
                        首页
                    </button>
                    <button 
                        class="page-btn" 
                        :disabled="currentPage === 1"
                        @click="currentPage--"
                    >
                        上一页
                    </button>
                    <span class="page-info">
                        第 {{ currentPage }} 页，共 {{ totalPages }} 页
                    </span>
                    <button 
                        class="page-btn" 
                        :disabled="currentPage === totalPages"
                        @click="currentPage++"
                    >
                        下一页
                    </button>
                    <button 
                        class="page-btn" 
                        :disabled="currentPage === totalPages"
                        @click="currentPage = totalPages"
                    >
                        末页
                    </button>
                </div>

                <!-- 统计信息 -->
                <div class="statistics">
                    <div class="stat-item">
                        <span class="stat-label">总记录数:</span>
                        <span class="stat-value">{{ filteredRecords.length }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总消费:</span>
                        <span class="stat-value consumption">{{ totalConsumption.toFixed(2) }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总充值:</span>
                        <span class="stat-value recharge">{{ totalRecharge.toFixed(2) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useStore } from 'vuex'

const store = useStore()

// 获取当前主题
const isDarkTheme = computed(() => {
    return store.getters.currentTheme === 'dark'
})

const props = defineProps({
    records: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['close'])

// 搜索和筛选
const searchQuery = ref('')
const selectedType = ref('')

// 分页
const currentPage = ref(1)
const pageSize = 10

// 过滤后的记录
const filteredRecords = ref([])

// 格式化日期
const formatDate = (dateStr) => {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    })
}

// 根据消费详情判断类型
const getRecordType = (details) => {
    if (details.includes('AI对话') || details.includes('chat')) return 'AI对话'
    if (details.includes('邮箱') || details.includes('email')) return '邮箱'
    if (details.includes('二维码') || details.includes('qrcode')) return '二维码'
    if (details.includes('验证码') || details.includes('captcha')) return '验证码'
    if (details.includes('充值') || details.includes('卡密')) return '充值'
    return '其他'
}

// 过滤记录
const filterRecords = () => {
    let filtered = [...props.records]
    
    // 按搜索关键词过滤
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(record => 
            record.details.toLowerCase().includes(query)
        )
    }
    
    // 按类型过滤
    if (selectedType.value) {
        filtered = filtered.filter(record => 
            getRecordType(record.details) === selectedType.value
        )
    }
    
    filteredRecords.value = filtered
    currentPage.value = 1 // 重置到第一页
}

// 分页计算
const totalPages = computed(() => Math.ceil(filteredRecords.value.length / pageSize))

const paginatedRecords = computed(() => {
    const start = (currentPage.value - 1) * pageSize
    const end = start + pageSize
    return filteredRecords.value.slice(start, end)
})

// 统计计算
const totalConsumption = computed(() => {
    return filteredRecords.value
        .filter(record => record.amount > 0)
        .reduce((sum, record) => sum + record.amount, 0)
})

const totalRecharge = computed(() => {
    return Math.abs(filteredRecords.value
        .filter(record => record.amount < 0)
        .reduce((sum, record) => sum + record.amount, 0))
})

// 关闭模态框
const closeModal = () => {
    emit('close')
}

// 监听记录变化，重新过滤
watch(() => props.records, () => {
    filterRecords()
}, { immediate: true })

onMounted(() => {
    filterRecords()
})
</script>

<style scoped>
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: modalFadeIn 0.3s ease-out;
}

.modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25);
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
    border: 2px solid #e5e7eb;
}

.modal-content.dark-theme {
    background: #1f2937;
    border-color: #374151;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    background: white;
    border-radius: 16px 16px 0 0;
}

.modal-content.dark-theme .modal-header {
    background: #1f2937;
    border-bottom-color: #374151;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
}

.modal-content.dark-theme .modal-header h2 {
    color: #f9fafb;
}

.modal-close {
    background: #f3f4f6;
    border: none;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
}

.modal-close:hover {
    background: #ef4444;
    color: white;
    transform: translateY(-1px);
}

.modal-content.dark-theme .modal-close {
    background: #374151;
    color: #9ca3af;
}

.modal-content.dark-theme .modal-close:hover {
    background: #ef4444;
    color: white;
}

.modal-body {
    padding: 2rem;
    background: white;
    border-radius: 0 0 16px 16px;
}

.modal-content.dark-theme .modal-body {
    background: #1f2937;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}
.consumption-modal {
    max-width: 800px;
    max-height: 90vh;
}

.search-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.modal-content.dark-theme .search-section {
    background: #1e293b;
    border-color: #334155;
}

.search-group,
.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.search-group label,
.filter-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.modal-content.dark-theme .search-group label,
.modal-content.dark-theme .filter-group label {
    color: #d1d5db;
}

.search-group input,
.filter-group select {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.modal-content.dark-theme .search-group input,
.modal-content.dark-theme .filter-group select {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

.search-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.records-container {
    min-height: 300px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #ffffff;
}

.modal-content.dark-theme .records-container {
    background: #111827;
    border-color: #374151;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    gap: 1rem;
    color: #6b7280;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-records {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6b7280;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.records-list {
    padding: 1rem;
}

.record-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1.2fr;
    gap: 1rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 6px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.modal-content.dark-theme .record-header {
    background: #1e293b;
    color: #d1d5db;
}

.record-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1.2fr;
    gap: 1rem;
    padding: 0.75rem;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s ease;
}

.record-item:hover {
    background: #f8fafc;
}

.modal-content.dark-theme .record-item {
    border-bottom-color: #334155;
}

.modal-content.dark-theme .record-item:hover {
    background: #1e293b;
}

.record-item.positive-amount .record-amount {
    color: #10b981;
}

.record-amount {
    color: #ef4444;
    font-weight: 600;
}

.record-time {
    color: #6b7280;
    font-size: 0.85rem;
}

.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin: 1.5rem 0;
}

.page-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #374151;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
    background: #f3f4f6;
    border-color: #6366f1;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.modal-content.dark-theme .page-btn {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
}

.modal-content.dark-theme .page-btn:hover:not(:disabled) {
    background: #4b5563;
}

.page-info {
    margin: 0 1rem;
    color: #6b7280;
    font-size: 0.9rem;
}

.statistics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.modal-content.dark-theme .statistics {
    background: #1e293b;
    border-color: #334155;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
}

.modal-content.dark-theme .stat-value {
    color: #d1d5db;
}

.stat-value.consumption {
    color: #ef4444;
}

.stat-value.recharge {
    color: #10b981;
}

@media (max-width: 768px) {
    .search-section {
        grid-template-columns: 1fr;
    }
    
    .record-header,
    .record-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .statistics {
        grid-template-columns: 1fr;
    }
}
</style>
