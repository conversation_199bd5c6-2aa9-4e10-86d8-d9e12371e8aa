"""
邮件内容缓存模块
用于缓存邮件详细内容，避免重复获取相同邮件
"""

import hashlib
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Tuple
import sqlalchemy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.models import temp_html_storage
    from api.db import get_db, with_db_retry
except:
    from models import temp_html_storage
    from db import get_db, with_db_retry


class EmailContentCache:
    """邮件内容缓存管理器"""

    # 内容缓存有效期（30分钟）
    CONTENT_CACHE_MINUTES = 30

    @staticmethod
    def _generate_content_key(family_id: str, username: str) -> str:
        """
        生成内容缓存键

        Args:
            family_id: 邮件家族ID
            username: 用户名

        Returns:
            缓存键
        """
        combined = f"{family_id}:{username}"
        return hashlib.md5(combined.encode()).hexdigest()

    @staticmethod
    @with_db_retry(max_retries=2)
    def get_cached_content(family_id: str, username: str) -> Optional[Dict]:
        """
        获取缓存的邮件内容

        Args:
            family_id: 邮件家族ID
            username: 用户名

        Returns:
            缓存的邮件内容，如果没有有效缓存则返回None
        """
        try:
            content_key = EmailContentCache._generate_content_key(family_id, username)
            current_time = datetime.now(timezone.utc)
            cache_expiry = current_time - timedelta(
                minutes=EmailContentCache.CONTENT_CACHE_MINUTES
            )

            with get_db() as db:
                # 查询有效的缓存记录
                stmt = sqlalchemy.select(temp_html_storage).where(
                    sqlalchemy.and_(
                        temp_html_storage.c.email_id == content_key,
                        temp_html_storage.c.created_at > int(cache_expiry.timestamp()),
                    )
                )

                result = db.execute(stmt).fetchone()

                if result:
                    logger.info(f"邮件内容缓存命中: {family_id[:8]}...")

                    # 解析HTML内容，提取邮件信息
                    html_content = result.html_content

                    # 简单解析，提取主要信息
                    try:
                        from bs4 import BeautifulSoup

                        soup = BeautifulSoup(html_content, "html.parser")

                        # 提取文本内容
                        text = soup.get_text(separator=" ", strip=True)

                        return {
                            "Subject": "缓存邮件",  # 这里可以进一步优化存储更多信息
                            "Content": text[:1000] if text else "",  # 限制长度
                            "From": "",
                            "cached_content": True,
                        }
                    except Exception as e:
                        logger.warning(f"解析缓存内容失败: {str(e)}")
                        return None

                return None

        except Exception as e:
            logger.error(f"获取邮件内容缓存时发生错误: {str(e)}")
            return None

    @staticmethod
    @with_db_retry(max_retries=2)
    def save_content_cache(
        family_id: str, username: str, content: Dict, html_content: str = None
    ) -> bool:
        """
        保存邮件内容缓存

        Args:
            family_id: 邮件家族ID
            username: 用户名
            content: 邮件内容字典
            html_content: HTML内容

        Returns:
            是否保存成功
        """
        try:
            content_key = EmailContentCache._generate_content_key(family_id, username)
            current_time = int(datetime.now(timezone.utc).timestamp())
            expires_at = current_time + (EmailContentCache.CONTENT_CACHE_MINUTES * 60)

            # 如果没有提供HTML内容，使用文本内容
            if not html_content:
                html_content = content.get("Content", "")

            # 生成内容哈希
            content_hash = hashlib.md5(html_content.encode()).hexdigest()

            with get_db() as db:
                # 检查是否已存在
                existing_stmt = sqlalchemy.select(temp_html_storage).where(
                    temp_html_storage.c.email_id == content_key
                )
                existing_result = db.execute(existing_stmt).fetchone()

                if existing_result:
                    # 更新现有记录
                    update_stmt = (
                        temp_html_storage.update()
                        .where(temp_html_storage.c.email_id == content_key)
                        .values(
                            html_content=html_content,
                            content_hash=content_hash,
                            created_at=current_time,
                            expires_at=expires_at,
                            username=username,
                        )
                    )
                    db.execute(update_stmt)
                else:
                    # 插入新记录
                    import uuid

                    insert_stmt = temp_html_storage.insert().values(
                        id=str(uuid.uuid4()),
                        email_id=content_key,
                        username=username,
                        content_hash=content_hash,
                        html_content=html_content,
                        created_at=current_time,
                        expires_at=expires_at,
                    )
                    db.execute(insert_stmt)

                db.commit()
                logger.info(f"邮件内容缓存保存成功: {family_id[:8]}...")
                return True

        except Exception as e:
            logger.error(f"保存邮件内容缓存时发生错误: {str(e)}")
            return False
