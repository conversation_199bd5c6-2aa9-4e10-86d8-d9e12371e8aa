<template>
    <div class="app" :class="{ 'dark-theme': isDarkTheme }">
        <!-- 添加背景浮动光点 -->
        <div class="floating-light"></div>
        <div class="floating-light"></div>
        <div class="floating-light"></div>
        <div class="floating-light"></div>

        <div class="header">
            <h1 class="site-title">可达鸭API</h1>
            <div class="user-section">
                <div class="theme-toggle" @click="toggleTheme">
                    <Sunny style="height: 1.5em;" v-if="isDarkTheme" />
                    <Moon style="height: 1.5em;" v-else />
                </div>
                <template v-if="!userInfo">
                    <LoadingButton class="auth-btn" @click="openAuthModal('login')">登录</LoadingButton>
                    <LoadingButton class="auth-btn" @click="openAuthModal('register')">注册</LoadingButton>
                </template>
                <template v-else>
                    <div class="user-info" @click="showUserModal">
                        <User style="height: 1.5em;" :color="isDarkTheme ? '#818cf8' : '#4f46e5'" />
                        <span class="balance">余额: {{ userInfo.balance }}</span>
                    </div>
                </template>
            </div>
        </div>

        <div class="cards-container">
            <!-- 加载中状态 -->
            <div v-if="apiLoading" class="loading-container">
                <div class="loading-animation"></div>
                <p>加载API数据中...</p>
            </div>

            <!-- 加载错误状态 -->
            <div v-else-if="apiLoadError" class="error-container">
                <div class="error-icon">❌</div>
                <p>加载API数据失败</p>
                <LoadingButton class="retry-btn" @click="loadApiData">重试</LoadingButton>
            </div>

            <!-- API列表 -->
            <div v-else v-for="(api, index) in apis" :key="index" class="card" @click="handleCardClick(api, index)">
                <el-popover :ref="`popover-${index}`" placement="bottom" :width="300"
                    :trigger="isMobile ? 'manual' : 'hover'" popper-class="api-description-popover"
                    :visible="isMobile ? activePopover === index : undefined" :hide-after="isMobile ? 0 : 200"
                    :show-after="isMobile ? 0 : 100">
                    <template #default>
                        <div class="popover-content">
                            <div class="popover-icon">{{ api.icon }}</div>
                            <div class="popover-text">{{ api.description }}</div>
                            <div class="popover-cost" v-if="extractCost(api.description) !== '未知'">
                                <span class="cost-label">消费点数:</span>
                                <span class="cost-value">{{ extractCost(api.description) }}</span>
                            </div>
                            <!-- 移动端关闭按钮 -->
                            <div v-if="isMobile" class="popover-close-btn" @click.stop="closePopover">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </div>
                        </div>
                    </template>
                    <template #reference>
                        <div class="card-content">
                            <h3>{{ api.title }}</h3>
                            <div class="icon">{{ api.icon }}</div>
                            <div class="cost-badge" v-if="extractCost(api.description) !== '未知'">
                                消费点数: <span>{{ extractCost(api.description) }}</span>
                            </div>
                        </div>
                    </template>
                </el-popover>
            </div>
        </div>

        <!-- 用户认证模态框 -->
        <div class="modal" v-if="authModal" @click="closeAuthModal">
            <div class="modal-content auth-modal" @click.stop>
                <div class="modal-header">
                    <h2>{{ authModal === 'login' ? '登录' : '注册' }}</h2>
                    <button class="modal-close" @click="closeAuthModal">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="auth-form" @submit.prevent="handleAuth">
                        <div class="form-group">
                            <label>邮箱</label>
                            <input type="email" v-model="authForm.email" required placeholder="请输入QQ邮箱">
                        </div>
                        <div class="form-group">
                            <label>密码</label>
                            <input type="password" v-model="authForm.password" required>
                        </div>

                        <!-- 添加验证码输入框和发送验证码按钮 -->
                        <div class="form-group verification-code-group" v-if="authModal === 'register'">
                            <label>验证码</label>
                            <div class="verification-code-input">
                                <input type="text" v-model="authForm.verificationCode" required placeholder="请输入验证码">
                                <LoadingButton class="send-code-btn" :loading="loading.sendCode"
                                    :disabled="!authForm.email || codeCooldown > 0 || loading.sendCode"
                                    @click.prevent="sendVerificationCode">
                                    {{ codeCooldown > 0 ? `${codeCooldown}秒后重新发送` : '发送验证码' }}
                                </LoadingButton>
                            </div>
                        </div>
                        <div class="form-group" v-if="authModal === 'register'">
                            <label>邀请码</label>
                            <input type="text" v-model="authForm.inviteCode" placeholder="选填，填写邀请人邮箱">
                        </div>
                        <!-- 添加 Turnstile 组件 -->
                        <div class="turnstile-container">
                            <div id="turnstile-widget"></div>
                        </div>
                        <LoadingButton class="submit-btn" :loading="loading[authModal]" type="submit">
                            {{ authModal === 'login' ? '登录' : '注册' }}
                        </LoadingButton>

                        <!-- 忘记密码链接 -->
                        <div v-if="authModal === 'login'" class="forgot-password-link">
                            <a href="#" @click.prevent="openForgotPasswordModal">忘记密码？</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 用户信息模态框 -->
        <div class="modal" v-if="showingUserInfo" @click="closeUserModal">
            <div class="modal-content user-modal" @click.stop>
                <div class="modal-header">
                    <h2>用户信息</h2>
                    <button class="modal-close" @click="closeUserModal">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="user-details">
                        <p>
                            <strong>邮箱</strong>
                            <span>{{ userInfo?.email }}</span>
                        </p>
                        <p>
                            <strong>余额</strong>
                            <span>{{ userInfo?.balance }}</span>
                        </p>
                        <p>
                            <strong>邀请人</strong>
                            <span>{{ userInfo?.inviter || '无' }}</span>
                        </p>
                        <p>
                            <strong>邀请收益</strong>
                            <span>{{ userInfo?.invite_reward || 0 }}</span>
                        </p>
                        <div>
                            <LoadingButton class="consumption-records-btn" @click="openConsumptionRecordsModal"
                                style="margin-left: 10px;">
                                查看消费记录
                            </LoadingButton>
                        </div>
                        <div>
                            <strong>Token</strong>
                            <pre class="token-container" @click="copyToken"><code>{{ userInfo?.token }}</code></pre>
                        </div>
                        <div class="button-group">
                            <LoadingButton class="reset-token-btn" @click="handleResetToken"
                                :loading="loading.resetToken">
                                重置 Token
                            </LoadingButton>
                            <LoadingButton class="change-password-btn" @click="openChangePasswordModal">
                                修改密码
                            </LoadingButton>
                            <LoadingButton class="logout-btn" @click="handleLogout" :loading="loading.logout">
                                退出登录
                            </LoadingButton>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模态框 -->
        <div class="modal" v-if="activeModal !== null" @click="closeModal">
            <div class="modal-content" @click.stop>
                <div class="modal-header">
                    <h2>{{ apis[activeModal]?.title }}</h2>
                    <button class="modal-close" @click="closeModal">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="api-docs">
                        <h3>请求接口</h3>
                        <div class="code-wrapper">
                            <button class="copy-btn" @click="copyCode(apis[activeModal]?.apiInfo?.endpoint)">复制</button>
                            <pre><code><span class="method-tag">{{ apis[activeModal]?.apiInfo?.method }}</span> {{
                                apis[activeModal]?.apiInfo?.endpoint }}</code></pre>
                        </div>

                        <h3>请求参数</h3>
                        <div class="code-wrapper">
                            <button class="copy-btn"
                                @click="copyCode(apis[activeModal]?.apiInfo?.params.replace(/\/\/.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, '').replace(/,(\s*[}\]])/g, '$1').trim())">复制</button>
                            <pre><code>{{ apis[activeModal]?.apiInfo?.params.replace(/\/\/.*$/gm,
                                '').replace(/\/\*[\s\S]*?\*\//g, '').replace(/,(\s*[}\]])/g, '$1').trim() }}</code></pre>
                        </div>

                        <div class="params-table">
                            <div class="params-header">
                                <div class="param-name">参数名</div>
                                <div class="param-type">类型</div>
                                <div class="param-required">必填</div>
                                <div class="param-desc">说明</div>
                                <div class="param-example">示例值</div>
                            </div>
                            <div class="params-body">
                                <template v-if="activeModal !== null">
                                    <!-- 如果有结构化参数定义，优先使用 -->
                                    <template v-if="apis[activeModal]?.apiInfo?.paramsDef">
                                        <div v-for="param in apis[activeModal].apiInfo.paramsDef" :key="param.name"
                                            class="param-row">
                                            <div class="param-name">{{ param.name }}</div>
                                            <div class="param-type">{{ param.type }}</div>
                                            <div class="param-required">
                                                <span :class="['required-tag', { required: param.required }]">
                                                    {{ param.required ? '是' : '否' }}
                                                </span>
                                            </div>
                                            <div class="param-desc">{{ param.desc }}</div>
                                            <div class="param-example">
                                                <span v-if="param.example" class="example-tag">{{ param.example
                                                    }}</span>
                                                <span v-else class="no-example-tag">无</span>
                                            </div>
                                        </div>
                                    </template>
                                    <!-- 否则使用传统的注释解析方式 -->
                                    <template v-else>
                                        <div v-for="(param, name) in parseParams(apis[activeModal]?.apiInfo?.params)"
                                            :key="name" class="param-row">
                                            <div class="param-name">{{ name }}</div>
                                            <div class="param-type">{{ param.type }}</div>
                                            <div class="param-required">
                                                <span :class="['required-tag', { required: param.required }]">
                                                    {{ param.required ? '是' : '否' }}
                                                </span>
                                            </div>
                                            <div class="param-desc">{{ param.desc }}</div>
                                            <div class="param-example">
                                                <span v-if="param.example" class="example-tag">{{ param.example
                                                    }}</span>
                                                <span v-else class="no-example-tag">无</span>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                            </div>
                        </div>

                        <h3>请求协议头</h3>
                        <div class="code-wrapper">
                            <button class="copy-btn" @click="copyCode(apis[activeModal]?.apiInfo?.auth)">复制</button>
                            <pre><code>{{ apis[activeModal]?.apiInfo?.auth }}</code></pre>
                        </div>

                        <h3>响应示例</h3>
                        <div class="code-wrapper">
                            <button class="copy-btn" @click="copyCode(apis[activeModal]?.apiInfo?.response)">复制</button>
                            <pre><code>{{ apis[activeModal]?.apiInfo?.response }}</code></pre>
                        </div>

                        <h3>在线测试</h3>
                        <div class="api-test">
                            <div class="test-form">
                                <div v-for="(param, key) in testParams" :key="key" class="form-group">
                                    <label>{{ key }}</label><input type="text" v-model="testParams[key]">
                                </div>
                            </div>
                            <div class="test-actions">
                                <LoadingButton class="test-btn" @click="testApi" :loading="testing">
                                    发送请求
                                </LoadingButton>
                            </div>
                            <div v-if="testResult" class="test-result">
                                <h4>测试结果</h4>
                                <div class="code-wrapper">
                                    <button class="copy-btn"
                                        @click="copyCode(JSON.stringify(testResult, null, 2))">复制</button>
                                    <pre><code>{{ JSON.stringify(testResult, null, 2) }}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 卡密充值模态框 -->
        <div class="modal" v-if="showCardModal" @click="closeCardModal">
            <div class="modal-content auth-modal" @click.stop>
                <div class="modal-header">
                    <h2>卡密充值</h2>
                    <button class="modal-close" @click="closeCardModal">
                        <!-- 关闭按钮SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="auth-form" @submit.prevent="handleCardRecharge">
                        <div class="form-group">
                            <label>卡密</label>
                            <input type="text" v-model="cardKeyInput" required placeholder="请输入卡密">
                        </div>
                        <LoadingButton class="submit-btn" :loading="loading.card" type="submit">充值</LoadingButton>
                    </form>
                </div>
            </div>
        </div>

        <!-- 忘记密码模态框 -->
        <div class="modal" v-if="forgotPasswordModal" @click="closeForgotPasswordModal">
            <div class="modal-content auth-modal" @click.stop>
                <div class="modal-header">
                    <h2>{{ forgotPasswordStep === 1 ? '忘记密码' : '重置密码' }}</h2>
                    <button class="modal-close" @click="closeForgotPasswordModal">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 第一步：输入邮箱发送验证码 -->
                    <form v-if="forgotPasswordStep === 1" class="auth-form" @submit.prevent="sendForgotPasswordCode">
                        <div class="form-group">
                            <label>邮箱</label>
                            <input type="email" v-model="forgotPasswordForm.email" required placeholder="请输入注册时的QQ邮箱">
                        </div>
                        <div class="turnstile-container">
                            <div id="turnstile-widget-forgot"></div>
                        </div>
                        <LoadingButton class="submit-btn" :loading="loading.forgotPassword" type="submit">
                            发送验证码
                        </LoadingButton>
                    </form>

                    <!-- 第二步：输入验证码和新密码 -->
                    <form v-else class="auth-form" @submit.prevent="resetPassword">
                        <div class="form-group">
                            <label>邮箱</label>
                            <input type="email" v-model="forgotPasswordForm.email" disabled>
                        </div>
                        <div class="form-group">
                            <label>验证码</label>
                            <input type="text" v-model="forgotPasswordForm.verificationCode" required
                                placeholder="请输入邮箱收到的验证码">
                        </div>
                        <div class="form-group">
                            <label>新密码</label>
                            <input type="password" v-model="forgotPasswordForm.newPassword" required
                                placeholder="请输入新密码">
                        </div>
                        <div class="turnstile-container">
                            <div id="turnstile-widget-reset"></div>
                        </div>
                        <LoadingButton class="submit-btn" :loading="loading.resetPassword" type="submit">
                            重置密码
                        </LoadingButton>
                        <div class="back-link">
                            <a href="#" @click.prevent="backToForgotPasswordStep1">返回上一步</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 修改密码模态框 -->
        <div class="modal" v-if="changePasswordModal" @click="closeChangePasswordModal">
            <div class="modal-content auth-modal" @click.stop>
                <div class="modal-header">
                    <h2>修改密码</h2>
                    <button class="modal-close" @click="closeChangePasswordModal">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="auth-form" @submit.prevent="handleChangePassword">
                        <div class="form-group">
                            <label>当前密码</label>
                            <input type="password" v-model="changePasswordForm.currentPassword" required
                                placeholder="请输入当前密码">
                        </div>
                        <div class="form-group">
                            <label>新密码</label>
                            <input type="password" v-model="changePasswordForm.newPassword" required
                                placeholder="请输入新密码（至少6位）">
                        </div>
                        <div class="form-group">
                            <label>确认新密码</label>
                            <input type="password" v-model="changePasswordForm.confirmPassword" required
                                placeholder="请再次输入新密码">
                        </div>
                        <div class="turnstile-container">
                            <div id="turnstile-widget-change-password"></div>
                        </div>
                        <LoadingButton class="submit-btn" :loading="loading.changePassword" type="submit">
                            修改密码
                        </LoadingButton>
                    </form>
                </div>
            </div>
        </div>

        <!-- 消费记录模态框 -->
        <ConsumptionRecordsModal v-if="showConsumptionRecordsModal" :records="consumptionRecords"
            :loading="loading.consumptionRecords" @close="closeConsumptionRecordsModal" />
    </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
import LoadingButton from './components/LoadingButton.vue'
import ConsumptionRecordsModal from './components/modals/ConsumptionRecordsModal.vue'
import { useStore } from 'vuex'
import { ElMessage, ElPopover, ElMessageBox } from 'element-plus'
// 导入API数据
import { defineAsyncComponent } from 'vue'
// 移除静态导入：
// import apiDataRaw from '@/api/apiData.js'
// import { generateAuthHeader } from '@/api/apiData.js'

const activeModal = ref(null)
const authModal = ref(null)
const showingUserInfo = ref(false)
const userInfo = ref(null)
const authForm = ref({
    email: '',
    password: '',
    inviteCode: '',
    verificationCode: ''
})
const testing = ref(false)
const testResult = ref(null)
const testParams = ref({})
const isInitializing = ref(true)
const store = useStore()

// 移动端 popover 控制
const isMobile = ref(false)
const activePopover = ref(null)
let popoverTimer = null

// 获取当前主题
const isDarkTheme = computed(() => {
    return store.getters.currentTheme === 'dark'
})

// 切换主题
const toggleTheme = () => {
    const currentTheme = store.getters.currentTheme
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark'
    store.dispatch('setTheme', newTheme)

    // 立即更新 HTML 元素类，确保背景色立即更新
    if (newTheme === 'dark') {
        document.documentElement.classList.add('dark-theme')
    } else {
        document.documentElement.classList.remove('dark-theme')
    }
}

// 监听系统主题变化
const handleSystemThemeChange = (e) => {
    if (store.state.theme === 'system') {
        // 触发重新计算主题
        store.dispatch('setTheme', 'system')
    }
}

// 检测是否为移动设备
const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768 || 'ontouchstart' in window
}

// 处理卡片点击事件
const handleCardClick = (api, index) => {
    if (isMobile.value) {
        // 移动端：先处理 popover 显示/隐藏
        if (activePopover.value === index) {
            // 如果当前 popover 已经显示，则关闭它并执行卡片功能
            closePopover()
            executeCardFunction(api, index)
        } else {
            // 显示 popover
            showPopover(index)
        }
    } else {
        // 桌面端：直接执行卡片功能
        executeCardFunction(api, index)
    }
}

// 执行卡片的实际功能
const executeCardFunction = (api, index) => {
    if (api.title.includes('订阅链接')) {
        copySubscriptionUrl(api)
    } else if (api.title === '卡密购买') {
        openPurchasePage(api)
    } else if (api.title === '卡密充值') {
        openRechargeModal()
    } else {
        openModal(index)
    }
}

// 显示 popover
const showPopover = (index) => {
    // 关闭其他 popover
    closePopover()

    activePopover.value = index

    // 设置自动关闭定时器（3秒后自动关闭）
    if (popoverTimer) {
        clearTimeout(popoverTimer)
    }
    popoverTimer = setTimeout(() => {
        closePopover()
    }, 3000)
}

// 关闭 popover
const closePopover = () => {
    activePopover.value = null
    if (popoverTimer) {
        clearTimeout(popoverTimer)
        popoverTimer = null
    }
}

// 添加按钮加载状态
const loading = ref({
    login: false,
    register: false,
    resetToken: false,
    logout: false,
    verifyEmail: false,
    card: false,
    sendCode: false,
    consumptionRecords: false,
    forgotPassword: false,
    resetPassword: false,
    changePassword: false
})

// 忘记密码相关状态
const forgotPasswordModal = ref(false)
const forgotPasswordStep = ref(1) // 1: 发送验证码, 2: 重置密码
const forgotPasswordForm = ref({
    email: '',
    verificationCode: '',
    newPassword: ''
})
const forgotPasswordTurnstileToken = ref('')
const resetPasswordTurnstileToken = ref('')

// 修改密码相关状态
const changePasswordModal = ref(false)
const changePasswordForm = ref({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
})
const changePasswordTurnstileToken = ref('')

// Turnstile 相关
const turnstileLoaded = ref(false)
const turnstileToken = ref('')

// 将apis初始化为空数组
const apis = ref([])

// API加载状态
const apiLoading = ref(true)
const apiLoadError = ref(false)

// 动态加载API数据
const loadApiData = async () => {
    apiLoading.value = true
    apiLoadError.value = false

    try {
        // 动态导入API数据
        const apiModule = await import('@/api/apiData.js')
        const apiDataRaw = apiModule.default
        const generateAuthHeader = apiModule.generateAuthHeader

        // 更新apis数据
        apis.value = apiDataRaw.map(api => ({
            ...api,
            apiInfo: {
                ...api.apiInfo,
                auth: computed(() => generateAuthHeader(userInfo.value))
            }
        }))
        apiLoading.value = false
    } catch (error) {
        console.error('加载API数据失败:', error)
        ElMessage.error(`加载API数据失败: ${error.message}`)
        apiLoading.value = false
        apiLoadError.value = true
    }
}

onMounted(() => {
    // 动态加载API数据
    loadApiData()

    // 初始化主题
    store.dispatch('initTheme')

    // 监听系统主题变化
    if (typeof window !== 'undefined' && window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)')
            .addEventListener('change', handleSystemThemeChange)
    }

    // 初始化移动端检测
    checkMobile()

    // 监听窗口大小变化
    window.addEventListener('resize', checkMobile)

    // 监听点击事件，用于关闭移动端 popover
    document.addEventListener('click', (e) => {
        if (isMobile.value && activePopover.value !== null) {
            // 检查点击是否在 popover 或卡片内部
            const popoverElement = document.querySelector('.api-description-popover')
            const cardElement = e.target.closest('.card')

            if (!popoverElement?.contains(e.target) && !cardElement) {
                closePopover()
            }
        }
    })

    // 从localStorage读取用户信息
    try {
        const storedUserInfo = getFromStorage('userInfo')
        if (storedUserInfo) {
            userInfo.value = storedUserInfo
            // 获取最新的用户信息
            getCurrentUser()
        }
    } catch (error) {
        console.error('Error loading user info from localStorage:', error)
    }
})

onUnmounted(() => {
    // 移除监听器
    if (typeof window !== 'undefined' && window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)')
            .removeEventListener('change', handleSystemThemeChange)
    }
})

const openModal = (index) => {
    // 关闭所有可能打开的popover
    if (typeof document !== 'undefined') {
        document.querySelectorAll('.el-popover').forEach(el => {
            el.style.display = 'none';
        });
    }
    activeModal.value = index
    document.body.style.overflow = 'hidden'
}

const closeModal = () => {
    activeModal.value = null
    document.body.style.overflow = 'auto'
}

const openAuthModal = (type) => {
    // 关闭所有可能打开的popover
    if (typeof document !== 'undefined') {
        document.querySelectorAll('.el-popover').forEach(el => {
            el.style.display = 'none';
        });
    }
    authModal.value = type
    document.body.style.overflow = 'hidden'
}

const closeAuthModal = () => {
    authModal.value = null
    document.body.style.overflow = 'auto'
    authForm.value = { email: '', password: '', inviteCode: '', verificationCode: '' }
}

// 显示用户模态框
const showUserModal = () => {
    if (!userInfo.value) {
        openAuthModal('login');
        return;
    }

    // 关闭所有可能打开的popover
    if (typeof document !== 'undefined') {
        document.querySelectorAll('.el-popover').forEach(el => {
            el.style.display = 'none';
        });
    }

    // 获取用户消费记录
    fetchConsumptionRecords();

    showingUserInfo.value = true;
    document.body.style.overflow = 'hidden';
}

const closeUserModal = () => {
    showingUserInfo.value = false
    document.body.style.overflow = 'auto'
}

// 加载 Turnstile 脚本
const loadTurnstile = () => {
    if (window.turnstile) {
        turnstileLoaded.value = true
        return
    }

    const script = document.createElement('script')
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js'
    script.async = true
    script.defer = true
    script.onload = () => {
        turnstileLoaded.value = true
        initTurnstile()
    }
    document.head.appendChild(script)
}

// 重新渲染 Turnstile 组件
const resetTurnstile = () => {
    if (!window.turnstile) return

    const widget = document.getElementById('turnstile-widget')
    if (widget) {
        // 清空容器内容
        widget.innerHTML = ''
        // 重置token
        turnstileToken.value = ''
    }
}

// 初始化 Turnstile
const initTurnstile = () => {
    if (!window.turnstile) return

    // 先重置现有实例
    resetTurnstile()

    // 根据当前主题设置Turnstile主题
    const turnstileTheme = isDarkTheme.value ? 'dark' : 'light'

    try {
        window.turnstile.render('#turnstile-widget', {
            sitekey: '0x4AAAAAABAsD1ldDuzN75My',
            theme: turnstileTheme,
            callback: function (token) {
                turnstileToken.value = token
            },
        })
    } catch (error) {
        console.error('Turnstile 初始化失败:', error)
        // 如果初始化失败，稍后重试
        setTimeout(() => {
            initTurnstile()
        }, 500)
    }
}

// 监听模态框的打开，重新加载 Turnstile
watch(authModal, (newVal) => {
    if (newVal) {
        if (!turnstileLoaded.value) {
            loadTurnstile()
        } else {
            // 重置 Turnstile
            setTimeout(() => {
                initTurnstile()
            }, 100)
        }
    }
})

// 监听忘记密码模态框的打开
watch(forgotPasswordModal, (newVal) => {
    if (newVal) {
        if (!turnstileLoaded.value) {
            loadTurnstile()
        } else {
            setTimeout(() => {
                initForgotPasswordTurnstile()
            }, 100)
        }
    }
})

// 监听修改密码模态框的打开
watch(changePasswordModal, (newVal) => {
    if (newVal) {
        if (!turnstileLoaded.value) {
            loadTurnstile()
        } else {
            setTimeout(() => {
                initChangePasswordTurnstile()
            }, 100)
        }
    }
})

// 监听主题变化，重新渲染 Turnstile
watch(isDarkTheme, () => {
    if (authModal.value && turnstileLoaded.value) {
        // 重新初始化 Turnstile
        setTimeout(() => {
            initTurnstile()
        }, 100)
    }
})

// 添加安全的localStorage访问方法
const getFromStorage = (key) => {
    try {
        if (typeof window !== 'undefined' && window.localStorage) {
            const value = window.localStorage.getItem(key)
            return value !== null ? JSON.parse(value) : null
        }
        return null
    } catch (e) {
        console.error('Error reading from localStorage', e)
        return null
    }
}

const saveToStorage = (key, value) => {
    try {
        if (typeof window !== 'undefined' && window.localStorage) {
            window.localStorage.setItem(key, JSON.stringify(value))
        }
    } catch (e) {
        console.error('Error writing to localStorage', e)
    }
}

const removeFromStorage = (key) => {
    try {
        if (typeof window !== 'undefined' && window.localStorage) {
            window.localStorage.removeItem(key)
        }
    } catch (e) {
        console.error('Error removing from localStorage', e)
    }
}

// 忘记密码相关函数
const openForgotPasswordModal = () => {
    closeAuthModal()
    forgotPasswordModal.value = true
    forgotPasswordStep.value = 1
    forgotPasswordForm.value = { email: '', verificationCode: '', newPassword: '' }
    document.body.style.overflow = 'hidden'
}

const closeForgotPasswordModal = () => {
    forgotPasswordModal.value = false
    forgotPasswordStep.value = 1
    forgotPasswordForm.value = { email: '', verificationCode: '', newPassword: '' }
    forgotPasswordTurnstileToken.value = ''
    resetPasswordTurnstileToken.value = ''
    document.body.style.overflow = 'auto'
}

const backToForgotPasswordStep1 = () => {
    forgotPasswordStep.value = 1
    forgotPasswordForm.value.verificationCode = ''
    forgotPasswordForm.value.newPassword = ''
    resetPasswordTurnstileToken.value = ''
    setTimeout(() => {
        initForgotPasswordTurnstile()
    }, 100)
}

// 初始化忘记密码的Turnstile
const initForgotPasswordTurnstile = () => {
    if (!window.turnstile) return

    const turnstileTheme = isDarkTheme.value ? 'dark' : 'light'

    // 清空容器
    const forgotWidget = document.getElementById('turnstile-widget-forgot')
    if (forgotWidget) {
        forgotWidget.innerHTML = ''
        forgotPasswordTurnstileToken.value = ''
    }

    try {
        if (forgotPasswordStep.value === 1) {
            window.turnstile.render('#turnstile-widget-forgot', {
                sitekey: '0x4AAAAAABAsD1ldDuzN75My',
                theme: turnstileTheme,
                callback: function (token) {
                    forgotPasswordTurnstileToken.value = token
                },
            })
        } else {
            const resetWidget = document.getElementById('turnstile-widget-reset')
            if (resetWidget) {
                resetWidget.innerHTML = ''
                resetPasswordTurnstileToken.value = ''
            }
            window.turnstile.render('#turnstile-widget-reset', {
                sitekey: '0x4AAAAAABAsD1ldDuzN75My',
                theme: turnstileTheme,
                callback: function (token) {
                    resetPasswordTurnstileToken.value = token
                },
            })
        }
    } catch (error) {
        console.error('Turnstile 初始化失败:', error)
    }
}

// 发送忘记密码验证码
const sendForgotPasswordCode = async () => {
    loading.value.forgotPassword = true

    if (!forgotPasswordForm.value.email.toLowerCase().includes('qq.com')) {
        ElMessage.error('只支持QQ邮箱')
        loading.value.forgotPassword = false
        return
    }

    if (!forgotPasswordTurnstileToken.value) {
        ElMessage.error('请完成人机验证')
        loading.value.forgotPassword = false
        return
    }

    try {
        const response = await fetch('/api/forgot-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: forgotPasswordForm.value.email,
                turnstileToken: forgotPasswordTurnstileToken.value
            })
        })

        const data = await response.json()

        if (response.ok) {
            ElMessage.success(data.message)
            forgotPasswordStep.value = 2
            setTimeout(() => {
                initForgotPasswordTurnstile()
            }, 100)
        } else {
            ElMessage.error(data.detail || '发送验证码失败')
        }
    } catch (error) {
        console.error('发送验证码失败:', error)
        ElMessage.error('发送验证码失败，请稍后重试')
    } finally {
        loading.value.forgotPassword = false
    }
}

// 重置密码
const resetPassword = async () => {
    loading.value.resetPassword = true

    if (!resetPasswordTurnstileToken.value) {
        ElMessage.error('请完成人机验证')
        loading.value.resetPassword = false
        return
    }

    if (forgotPasswordForm.value.newPassword.length < 6) {
        ElMessage.error('新密码长度不能少于6位')
        loading.value.resetPassword = false
        return
    }

    try {
        const response = await fetch('/api/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: forgotPasswordForm.value.email,
                verificationCode: forgotPasswordForm.value.verificationCode,
                newPassword: forgotPasswordForm.value.newPassword,
                turnstileToken: resetPasswordTurnstileToken.value
            })
        })

        const data = await response.json()

        if (response.ok) {
            ElMessage.success(data.message)
            closeForgotPasswordModal()
            // 自动打开登录模态框
            setTimeout(() => {
                openAuthModal('login')
                authForm.value.email = forgotPasswordForm.value.email
            }, 500)
        } else {
            ElMessage.error(data.detail || '重置密码失败')
        }
    } catch (error) {
        console.error('重置密码失败:', error)
        ElMessage.error('重置密码失败，请稍后重试')
    } finally {
        loading.value.resetPassword = false
    }
}

// 修改密码相关函数
const openChangePasswordModal = () => {
    closeUserModal()
    changePasswordModal.value = true
    changePasswordForm.value = { currentPassword: '', newPassword: '', confirmPassword: '' }
    changePasswordTurnstileToken.value = ''
    document.body.style.overflow = 'hidden'

    // 初始化Turnstile
    setTimeout(() => {
        initChangePasswordTurnstile()
    }, 100)
}

const closeChangePasswordModal = () => {
    changePasswordModal.value = false
    changePasswordForm.value = { currentPassword: '', newPassword: '', confirmPassword: '' }
    changePasswordTurnstileToken.value = ''
    document.body.style.overflow = 'auto'
}

// 初始化修改密码的Turnstile
const initChangePasswordTurnstile = () => {
    if (!window.turnstile) return

    const turnstileTheme = isDarkTheme.value ? 'dark' : 'light'

    // 清空容器
    const changePasswordWidget = document.getElementById('turnstile-widget-change-password')
    if (changePasswordWidget) {
        changePasswordWidget.innerHTML = ''
        changePasswordTurnstileToken.value = ''
    }

    try {
        window.turnstile.render('#turnstile-widget-change-password', {
            sitekey: '0x4AAAAAABAsD1ldDuzN75My',
            theme: turnstileTheme,
            callback: function (token) {
                changePasswordTurnstileToken.value = token
            },
        })
    } catch (error) {
        console.error('Turnstile 初始化失败:', error)
    }
}

// 处理修改密码
const handleChangePassword = async () => {
    loading.value.changePassword = true

    // 验证表单
    if (changePasswordForm.value.newPassword.length < 6) {
        ElMessage.error('新密码长度不能少于6位')
        loading.value.changePassword = false
        return
    }

    if (changePasswordForm.value.newPassword !== changePasswordForm.value.confirmPassword) {
        ElMessage.error('两次输入的新密码不一致')
        loading.value.changePassword = false
        return
    }

    if (changePasswordForm.value.currentPassword === changePasswordForm.value.newPassword) {
        ElMessage.error('新密码不能与当前密码相同')
        loading.value.changePassword = false
        return
    }

    if (!changePasswordTurnstileToken.value) {
        ElMessage.error('请完成人机验证')
        loading.value.changePassword = false
        return
    }

    try {
        const response = await fetch('/api/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userInfo.value?.token}`
            },
            body: JSON.stringify({
                currentPassword: changePasswordForm.value.currentPassword,
                newPassword: changePasswordForm.value.newPassword,
                turnstileToken: changePasswordTurnstileToken.value
            })
        })

        const data = await response.json()

        if (response.ok) {
            ElMessage.success(data.message || '密码修改成功')
            closeChangePasswordModal()
            // 可选：提示用户重新登录
            ElMessage.info('密码已修改，建议重新登录以确保安全')
        } else {
            ElMessage.error(data.detail || '密码修改失败')
        }
    } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error('修改密码失败，请稍后重试')
    } finally {
        loading.value.changePassword = false
    }
}

// 修改handleAuth函数中的localStorage代码
const handleAuth = async () => {
    const type = authModal.value
    loading.value[type] = true

    // 添加QQ邮箱验证
    if (!authForm.value.email.toLowerCase().includes('qq.com')) {
        ElMessage.error('只支持QQ邮箱注册')
        loading.value[type] = false
        return
    }

    if (!turnstileToken.value) {
        ElMessage.error('请完成人机验证')
        loading.value[type] = false
        return
    }

    // 验证注册时是否填写验证码
    if (type === 'register' && !authForm.value.verificationCode) {
        ElMessage.error('请输入验证码')
        loading.value[type] = false
        return
    }

    try {
        const endpoint = type === 'login' ? '/api/login' : '/api/register'

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ...authForm.value,
                turnstileToken: turnstileToken.value
            })
        })

        const data = await response.json()
        if (response.ok) {
            userInfo.value = data
            saveToStorage('userInfo', data)
            ElMessage.success(data.message || (type === 'login' ? '登录成功' : '注册成功'))
            closeAuthModal()
        } else {
            ElMessage.error(data.detail || (type === 'login' ? '登录失败' : '注册失败'))
        }
    } catch (error) {
        console.error('Error:', error)
        ElMessage.error('网络错误，请稍后重试')
    } finally {
        loading.value[type] = false
        // 重置 Turnstile
        turnstileToken.value = ''
        if (window.turnstile) {
            window.turnstile.reset('#turnstile-widget')
        }
    }
}

// 修改handleLogout函数中的localStorage代码
const handleLogout = async () => {
    loading.value.logout = true
    try {
        userInfo.value = null
        removeFromStorage('userInfo')
        closeUserModal()
        ElMessage.success('已退出登录')
    } finally {
        loading.value.logout = false
    }
}

// 修改handleResetToken函数中的localStorage代码
const handleResetToken = async () => {
    loading.value.resetToken = true
    try {
        const response = await fetch('/api/reset-token', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${userInfo.value.token}`
            }
        });

        const data = await response.json();
        if (response.ok) {
            userInfo.value.token = data.token;
            saveToStorage('userInfo', userInfo.value);
            ElMessage.success('Token 重置成功');
        } else {
            ElMessage.error(data.detail || 'Token 重置失败');
        }
    } catch (error) {
        console.error('Error:', error);
        ElMessage.error('网络错误，请稍后重试');
    } finally {
        loading.value.resetToken = false
    }
}

// 修改getCurrentUser函数中的localStorage代码
const getCurrentUser = async () => {
    try {
        const response = await fetch('/api/user', {
            headers: {
                'Authorization': `Bearer ${userInfo.value?.token}`
            }
        });
        if (response.ok) {
            const data = await response.json();
            userInfo.value = { ...userInfo.value, ...data };
            saveToStorage('userInfo', userInfo.value);
        } else {
            userInfo.value = null;
            const errorData = await response.json();
            ElMessage.error(errorData.detail || '获取用户信息失败');
        }
    } catch (error) {
        userInfo.value = null;
        console.error('Error:', error);
        ElMessage.error('网络错误，请稍后重试');
    }
}

const copyToken = async () => {
    if (userInfo.value?.token) {
        try {
            // 优先使用现代的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(userInfo.value.token)
                ElMessage.success('Token 已复制到剪贴板')
            } else {
                // 降级到传统的复制方法（适用于不支持 Clipboard API 的环境）
                fallbackCopyTextToClipboard(userInfo.value.token, 'Token')
            }
        } catch (err) {
            console.error('复制Token失败:', err)
            // 如果现代API失败，尝试降级方法
            fallbackCopyTextToClipboard(userInfo.value.token, 'Token')
        }
    }
}

// 降级复制方法（适用于不支持 Clipboard API 的环境，如某些手机浏览器）
const fallbackCopyTextToClipboard = (text, type = '内容', shouldOpenClash = false) => {
    try {
        // 创建一个临时的 textarea 元素
        const textArea = document.createElement('textarea')
        textArea.value = text

        // 设置样式使其不可见
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        textArea.style.opacity = '0'
        textArea.style.pointerEvents = 'none'
        textArea.setAttribute('readonly', '')

        document.body.appendChild(textArea)

        // 选择文本
        textArea.focus()
        textArea.select()
        textArea.setSelectionRange(0, 99999) // 对于移动设备

        // 尝试复制
        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (successful) {
            ElMessage.success(`${type} 已复制到剪贴板`)

            // 如果是订阅地址复制成功，自动打开 Clash 配置链接
            if (shouldOpenClash) {
                openClashConfig(text)
            }
        } else {
            // 如果复制失败，显示手动复制提示
            showManualCopyDialog(text, type)
        }
    } catch (err) {
        console.error('降级复制方法失败:', err)
        // 最后的降级方案：显示手动复制对话框
        showManualCopyDialog(text, type)
    }
}

// 显示手动复制对话框
const showManualCopyDialog = (text, type = '内容') => {
    // 创建一个包含可选择文本的HTML内容
    const htmlContent = `
        <div style="margin: 10px 0;">
            <p style="margin-bottom: 10px; color: #666;">由于浏览器限制，无法自动复制。请手动复制以下${type}：</p>
            <div style="
                background: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 10px;
                word-break: break-all;
                user-select: text;
                font-family: monospace;
                font-size: 12px;
                max-height: 200px;
                overflow-y: auto;
            ">${text}</div>
            <p style="margin-top: 10px; font-size: 12px; color: #999;">
                提示：长按或双击上方文本框可选择全部内容
            </p>
        </div>
    `;

    ElMessageBox.alert(htmlContent, '手动复制', {
        confirmButtonText: '我已复制',
        type: 'warning',
        customClass: 'manual-copy-dialog',
        dangerouslyUseHTMLString: true,
        beforeClose: (action, instance, done) => {
            done()
        }
    }).then(() => {
        ElMessage.info('请确保已正确复制内容')
    }).catch(() => {
        // 用户取消了对话框
    })

    // 同时在控制台输出，方便开发者调试
    console.log(`${type}内容:`, text)
}

// 添加复制代码函数
const copyCode = async (text) => {
    if (text) {
        try {
            // 优先使用现代的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text)
                ElMessage.success('代码已复制到剪贴板')
            } else {
                // 降级到传统的复制方法
                fallbackCopyTextToClipboard(text, '代码')
            }
        } catch (err) {
            console.error('复制代码失败:', err)
            // 如果现代API失败，尝试降级方法
            fallbackCopyTextToClipboard(text, '代码')
        }
    }
}

// 解析参数函数
const parseParams = (paramsStr) => {
    if (!paramsStr) return {}
    try {
        // 移除注释并解析JSON
        const cleanParams = paramsStr
            .replace(/\/\/.*$/gm, '') // 移除单行注释
            .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
            .replace(/,(\s*[}\]])/g, '$1') // 移除尾随逗号
            .trim()

        const params = JSON.parse(cleanParams)
        const result = {}

        // 解析每个参数
        Object.entries(params).forEach(([key, value]) => {
            // 从注释中提取类型和说明
            const comment = paramsStr.match(new RegExp(`"${key}":.*?//\\s*(.*?)$`, 'm'))
            const desc = comment ? comment[1] : ''

            // 提取示例值
            const exampleMatch = desc.match(/示例值:([^,\n}]*)/)
            const example = exampleMatch ? exampleMatch[1].trim() : ''

            // 判断是否必填
            const required = desc.includes('必填') || desc.includes('必传')

            // 提取类型
            let type = 'string'
            if (typeof value === 'number') type = 'number'
            else if (typeof value === 'boolean') type = 'boolean'
            else if (Array.isArray(value)) type = 'array'
            else if (typeof value === 'object') type = 'object'

            // 如果是可选参数且没有示例值，不设置示例值
            result[key] = {
                type,
                required,
                desc: desc.replace(/示例值:.*$/, '').replace(/必填|必传/, '').trim(),
                example: example || null
            }
        })

        return result
    } catch (e) {
        console.error('参数解析错误:', e)
        return {}
    }
}

// 监听模态框打开，初始化测试参数
watch(activeModal, (newVal) => {
    if (newVal !== null) {
        const api = apis.value[newVal]
        testParams.value = {}

        // 如果有结构化参数定义，优先使用
        if (api?.apiInfo?.paramsDef) {
            testParams.value = api.apiInfo.paramsDef.reduce((acc, param) => {
                acc[param.name] = param.example || ''
                return acc
            }, {})
        }
        // 否则使用传统的注释解析方式
        else if (api?.apiInfo?.params) {
            try {
                // 移除注释并解析JSON
                const cleanParams = api.apiInfo.params
                    .replace(/\/\/.*$/gm, '') // 移除单行注释
                    .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
                    .replace(/,(\s*[}\]])/g, '$1') // 移除尾随逗号
                    .trim()

                const params = JSON.parse(cleanParams)

                // 从注释中提取示例值
                const getExampleValue = (paramStr, key) => {
                    const match = paramStr.match(new RegExp(`"${key}".*?示例值:([^,\\n}]*)`, 'm'))
                    return match ? match[1].trim() : ''
                }

                testParams.value = Object.keys(params).reduce((acc, key) => {
                    acc[key] = getExampleValue(api.apiInfo.params, key)
                    return acc
                }, {})
            } catch (e) {
                console.error('参数解析错误:', e)
                testParams.value = {}
            }
        }
        testResult.value = null
    }
})

// 测试API
const testApi = async () => {
    if (!userInfo.value?.token) {
        ElMessage.error('请先登录')
        return
    }

    testing.value = true
    testResult.value = null

    try {
        const api = apis.value[activeModal.value]
        // 过滤掉空字符串的参数
        const params = Object.entries(testParams.value).reduce((acc, [key, value]) => {
            if (value !== '') {
                acc[key] = value
            }
            return acc
        }, {})

        const response = await fetch(api.apiInfo.endpoint, {
            method: api.apiInfo.method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userInfo.value.token}`
            },
            body: JSON.stringify(params)
        })

        const data = await response.json()
        testResult.value = data

        if (response.ok) {
            ElMessage.success('请求成功')
        } else {
            ElMessage.error(data.detail || '请求失败')
        }
    } catch (error) {
        console.error('Error:', error)
        ElMessage.error('网络错误，请稍后重试')
    } finally {
        testing.value = false
    }
}

const extractCost = (description) => {
    // 从描述中提取消费点数
    if (!description) return '未知';

    // 匹配模式如 "扣费1.0余额", "扣费150.0余额", "每次调用扣费200.0余额" 等
    const match = description.match(/(?:扣费|每次调用扣费)(\d+(?:\.\d+)?)余额/);
    return match ? match[1] : '未知';
}

// 验证邮箱函数已被移除 - 验证步骤现在在注册过程中通过验证码完成

const showCardModal = ref(false)
const cardKeyInput = ref('')
loading.value.card = false
const closeCardModal = () => {
    showCardModal.value = false
    cardKeyInput.value = ''
}
const handleCardRecharge = async () => {
    if (!cardKeyInput.value) return
    loading.value.card = true
    try {
        const response = await fetch('/api/recharge_card', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userInfo.value.token}`
            },
            body: JSON.stringify({ card_key: cardKeyInput.value })
        })
        const data = await response.json()
        if (response.ok) {
            ElMessage.success(data.message || '充值成功')
            closeCardModal()
            await getCurrentUser()
        } else {
            ElMessage.error(data.detail || '充值失败')
        }
    } catch (e) {
        ElMessage.error('网络错误，请稍后重试')
    } finally {
        loading.value.card = false
    }
}

// 打开 Clash 配置链接
const openClashConfig = (subscriptionUrl) => {
    try {
        // 构建 Clash 配置链接
        const clashUrl = `clash://install-config?url=${encodeURIComponent(subscriptionUrl)}`

        // 尝试打开 Clash 链接
        window.location.href = clashUrl

        // 显示提示信息
        ElMessage.info('正在尝试打开 Clash 客户端...')

        console.log('Clash 配置链接:', clashUrl)

        // 如果用户没有安装 Clash 或者链接无法打开，提供备用方案
        setTimeout(() => {
            ElMessageBox.confirm(
                '如果 Clash 客户端没有自动打开，请手动复制订阅地址到 Clash 中添加配置。',
                '提示',
                {
                    confirmButtonText: '我知道了',
                    cancelButtonText: '查看帮助',
                    type: 'info',
                }
            ).then(() => {
                // 用户确认知道了
            }).catch(() => {
                // 用户想查看帮助
                ElMessageBox.alert(
                    `
                    <div class="clash-help-content">
                        <h4>手动添加订阅的步骤：</h4>
                        <ol style="padding-left: 60px; margin-left: 0; list-style-type: decimal; list-style-position: outside;">
                            <li style="padding-left: 10px; margin-left: 0; display: list-item;">打开 Clash 客户端</li>
                            <li style="padding-left: 10px; margin-left: 0; display: list-item;">找到"配置"或"Profiles"选项</li>
                            <li style="padding-left: 10px; margin-left: 0; display: list-item;">点击"添加"或"+"按钮</li>
                            <li style="padding-left: 10px; margin-left: 0; display: list-item;">选择"从URL导入"</li>
                            <li style="padding-left: 10px; margin-left: 0; display: list-item;">粘贴已复制的订阅地址</li>
                            <li style="padding-left: 10px; margin-left: 0; display: list-item;">点击确认添加</li>
                        </ol>
                        <p style="color: #666; font-size: 12px; margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 6px; border-left: 3px solid #6366f1;">
                            订阅地址已经复制到剪贴板，可以直接粘贴使用。
                        </p>
                    </div>
                    `,
                    '使用帮助',
                    {
                        confirmButtonText: '明白了',
                        dangerouslyUseHTMLString: true,
                        customClass: 'clash-help-dialog'
                    }
                )
            })
        }, 2000) // 2秒后显示提示

    } catch (err) {
        console.error('打开 Clash 配置链接失败:', err)
        ElMessage.warning('无法自动打开 Clash 客户端，请手动添加订阅')
    }
}

const copySubscriptionUrl = async (api) => {
    // 如果用户未登录，提示登录
    if (!userInfo.value) {
        ElMessage.warning('请先登录');
        openAuthModal('login');
        return;
    }

    // 检查用户是否有有效的token
    if (!userInfo.value.token) {
        ElMessage.error('用户token无效，请重新登录');
        openAuthModal('login');
        return;
    }

    let subscriptionUrl = '';
    let urlType = '';

    // 检查是否是新的订阅链接格式（method为COPY）
    if (api.apiInfo.method === 'COPY') {
        // 动态替换endpoint中的TOKEN占位符为用户真实token
        // 使用正则表达式确保只替换token参数值，而不是URL中其他可能包含TOKEN的部分
        subscriptionUrl = api.apiInfo.endpoint.replace(/token=TOKEN/g, `token=${userInfo.value.token}`);
        urlType = '订阅地址';

        // 验证替换是否成功
        if (subscriptionUrl.includes('TOKEN')) {
            console.warn('订阅URL中仍包含TOKEN占位符，使用备用方法');
            // 备用方法：直接替换所有TOKEN
            subscriptionUrl = api.apiInfo.endpoint.replace(/TOKEN/g, userInfo.value.token);
        }
    } else {
        // 兼容旧的订阅链接格式，构建完整的订阅URL，替换token参数
        subscriptionUrl = `${api.apiInfo.endpoint}?token=${userInfo.value.token}`;
        urlType = '订阅链接';
    }

    // 记录生成的订阅URL（用于调试，生产环境中可以移除）
    console.log(`生成的${urlType}:`, subscriptionUrl);

    try {
        // 优先使用现代的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(subscriptionUrl);
            ElMessage.success(`${urlType}已复制到剪贴板`);
            console.log(`${urlType}复制成功`);

            // 复制成功后，自动打开 Clash 配置链接
            openClashConfig(subscriptionUrl);
        } else {
            // 降级到传统的复制方法
            fallbackCopyTextToClipboard(subscriptionUrl, urlType, true);
        }
    } catch (err) {
        console.error(`复制${urlType}失败:`, err);
        // 如果现代API失败，尝试降级方法
        try {
            fallbackCopyTextToClipboard(subscriptionUrl, urlType, true);
        } catch (fallbackErr) {
            console.error(`降级复制${urlType}也失败:`, fallbackErr);
            // 最终降级：显示详细的错误信息和手动复制选项
            ElMessageBox.confirm(
                `自动复制失败，可能是由于浏览器安全限制。是否查看${urlType}以便手动复制？`,
                '复制失败',
                {
                    confirmButtonText: '查看并手动复制',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            ).then(() => {
                showManualCopyDialog(subscriptionUrl, urlType);
            }).catch(() => {
                ElMessage.info('已取消复制操作');
            });
        }
    }
}

// 打开卡密购买页面
const openPurchasePage = (api) => {
    // 在新标签页中打开卡密购买网站
    window.open(api.apiInfo.endpoint, '_blank');
}

const openRechargeModal = () => {
    // 如果用户未登录，提示登录
    if (!userInfo.value) {
        ElMessage.warning('请先登录');
        openAuthModal('login');
        return;
    }

    // 打开卡密充值模态框
    showCardModal.value = true;
}

// 发送验证码
const sendVerificationCode = async () => {
    if (!authForm.value.email) {
        ElMessage.error('请输入邮箱');
        return;
    }

    if (!turnstileToken.value) {
        ElMessage.error('请完成人机验证');
        return;
    }

    loading.value.sendCode = true;
    try {
        const response = await fetch('/api/send-verification-code', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: authForm.value.email,
                turnstileToken: turnstileToken.value
            })
        });

        const data = await response.json();

        if (response.ok) {
            ElMessage.success(data.message || '验证码已发送，请查收邮件');

            // 开始倒计时
            codeCooldown.value = 60;
            const timer = setInterval(() => {
                codeCooldown.value--;
                if (codeCooldown.value <= 0) {
                    clearInterval(timer);
                }
            }, 1000);
        } else {
            ElMessage.error(data.detail || '发送验证码失败');
        }
    } catch (error) {
        console.error('Error:', error);
        ElMessage.error('网络错误，请稍后再试');
    } finally {
        loading.value.sendCode = false;
    }
}

// 冷却时间
const codeCooldown = ref(0);

const consumptionRecords = ref([])
const showConsumptionRecordsModal = ref(false)

const formatDate = (dateStr) => {
    const date = new Date(dateStr)
    return date.toLocaleString()
}

// 获取用户消费记录
const fetchConsumptionRecords = async () => {
    if (!userInfo.value?.token) return;

    loading.value.consumptionRecords = true;
    try {
        const response = await fetch('/api/consumption-records', {
            headers: {
                'Authorization': `Bearer ${userInfo.value.token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            consumptionRecords.value = data.records || [];
        } else {
            const errorData = await response.json();
            ElMessage.error(errorData.detail || '获取消费记录失败');
            consumptionRecords.value = [];
        }
    } catch (error) {
        console.error('Error:', error);
        ElMessage.error('网络错误，请稍后重试');
        consumptionRecords.value = [];
    } finally {
        loading.value.consumptionRecords = false;
    }
}

// 打开消费记录模态框
const openConsumptionRecordsModal = async () => {
    if (!userInfo.value) {
        ElMessage.warning('请先登录');
        openAuthModal('login');
        return;
    }

    // 获取最新的消费记录
    await fetchConsumptionRecords();

    showConsumptionRecordsModal.value = true;
    document.body.style.overflow = 'hidden';
}

// 关闭消费记录模态框
const closeConsumptionRecordsModal = () => {
    showConsumptionRecordsModal.value = false;
    document.body.style.overflow = 'auto';
}
</script>

<style lang="scss" scoped>
:root {
    --primary-color: #6366f1 !important;
    --primary-hover: #4f46e5 !important;
    --primary-light: #eef2ff;
    --primary-dark: #4338ca;
    --primary-active: #3730a3 !important;
    /* 添加主色调活动状态变量 */
    --text-color: #111827;
    --text-light: #374151;
    --text-muted: #6b7280;
    --bg-color: #f8f9fa;
    --bg-gradient: #f8f9fa;
    --card-bg: #ffffff;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.1);
    --modal-bg: #ffffff;
    --modal-overlay: rgba(0, 0, 0, 0.6);
    --tooltip-bg: #ffffff;
    --code-bg: #f9fafb;
    --code-color: #111827;
    --code-border: #e5e7eb;
    --auth-btn-bg: #6366f1;
    --auth-btn-hover: #4f46e5;
    --primary-active: #3730a3;
    /* 修改为深紫色，与按钮悬停色调一致 */
}

.dark-theme {
    --primary-color: #818cf8 !important;
    --primary-hover: #6366f1 !important;
    --primary-light: rgba(99, 102, 241, 0.2);
    --primary-dark: #c7d2fe;
    --primary-active: #4f46e5 !important;
    /* 添加暗色主题的活动状态变量 */
    --text-color: #f9fafb;
    --text-light: #e5e7eb;
    --text-muted: #9ca3af;
    --bg-color: #111827;
    --bg-gradient: #111827;
    --card-bg: #1f2937;
    --border-color: #374151;
    --shadow-color: rgba(0, 0, 0, 0.2);
    --shadow-hover: rgba(0, 0, 0, 0.3);
    --modal-bg: #1f2937;
    --modal-overlay: rgba(0, 0, 0, 0.75);
    --tooltip-bg: #1f2937;
    --code-bg: #111827;
    --code-color: #f9fafb;
    --code-border: #374151;
    --auth-btn-bg: #818cf8;
    --auth-btn-hover: #6366f1;
}

body {
    background: var(--bg-gradient);
    color: var(--text-color);
    transition: background 0.3s ease, color 0.3s ease;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin: 0;
    position: relative;
    overflow-x: hidden;
}

/* 添加背景呼吸光感效果 */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%,
            var(--primary-light) 0%,
            transparent 60%);
    opacity: 0.4;
    z-index: -1;
    animation: breathe 8s ease-in-out infinite;
}

/* 添加第二层呼吸光效 */
body::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%,
            var(--primary-light) 0%,
            transparent 65%),
        radial-gradient(circle at 70% 30%,
            var(--primary-color) 0%,
            transparent 65%);
    opacity: 0.2;
    z-index: -2;
    animation: breathe-secondary 12s ease-in-out infinite;
}

@keyframes breathe {

    0%,
    100% {
        opacity: 0.2;
        transform: scale(1);
    }

    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

@keyframes breathe-secondary {

    0%,
    100% {
        opacity: 0.1;
        transform: scale(1.1) rotate(0deg);
    }

    50% {
        opacity: 0.3;
        transform: scale(1.3) rotate(5deg);
    }
}

.dark-theme body::before {
    background: radial-gradient(circle at 50% 50%,
            var(--primary-light) 0%,
            transparent 70%);
    opacity: 0.15;
}

.dark-theme body::after {
    background:
        radial-gradient(circle at 30% 70%,
            var(--primary-light) 0%,
            transparent 70%),
        radial-gradient(circle at 70% 30%,
            var(--primary-dark) 0%,
            transparent 70%);
    opacity: 0.1;
}

/* 添加浮动光点效果 */
.floating-light {
    position: fixed;
    z-index: -3;
    border-radius: 50%;
    filter: blur(10px);
    opacity: 0.5;
    mix-blend-mode: lighten;
    background: var(--primary-light);
    animation: float 15s linear infinite;
}

.floating-light:nth-child(1) {
    width: 100px;
    height: 100px;
    top: 15%;
    left: 20%;
    animation-duration: 20s;
    animation-delay: -2s;
}

.floating-light:nth-child(2) {
    width: 150px;
    height: 150px;
    bottom: 25%;
    right: 15%;
    animation-duration: 25s;
    animation-delay: -5s;
}

.floating-light:nth-child(3) {
    width: 80px;
    height: 80px;
    bottom: 15%;
    left: 30%;
    animation-duration: 18s;
    animation-delay: -8s;
}

.floating-light:nth-child(4) {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 25%;
    animation-duration: 22s;
    animation-delay: -12s;
}

.dark-theme .floating-light {
    opacity: 0.2;
    background: var(--primary-dark);
}

@keyframes float {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }

    25% {
        transform: translate(15px, 20px) rotate(5deg);
    }

    50% {
        transform: translate(5px, -15px) rotate(10deg);
    }

    75% {
        transform: translate(-10px, 5px) rotate(5deg);
    }

    100% {
        transform: translate(0, 0) rotate(0deg);
    }
}

.app {
    max-width: 1200px;
    width: 100%;
    padding: 2rem;
    position: relative;
    z-index: 1;
    margin: 0 auto;
    /* 添加这行使容器居中 */
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 0 1rem;
    user-select: none;
    -webkit-user-select: none;

    .site-title {
        margin: 0;
        font-size: 2rem;
        color: var(--text-color);
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        line-height: 1.3;
        text-align: center;
        max-width: 100%;
        word-wrap: break-word;
        hyphens: auto;
    }
}

.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    cursor: pointer;
    margin-right: 1rem;
    background: #eef2ff;
    color: #4f46e5;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.25), 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(99, 102, 241, 0.2);

    .dark-theme & {
        background: rgba(99, 102, 241, 0.2);
        color: #818cf8;
        border: 1px solid rgba(99, 102, 241, 0.3);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
    }

    &:hover {
        transform: translateY(-3px) rotate(12deg);
        background: #4f46e5;
        color: white;
        box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4), 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
        transform: translateY(0) rotate(0);
    }
}

.user-section {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.auth-btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #4361EE 0%, #7209B7 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    -webkit-user-select: none;
    font-weight: 500;
    font-size: 0.95rem;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, #3a56df 0%, #6209a4 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
    }

    .dark-theme & {
        background: linear-gradient(135deg, #5e60ce 0%, #7400b8 100%);
        box-shadow: 0 4px 12px rgba(116, 0, 184, 0.4);

        &:hover:not(:disabled) {
            background: linear-gradient(135deg, #5051c0 0%, #6400a0 100%);
            box-shadow: 0 6px 16px rgba(116, 0, 184, 0.5);
        }
    }
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.7rem 1.3rem;
    background: #ffffff;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.03);
    border: 1px solid #e5e7eb;

    .dark-theme & {
        background: #1f2937;
        border: 1px solid #374151;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
    }

    &:hover {
        background: #f9fafb;
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2), 0 4px 10px rgba(0, 0, 0, 0.05);
        border-color: #6366f1;

        .dark-theme & {
            background: #111827;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            border-color: #6366f1;
        }
    }

    &:active {
        transform: translateY(-1px);
    }


    .balance {
        color: #111827;
        font-weight: 600;
        font-size: 0.95rem;

        .dark-theme & {
            color: #f9fafb;
        }
    }
}

.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    padding: 1rem;
    position: relative;
    user-select: none;
    -webkit-user-select: none;
    width: 100%;
    /* 确保卡片容器占满整个宽度 */
    margin: 0 auto;
    /* 居中卡片容器 */
}

.card {
    position: relative;
    background: var(--card-bg);
    backdrop-filter: none;
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    overflow: hidden;

    &:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        border-color: var(--primary-color);
        z-index: 2;
    }

    .dark-theme & {
        background: rgba(31, 41, 55, 0.9);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    }

    .dark-theme &:hover {
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.35);
    }

    &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
        transform: scale(0);
        opacity: 0;
        transition: transform 0.6s ease, opacity 0.6s ease;
    }

    &:hover::before {
        transform: scale(1);
        opacity: 1;
    }

    .el-popover__reference-wrapper {
        display: block;
        height: 100%;
        width: 100%;
    }
}

.card-content {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    z-index: 1;

    h3 {
        margin-bottom: 1.5rem;
        color: var(--text-color);
        font-size: 1.5rem;
        font-weight: 600;
        user-select: none;
        -webkit-user-select: none;
    }

    .icon {
        font-size: 3.5rem;
        margin-bottom: 1rem;
        transition: transform 0.5s ease;
        color: var(--primary-color);
    }

    .cost-badge {
        padding: 0.5rem 0.8rem;
        color: white;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;

        background: linear-gradient(135deg, #4361EE 0%, #7209B7 100%);
        box-shadow: 0 2px 8px rgba(99, 102, 241, 0.4);

        span {
            margin-left: 4px;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: 700;
        }

        .dark-theme & {
            background: linear-gradient(135deg, #5e60ce 0%, #7400b8 100%);
            box-shadow: 0 2px 10px rgba(116, 0, 184, 0.5);
        }
    }
}

.card:hover .icon {
    transform: scale(1.3) rotate(5deg);
    animation: pulse 1.2s infinite alternate;
}

@keyframes pulse {
    0% {
        transform: scale(1.1) rotate(-3deg);
    }

    100% {
        transform: scale(1.3) rotate(8deg);
    }
}

.modal {
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--modal-overlay);
    z-index: 100;
    align-items: center;
    justify-content: center;
    padding: 0;
    overflow: hidden;
    animation: modal-in 0.3s ease-out;
    backdrop-filter: blur(4px);
}

@keyframes modal-in {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-content {
    background-color: var(--modal-bg) !important;
    border-radius: 16px;
    width: 90%;
    max-width: 1280px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25);
    position: relative;
    animation: modal-content-in 0.3s ease-out;
    border: 1px solid rgba(229, 231, 235, 0.5);
    opacity: 1 !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    /* 增加亮色主题下的边框可见性 */
    border: 2px solid var(--border-color);
}

@keyframes modal-content-in {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    background: var(--modal-bg) !important;
    position: sticky;
    top: 0;
    z-index: 1;
    opacity: 1 !important;
}

.modal-title {
    font-size: 1.6rem;
    font-weight: 700;
    color: #4f46e5;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    letter-spacing: -0.5px;

    .dark-theme & {
        color: #818cf8;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
}

.modal-close {
    width: 40px;
    height: 40px;
    border: none;
    background-color: #f3f4f6;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

.modal-close:hover {
    background-color: #ef4444;
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
    transform: translateY(-1px);
}

.dark .modal-close {
    background-color: #374151;
    color: #9ca3af;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.05);
}

.dark .modal-close:hover {
    background-color: #ef4444;
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 80px);
    background: var(--modal-bg) !important;
    opacity: 1 !important;
    color: var(--text-color);
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
}

.modal-body::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
}

body::-webkit-scrollbar {
    display: none;
}

html,
body {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* 仅为 params-table 添加悬浮时显示的横向滚动条 */
.params-table::-webkit-scrollbar {
    width: 0;
    height: 6px;
    background: transparent;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.params-table::-webkit-scrollbar-thumb {
    background: rgba(99, 102, 241, 0.2);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.params-table::-webkit-scrollbar-track {
    background: transparent;
}

.params-table:hover::-webkit-scrollbar-thumb {
    background: rgba(99, 102, 241, 0.8);
}

.dark-theme .params-table::-webkit-scrollbar-thumb {
    background: rgba(129, 140, 248, 0.2);
}

.dark-theme .params-table:hover::-webkit-scrollbar-thumb {
    background: rgba(129, 140, 248, 0.8);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }

    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.api-docs {
    h3 {
        color: var(--primary-color);
        margin: 2rem 0 1rem;
        font-size: 1.4rem;
        font-weight: 600;
        user-select: none;
        -webkit-user-select: none;

        &:first-child {
            margin-top: 0;
        }
    }

    .code-wrapper {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        margin: 1rem 0;
        background: var(--code-bg) !important;
        overflow-x: auto;
        /* 允许代码水平滚动 */
        -webkit-overflow-scrolling: touch;
        /* 平滑滚动 */

        .dark-theme & {
            background: #111827 !important;
        }

        .copy-btn {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            padding: 0.3rem 0.7rem;
            background: linear-gradient(135deg, #4361EE 0%, #7209B7 100%);
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 1;
            opacity: 0;
            transform: translateY(10px) scale(0.9);
            user-select: none;
            -webkit-user-select: none;
            box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);

            .dark-theme & {
                background: linear-gradient(135deg, #5e60ce 0%, #7400b8 100%);
                box-shadow: 0 2px 8px rgba(116, 0, 184, 0.4);
            }

            &:hover {
                background: linear-gradient(135deg, #3a56df 0%, #6209a4 100%);
                color: white;
                transform: translateY(-2px) scale(1.05);
                box-shadow: 0 4px 10px rgba(99, 102, 241, 0.4);

                .dark-theme & {
                    background: linear-gradient(135deg, #5051c0 0%, #6400a0 100%);
                    box-shadow: 0 4px 12px rgba(116, 0, 184, 0.5);
                }
            }
        }

        &:hover {
            .copy-btn {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        pre {
            margin: 0 !important;
            background: var(--code-bg) !important;
            padding: 1.2rem;
            overflow-x: auto;
            border: 1px solid #e5e7eb;
            opacity: 1 !important;

            .dark-theme & {
                background: #111827 !important;
                border: 1px solid #374151;
            }

            code {
                font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                font-size: 0.95rem;
                color: var(--text-color);

                .dark-theme & {
                    color: #f9fafb;
                }
            }
        }
    }
}



.auth-modal {
    max-width: 420px;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.6rem;
        user-select: none;
        -webkit-user-select: none;

        label {
            color: var(--text-color);
            font-weight: 500;
            font-size: 0.95rem;
        }

        input {
            padding: 0.8rem;
            border: 1.5px solid var(--border-color, #d1d5db); // 增加边框宽度和默认色
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: var(--card-bg, #fff);
            color: var(--text-color, #111);
            box-shadow: 0 0 0 1px #e5e7eb; // 亮色主题下增加淡灰色描边

            &:focus {
                outline: none;
                border-color: var(--primary-color, #6366f1);
                box-shadow: 0 0 0 3px var(--primary-light, #c7d2fe);
            }
        }
    }

    .submit-btn {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 1rem;
        border: none;
        border-radius: 12px;
        background: linear-gradient(135deg, #4361EE 0%, #7209B7 100%);
        color: white;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        user-select: none;
        -webkit-user-select: none;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);

        &:hover:not(:disabled) {
            background: linear-gradient(135deg, #3a56df 0%, #6209a4 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
        }

        &:disabled {
            background: linear-gradient(135deg, #a4b0ff 0%, #c3a4d7 100%);
            cursor: not-allowed;
            transform: none !important;
        }

        .dark-theme & {
            background: linear-gradient(135deg, #5e60ce 0%, #7400b8 100%);
            box-shadow: 0 4px 12px rgba(116, 0, 184, 0.4);

            &:hover:not(:disabled) {
                background: linear-gradient(135deg, #5051c0 0%, #6400a0 100%);
                box-shadow: 0 6px 16px rgba(116, 0, 184, 0.5);
            }

            &:disabled {
                background: linear-gradient(135deg, #6b6eb7 0%, #aa77c1 100%);
            }
        }
    }
}

.user-modal {
    max-width: 480px;

    .user-details {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 0.5rem;
        border-radius: 16px;
        background: #ffffff;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid #e5e7eb;

        .dark-theme & {
            background: #1f2937;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            border: 1px solid #374151;
        }

        p {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);

            .dark-theme & {
                background: #111827;
                border: 1px solid #374151;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            &:hover {
                background: #ffffff;
                transform: translateY(-2px);
                border-color: #6366f1;
                box-shadow: 0 8px 16px rgba(99, 102, 241, 0.15);

                .dark-theme & {
                    background: #1f2937;
                    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.25);
                }
            }

            strong {
                color: #6366f1;
                font-size: 0.9rem;
                font-weight: 500;
                user-select: none;
                -webkit-user-select: none;
            }

            span {
                font-size: 1.1rem;
                font-weight: 600;
                caret-color: transparent;
            }
        }

        p:last-child {
            grid-column: 1 / -1;
        }

        .token-container {
            grid-column: 1 / -1;
            background: #f9fafb;
            padding: 1.2rem;
            border-radius: 12px;
            overflow-x: auto;
            margin: 0.8rem 0;
            max-width: 100%;
            word-break: break-all;
            white-space: pre-wrap;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            position: relative;
            color: #111827;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);

            .dark-theme & {
                background: #111827;
                border: 1px solid #374151;
                color: #f9fafb;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }

            &:hover {
                background: #ffffff;
                border-color: #6366f1;
                transform: translateY(-2px);
                box-shadow: 0 8px 16px rgba(99, 102, 241, 0.15);

                .dark-theme & {
                    background: #1f2937;
                    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.25);
                }
            }

            &:active {
                background: #eef2ff;
                color: #4f46e5;
                transform: translateY(0);

                .dark-theme & {
                    background: rgba(99, 102, 241, 0.2);
                    color: #818cf8;
                }
            }

            code {
                font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                font-size: 0.9rem;
                color: inherit;
            }

            &::after {
                content: '点击复制';
                display: block;
                font-size: 0.85rem;
                color: #6366f1;
                margin-top: 0.8rem;
                text-align: center;
                font-weight: 500;
                opacity: 0.8;
                transition: all 0.3s ease;

                .dark-theme & {
                    color: #818cf8;
                }
            }

            &:hover::after {
                opacity: 1;
                transform: translateY(2px);
            }
        }

        .button-group {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 1rem;
        }

        .reset-token-btn,
        .logout-btn {
            padding: 1rem;
            border: none;
            border-radius: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            box-shadow: 0 4px 12px var(--shadow-color);

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 16px var(--shadow-hover);
            }

            &:active {
                transform: translateY(0);
            }
        }

        .verify-btn {
            background: linear-gradient(135deg, #38b2ac 0%, #0694a2 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(56, 178, 172, 0.3) !important;

            .dark-theme & {
                background: linear-gradient(135deg, #38a89d 0%, #0d9488 100%) !important;
                box-shadow: 0 4px 12px rgba(13, 148, 136, 0.4) !important;
            }

            &:hover {
                transform: translateY(-2px);
                background: linear-gradient(135deg, #319795 0%, #0987a0 100%) !important;
                box-shadow: 0 6px 16px rgba(56, 178, 172, 0.4) !important;

                .dark-theme & {
                    background: linear-gradient(135deg, #2c7a7b 0%, #0c8599 100%) !important;
                    box-shadow: 0 6px 16px rgba(13, 148, 136, 0.5) !important;
                }
            }
        }

        .reset-token-btn {
            background: linear-gradient(135deg, #4361EE 0%, #7209B7 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;

            .dark-theme & {
                background: linear-gradient(135deg, #5e60ce 0%, #7400b8 100%) !important;
                box-shadow: 0 4px 12px rgba(116, 0, 184, 0.4) !important;
            }

            &:hover {
                background: linear-gradient(135deg, #3a56df 0%, #6209a4 100%) !important;
                box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4) !important;

                .dark-theme & {
                    background: linear-gradient(135deg, #5051c0 0%, #6400a0 100%) !important;
                    box-shadow: 0 6px 16px rgba(116, 0, 184, 0.5) !important;
                }
            }
        }

        .logout-btn {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white !important;
            /* 确保文字颜色为白色 */
            border: 1px solid #c82333;
            /* 添加边框增加可见性 */

            &:hover {
                background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            }
        }
    }
}

.api-test {
    margin-top: 1.5rem;
    background: var(--modal-bg) !important;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    opacity: 1 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);

    .dark-theme & {
        background: #111827 !important;
        border: 1px solid #374151;
    }

    &:hover {
        border-color: var(--primary-color);
        box-shadow: 0 8px 24px var(--shadow-color);
    }

    .test-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;

        .form-group {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 0.5rem;
            user-select: none;
            -webkit-user-select: none;

            label {
                color: #111827;
                font-weight: 500;
                min-width: 120px;
                font-size: 0.95rem;
                white-space: nowrap;

                .dark-theme & {
                    color: #f9fafb;
                }
            }

            input {
                flex: 1;
                min-width: 200px;
                max-width: 100%;
                padding: 0.8rem;
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                font-size: 1rem;
                transition: all 0.3s ease;
                background-color: #ffffff !important;
                color: #111827;
                opacity: 1 !important;

                .dark-theme & {
                    background-color: #1f2937 !important;
                    color: #f9fafb;
                    border: 1px solid #374151;
                }

                &:focus {
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px var(--primary-light);
                }
            }
        }
    }

    .test-actions {
        display: flex;
        justify-content: center;
        margin: 1.5rem 0;

        .test-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, #4361EE 0%, #7209B7 100%) !important;
            color: white !important;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
            -webkit-user-select: none;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

            .dark-theme & {
                background: linear-gradient(135deg, #5e60ce 0%, #7400b8 100%) !important;
                box-shadow: 0 4px 12px rgba(116, 0, 184, 0.4) !important;
            }

            &:hover:not(:disabled) {
                background: linear-gradient(135deg, #3a56df 0%, #6209a4 100%) !important;
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4) !important;

                .dark-theme & {
                    background: linear-gradient(135deg, #5051c0 0%, #6400a0 100%) !important;
                    box-shadow: 0 6px 16px rgba(116, 0, 184, 0.5) !important;
                }
            }

            &:disabled {
                background: linear-gradient(135deg, #a4b0ff 0%, #c3a4d7 100%) !important;
                color: rgba(255, 255, 255, 0.7) !important;
                cursor: not-allowed;
                box-shadow: none !important;

                .dark-theme & {
                    background: linear-gradient(135deg, #6b6eb7 0%, #aa77c1 100%) !important;
                }
            }
        }
    }

    .test-result {
        h4 {
            color: var(--primary-color);
            margin-bottom: 0.8rem;
            font-size: 1.2rem;
            font-weight: 600;
        }
    }
}

.params-table {
    margin: 1rem 0;
    background: var(--modal-bg) !important;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 1 !important;
    /* 添加以下属性以支持手机端的水平滚动 */
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    /* 为iOS添加平滑滚动 */
    cursor: pointer;
    /* 添加手指光标样式 */

    .dark-theme & {
        background: #111827 !important;
        border: 1px solid #374151;
    }

    &:hover {
        border-color: var(--primary-color);
        box-shadow: 0 8px 24px var(--shadow-color);
    }

    .params-header {
        display: grid;
        grid-template-columns: minmax(120px, 1fr) minmax(80px, 1fr) minmax(80px, 0.8fr) minmax(200px, 2fr) minmax(120px, 1fr);
        gap: 1rem;
        padding: 1rem;
        background: #303f9f;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: white;
        opacity: 1 !important;
        min-width: 600px;
        /* 设置最小宽度确保在小屏幕上可以水平滚动 */
        cursor: pointer;
        /* 添加手指光标样式 */

        .dark-theme & {
            border-bottom: 1px solid #374151;
        }
    }

    .params-body {
        min-width: 600px;
        /* 与header保持一致的最小宽度 */
        cursor: pointer;
        /* 添加手指光标样式 */

        .param-row {
            display: grid;
            grid-template-columns: minmax(120px, 1fr) minmax(80px, 1fr) minmax(80px, 0.8fr) minmax(200px, 2fr) minmax(120px, 1fr);
            gap: 1rem;
            padding: 0.8rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            align-items: start;
            transition: background 0.3s ease;
            background: var(--modal-bg) !important;
            opacity: 1 !important;
            cursor: pointer;
            /* 添加手指光标样式 */

            .dark-theme & {
                background: #1f2937 !important;
                border-bottom: 1px solid #374151;
            }

            &:nth-child(odd) {
                background: var(--bg-color) !important;

                .dark-theme & {
                    background: #111827 !important;
                }
            }

            &:hover {
                background: #eef2ff !important;

                .dark-theme & {
                    background: rgba(99, 102, 241, 0.1) !important;
                }
            }

            &:last-child {
                border-bottom: none;
            }

            .param-name {
                font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                color: var(--primary-color);
                word-break: break-all;
            }

            .param-type {
                color: var(--text-secondary);
            }

            .param-required {
                .required-tag {
                    display: inline-block;
                    padding: 0.2rem 0.6rem;
                    border-radius: 4px;
                    font-size: 0.85rem;
                    background: var(--code-bg);
                    color: var(--text-secondary);

                    &.required {
                        background: #2196f3;
                        color: white;
                    }
                }
            }

            .param-desc {
                color: var(--text-secondary);
                line-height: 1.5;
                word-break: break-word;
            }

            .param-example {

                .example-tag,
                .no-example-tag {
                    display: inline-block;
                    padding: 0.2rem 0.6rem;
                    border-radius: 4px;
                    font-size: 0.85rem;
                    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                    word-break: break-all;
                    white-space: pre-wrap;
                    max-width: 100%;
                }

                .example-tag {
                    background: var(--primary-light);
                    color: var(--primary-dark);
                    border: 1px solid var(--primary-color);
                }

                .no-example-tag {
                    background: var(--code-bg);
                    color: var(--text-secondary);
                    border: 1px solid var(--border-color);
                }
            }
        }
    }
}

.loading-spinner {
    display: inline-block;
    width: 1.2em;
    height: 1.2em;
    border: 2px solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spin 0.6s linear infinite;
    opacity: 0.8;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 1.5rem;
}

.verify-status {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    border-radius: 4px;
    font-size: 0.85rem;
    background: var(--code-bg);
    color: #dc3545;
    border: 1px solid #dc3545;

    &.verified {
        background: var(--primary-light);
        color: var(--primary-dark);
        border: 1px solid var(--primary-color);
    }

    .verification-instructions {
        font-size: 0.8rem;
        color: var(--text-secondary);
        font-weight: normal;
        line-height: 1.4;
    }
}

.turnstile-container {
    display: flex;
    justify-content: center;
    margin: 1rem 0;
}

.code-wrapper {
    background: var(--code-bg);
    border-radius: 12px;
    padding: 1rem;
    margin: 1rem 0;
    border: 1px solid var(--code-border);
    overflow-x: auto;
    box-shadow: 0 20px 60px var(--shadow-color);
}

pre,
code {
    font-family: 'Fira Code', monospace;
    font-size: 0.9rem;
    background: var(--code-bg);
    color: var(--code-color);
    border-radius: 4px;
    white-space: pre-wrap;
    word-break: break-word;
}

.json-key {
    color: #9d0006;

    .dark-theme & {
        color: #f59e0b;
    }
}

.json-string {
    color: #38a169;

    .dark-theme & {
        color: #10b981;
    }
}

.json-number {
    color: #dd6b20;

    .dark-theme & {
        color: #f97316;
    }
}

.json-boolean {
    color: #0369a1;

    .dark-theme & {
        color: #60a5fa;
    }
}

.json-null {
    color: #4338ca;

    .dark-theme & {
        color: #818cf8;
    }
}

:global(.message-container) {
    position: fixed;
    top: 24px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    z-index: 9999;
    pointer-events: none;
}

// 深色主题模态框样式
.dark-theme {
    .modal-content {
        background: var(--modal-bg) !important;
        border: 1px solid #374151;
    }

    .modal-header {
        background: var(--modal-bg) !important;
        border-bottom: 1px solid #374151;
    }

    .modal-body {
        background: var(--modal-bg) !important;
        color: #f9fafb;
    }

    .modal-title {
        color: #f9fafb;
    }

    .modal-close {
        background: #111827;
        color: #f9fafb;

        &:hover {
            background: rgba(99, 102, 241, 0.2);
            color: #818cf8;
        }
    }
}

// 独立设置method-tag样式以避免嵌套问题
.method-tag {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    background: linear-gradient(135deg, #4361EE 0%, #7209B7 100%) !important;
    color: white !important;
    border-radius: 4px;
    font-size: 0.85rem;
    margin-right: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    border: none;
    user-select: none;
    -webkit-user-select: none;
}

.dark-theme .method-tag {
    background: linear-gradient(135deg, #5e60ce 0%, #7400b8 100%) !important;
    box-shadow: 0 2px 6px rgba(116, 0, 184, 0.4);
}

/* 增加以下代码，强制指定亮色主题的背景色 */
:root:not(.dark-theme) {

    .modal-content,
    .modal-header,
    .modal-body {
        background-color: #ffffff !important;
        color: #111827 !important;
        opacity: 1 !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }

    .auth-modal,
    .user-modal {
        background-color: #ffffff !important;
    }

    /* 为模态框增加明显的阴影 */
    .modal-content {
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
        border: 2px solid #e5e7eb !important;
    }
}

/* 保留暗色主题的样式 */
.dark-theme {
    .modal-content {
        background-color: #1f2937 !important;
        border: 1px solid #374151;
    }

    .modal-header {
        background-color: #1f2937 !important;
        border-bottom: 1px solid #374151;
    }

    .modal-body {
        background-color: #1f2937 !important;
        color: #f9fafb;
    }
}

.popover-content {
    position: relative;

    .popover-icon {
        position: absolute;
        right: -5px;
        top: -5px;
        font-size: 36px;
        opacity: 0.15;
        transform: rotate(5deg);
    }

    .popover-text {
        margin-bottom: 14px;
        position: relative;
        z-index: 2;

        &::before {
            content: "功能说明";
            display: block;
            font-weight: 700;
            font-size: 13px;
            margin-bottom: 8px;
            color: #4361EE;
            letter-spacing: 1px;
            position: relative;

            &::after {
                content: "";
                position: absolute;
                bottom: -4px;
                left: 0;
                width: 24px;
                height: 2px;
                background-color: #4361EE;
                border-radius: 2px;
            }
        }
    }

    .popover-cost {
        display: inline-block;
        background: rgba(67, 97, 238, 0.1);
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 14px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(67, 97, 238, 0.2);

        .cost-label {
            font-weight: 600;
            color: #4361EE;
            margin-right: 2px;
        }

        .cost-value {
            font-weight: 700;
            color: #4361EE;
        }
    }

    /* 移动端关闭按钮 */
    .popover-close-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;
        z-index: 10;

        &:hover {
            background: rgba(0, 0, 0, 0.2);
            transform: scale(1.1);
        }

        svg {
            width: 12px;
            height: 12px;
            color: #666;
        }
    }
}

/* 为深色主题下的Popover设置样式 */
:root.dark-theme {
    .api-description-popover {
        background-image: linear-gradient(to bottom right, rgba(31, 41, 55, 0.95), rgba(17, 24, 39, 0.9)) !important;
        color: #f3f4f6 !important;
        border-left: 4px solid #818cf8 !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 15px rgba(129, 140, 248, 0.2) !important;
        animation: fadeIn 0.3s ease-out, neonPulse 3s infinite ease-in-out !important;
    }

    .popover-content {
        .popover-icon {
            opacity: 0.2;
            filter: drop-shadow(0 0 8px rgba(129, 140, 248, 0.5));
        }

        .popover-text::before {
            color: #818cf8;
            text-shadow: 0 0 10px rgba(129, 140, 248, 0.3);

            &::after {
                background-color: #818cf8;
                box-shadow: 0 0 8px rgba(129, 140, 248, 0.5);
            }
        }

        .popover-cost {
            background: rgba(129, 140, 248, 0.15);
            border: 1px solid rgba(129, 140, 248, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

            .cost-label,
            .cost-value {
                color: #a5b4fc;
            }

            .cost-value {
                text-shadow: 0 0 8px rgba(129, 140, 248, 0.5);
            }
        }
    }
}

/* 确保卡片上的点击事件仍然有效 */
.card {
    .el-popover__reference {
        display: block;
        height: 100%;
        width: 100%;
    }
}

/* 美化Popover箭头 */
.el-popper.api-description-popover {
    .el-popper__arrow::before {
        background: linear-gradient(45deg, #4361EE, transparent) !important;
        border-color: transparent !important;
    }
}

:root.dark-theme .el-popper.api-description-popover {
    .el-popper__arrow::before {
        background: linear-gradient(45deg, #818cf8, transparent) !important;
    }
}

/* 为深色主题下的Popover添加霓虹灯效果 */
@keyframes neonPulse {
    0% {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 15px rgba(129, 140, 248, 0.2);
        border-left-color: #818cf8;
    }

    50% {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 25px rgba(129, 140, 248, 0.4);
        border-left-color: #a5b4fc;
    }

    100% {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 15px rgba(129, 140, 248, 0.2);
        border-left-color: #818cf8;
    }
}

:root.dark-theme {
    .api-description-popover {
        background-image: linear-gradient(to bottom right, rgba(31, 41, 55, 0.95), rgba(17, 24, 39, 0.9)) !important;
        color: #f3f4f6 !important;
        border-left: 4px solid #818cf8 !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 15px rgba(129, 140, 248, 0.2) !important;
        animation: fadeIn 0.3s ease-out, neonPulse 3s infinite ease-in-out !important;
    }

    // ... existing code ...
}

/* 卡片在深色模式下的特效 */
:root.dark-theme {
    .card {
        transition: all 0.3s ease-in-out;

        &:hover {
            box-shadow: 0 0 20px rgba(129, 140, 248, 0.3), 0 0 8px rgba(129, 140, 248, 0.2) !important;
            transform: translateY(-5px);
            border-color: rgba(129, 140, 248, 0.5) !important;

            .icon {
                animation: iconPulse 2s infinite ease-in-out;
                color: #a5b4fc !important;
            }

            h3 {
                color: #a5b4fc !important;
            }
        }
    }
}

@keyframes iconPulse {
    0% {
        transform: scale(1);
        opacity: 0.9;
    }

    50% {
        transform: scale(1.1);
        opacity: 1;
    }

    100% {
        transform: scale(1);
        opacity: 0.9;
    }
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    border-radius: 16px;
    background: var(--modal-bg);
    border: 1px dashed var(--border-color);
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    opacity: 1 !important;

    .loading-animation {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(99, 102, 241, 0.3);
        border-top-color: rgba(99, 102, 241, 0.6);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    p {
        margin-top: 1rem;
        color: var(--text-color);
        font-size: 1rem;
        font-weight: 500;
    }
}

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    border-radius: 16px;
    background: var(--modal-bg);
    border: 1px dashed var(--border-color);
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    opacity: 1 !important;

    .error-icon {
        font-size: 2rem;
        color: #dc3545;
    }

    p {
        margin-top: 1rem;
        color: var(--text-color);
        font-size: 1rem;
        font-weight: 500;
    }

    .retry-btn {
        margin-top: 1rem;
        padding: 0.8rem 1.5rem;
        border: none;
        border-radius: 12px;
        background: linear-gradient(135deg, #4361EE 0%, #7209B7 100%);
        color: white;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);

        &:hover:not(:disabled) {
            background: linear-gradient(135deg, #3a56df 0%, #6209a4 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
        }

        &:disabled {
            background: linear-gradient(135deg, #a4b0ff 0%, #c3a4d7 100%);
            cursor: not-allowed;
            transform: none !important;
        }

        .dark-theme & {
            background: linear-gradient(135deg, #5e60ce 0%, #7400b8 100%);
            box-shadow: 0 4px 12px rgba(116, 0, 184, 0.4);

            &:hover:not(:disabled) {
                background: linear-gradient(135deg, #5051c0 0%, #6400a0 100%);
                box-shadow: 0 6px 16px rgba(116, 0, 184, 0.5);
            }

            &:disabled {
                background: linear-gradient(135deg, #6b6eb7 0%, #aa77c1 100%);
            }
        }
    }
}

.verification-code-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;

    .verification-code-input {
        display: flex;
        gap: 0.5rem;

        .verification-code-input-field {
            flex: 1;
            padding: 0.8rem;
            border: 1.5px solid var(--border-color, #d1d5db);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: var(--card-bg, #fff);
            color: var(--text-color, #111);
            box-shadow: 0 0 0 1px #e5e7eb;

            &:focus {
                outline: none;
                border-color: var(--primary-color, #6366f1);
                box-shadow: 0 0 0 3px var(--primary-light, #c7d2fe);
            }
        }

        .send-code-btn {
            padding: 0.8rem 1.2rem;
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, #4361EE 0%, #7209B7 100%);
            color: white;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
            -webkit-user-select: none;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);

            &:hover:not(:disabled) {
                background: linear-gradient(135deg, #3a56df 0%, #6209a4 100%);
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            }

            &:disabled {
                background: linear-gradient(135deg, #a4b0ff 0%, #c3a4d7 100%);
                cursor: not-allowed;
                transform: none !important;
            }

            .dark-theme & {
                background: linear-gradient(135deg, #5e60ce 0%, #7400b8 100%);
                box-shadow: 0 4px 12px rgba(116, 0, 184, 0.4);

                &:hover:not(:disabled) {
                    background: linear-gradient(135deg, #5051c0 0%, #6400a0 100%);
                    box-shadow: 0 6px 16px rgba(116, 0, 184, 0.5);
                }

                &:disabled {
                    background: linear-gradient(135deg, #6b6eb7 0%, #aa77c1 100%);
                }
            }
        }
    }
}

/* 验证码样式 */
.verification-code-group {
    margin-bottom: 15px;
}

.verification-code-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.verification-code-input input {
    flex: 1;
    padding: 0.8rem;
    border: 1.5px solid var(--border-color, #d1d5db);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--card-bg, #fff);
    color: var(--text-color, #111);
}

.verification-code-input input:focus {
    outline: none;
    border-color: var(--primary-color, #6366f1);
    box-shadow: 0 0 0 3px var(--primary-light, #c7d2fe);
}

.send-code-btn {
    white-space: nowrap;
    font-size: 0.9rem;
    padding: 0 12px;
    height: 40px;
    min-width: 110px;
}

.consumption-records-btn {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%) !important;
    color: white !important;
    border: none !important;
    padding: 0.5rem 1rem !important;
    border-radius: 8px !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, #5b5bd6 0%, #4338ca 100%) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4) !important;
    }

    .dark-theme & {
        background: linear-gradient(135deg, #818cf8 0%, #6366f1 100%) !important;
        box-shadow: 0 2px 8px rgba(129, 140, 248, 0.3) !important;

        &:hover:not(:disabled) {
            background: linear-gradient(135deg, #6366f1 0%, #5b5bd6 100%) !important;
            box-shadow: 0 4px 12px rgba(129, 140, 248, 0.4) !important;
        }
    }
}

.consumption-records {
    grid-column: 1 / -1;
    background: #f9fafb;
    padding: 1.2rem;
    border-radius: 12px;
    overflow-y: auto;
    margin: 0.8rem 0;
    max-width: 100%;
    max-height: 200px;
    word-break: break-all;
    white-space: normal;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    position: relative;
    color: #111827;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);

    .dark-theme & {
        background: #111827;
        border: 1px solid #374151;
        color: #f9fafb;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    &:hover {
        border-color: #6366f1;
        box-shadow: 0 8px 16px rgba(99, 102, 241, 0.15);

        .dark-theme & {
            background: #1f2937;
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.25);
        }
    }

    .no-records {
        color: var(--text-muted);
        font-style: italic;
        text-align: center;
        padding: 15px 0;
    }

    .records-list {
        .record-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 10px;
            padding: 8px 5px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .record-item {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 10px;
            padding: 8px 5px;
            border-bottom: 1px solid var(--border-color-light);

            &:last-child {
                border-bottom: none;
            }

            .record-details {
                color: var(--text-color);
                font-size: 0.9rem;
            }

            .record-amount {
                color: #f56c6c;
                font-weight: 500;
                font-size: 0.9rem;
            }

            .record-time {
                color: var(--text-secondary);
                font-size: 0.8rem;
            }
        }
    }
}

/* 手动复制对话框样式 */
:deep(.manual-copy-dialog) {
    .el-message-box__content {
        text-align: left;
    }

    .el-message-box__message {
        margin: 0;
    }
}

/* Clash 帮助对话框样式 - 确保内容完全可见 */
:deep(.clash-help-dialog) {

    /* 对话框容器 - 增加宽度确保序号完全显示 */
    .el-message-box {
        width: 95% !important;
        max-width: 600px !important;
        min-width: 480px !important;
        min-height: auto !important;
        max-height: 85vh !important;
        overflow: hidden !important;
    }

    /* 标题区域 */
    .el-message-box__header {
        padding: 20px 20px 10px 20px !important;
        border-bottom: none !important;
    }

    /* 内容区域 - 关键修复 */
    .el-message-box__content {
        text-align: left !important;
        max-height: calc(85vh - 120px) !important;
        /* 减去头部和按钮区域高度 */
        overflow-y: auto !important;
        overflow-x: hidden !important;
        padding: 0 30px 10px 30px !important;
        /* 增加左右内边距为序号留出空间 */

        /* 标题样式 */
        h4 {
            color: var(--primary-color) !important;
            margin: 0 0 15px 0 !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            line-height: 1.4 !important;
            display: block !important;
        }

        /* 有序列表样式 - 修复边距确保序号完全显示 */
        div ol {
            padding-left: 60px !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
            line-height: 1.8 !important;
            margin-top: 15px !important;
            margin-bottom: 15px !important;
            list-style-type: decimal !important;
            list-style-position: outside !important;
            box-sizing: border-box !important;

            li {
                margin-bottom: 10px !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
                color: var(--text-color) !important;
                font-size: 14px !important;
                line-height: 1.6 !important;
                display: list-item !important;
                padding-left: 10px !important;
                padding-right: 0 !important;
                text-indent: 0 !important;
                box-sizing: border-box !important;
                list-style-type: decimal !important;
                list-style-position: outside !important;
            }
        }

        /* 额外的强制样式 */
        ol li::marker {
            color: var(--text-color) !important;
            font-weight: normal !important;
        }

        /* 提示段落样式 */
        p {
            margin: 20px 0 0 0 !important;
            padding: 12px !important;
            background: var(--card-bg, #f8f9fa) !important;
            border-radius: 6px !important;
            border-left: 3px solid var(--primary-color) !important;
            font-size: 12px !important;
            line-height: 1.5 !important;
            color: var(--text-color, #666) !important;
            display: block !important;
        }
    }

    /* 消息容器 - 移除默认限制 */
    .el-message-box__message {
        padding: 0 !important;
        margin: 0 !important;
        max-height: none !important;
        overflow: visible !important;
        line-height: normal !important;
        word-wrap: break-word !important;
    }

    /* 按钮区域 */
    .el-message-box__btns {
        padding: 20px !important;
        border-top: 1px solid var(--border-color, #ebeef5) !important;
        margin-top: 10px !important;
        flex-shrink: 0 !important;
    }
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {

    /* 整体应用容器 */
    .app {
        padding: 1rem;
        max-width: 100%;
    }

    /* 头部区域 */
    .header {
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
        margin-bottom: 2rem;
        padding: 1rem;

        .site-title {
            font-size: 1.6rem;
            text-align: center;
            line-height: 1.4;
            padding: 0 0.5rem;
            word-break: break-word;
            hyphens: auto;
        }

        .user-section {
            width: 100%;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
        }
    }

    /* 卡片容器 */
    .cards-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0.5rem;
    }

    /* 卡片样式 */
    .card {
        min-height: auto;
        padding: 1.5rem;

        .card-content {
            h3 {
                font-size: 1.1rem;
                margin-bottom: 0.8rem;
            }

            .icon {
                font-size: 2rem;
            }

            .cost-badge {
                font-size: 0.8rem;
                padding: 0.3rem 0.6rem;
            }
        }
    }

    /* 模态框 */
    .modal-content {
        width: 95%;
        max-width: none;
        margin: 1rem;
        max-height: 90vh;
        overflow-y: auto;
        border-radius: 16px;
    }

    .modal-header {
        padding: 1rem;

        h2 {
            font-size: 1.3rem;
        }
    }

    .modal-body {
        padding: 1rem;
    }

    /* 认证表单 */
    .auth-form {
        padding: 1rem;
        gap: 1rem;

        .form-group {
            input {
                font-size: 16px;
                /* 防止iOS缩放 */
                padding: 0.8rem;
            }
        }

        .verification-code-input {
            flex-direction: column;
            gap: 0.8rem;

            input {
                width: 100%;
            }

            .send-code-btn {
                width: 100%;
                margin: 0;
            }
        }
    }

    /* API测试区域 */
    .api-test {
        padding: 1rem;

        .test-form {
            .form-group {
                flex-direction: column;
                align-items: flex-start;
                width: 100%;
                margin-bottom: 1rem;

                label {
                    width: 100%;
                    margin-bottom: 0.5rem;
                    font-weight: 600;
                }

                input {
                    width: 100%;
                    font-size: 16px;
                    /* 防止iOS缩放 */
                }
            }
        }

        .test-actions {
            margin: 1rem 0;

            .test-btn {
                width: 100%;
                padding: 0.8rem;
            }
        }
    }

    /* 代码块 */
    .code-wrapper {
        position: relative;
        margin: 1rem 0;

        pre {
            font-size: 0.8rem;
            padding: 1rem;
            overflow-x: auto;
        }

        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
    }

    /* 参数表格 */
    .params-table {
        overflow-x: auto;

        .params-header {
            min-width: auto;
            grid-template-columns: 1fr 0.8fr 0.6fr 1.5fr 1fr;
            gap: 0.5rem;
            font-size: 0.8rem;
            padding: 0.8rem 0.5rem;
        }

        .params-body {
            min-width: auto;

            .param-row {
                grid-template-columns: 1fr 0.8fr 0.6fr 1.5fr 1fr;
                gap: 0.5rem;
                font-size: 0.8rem;
                padding: 0.6rem 0.5rem;
            }
        }
    }

    /* 用户信息 */
    .user-details {
        .token-container {
            font-size: 0.7rem;
            padding: 0.8rem;
            word-break: break-all;
        }

        .button-group {
            flex-direction: column;
            gap: 0.8rem;

            .reset-token-btn,
            .logout-btn {
                width: 100%;
            }
        }
    }

    /* 手机端手动复制对话框优化 */
    :deep(.manual-copy-dialog) {
        .el-message-box {
            width: 90% !important;
            max-width: 400px;
        }

        .el-message-box__content {
            padding: 15px;
        }
    }

    /* 手机端 Clash 帮助对话框优化 */
    :deep(.clash-help-dialog) {
        .el-message-box {
            width: 98% !important;
            max-width: none !important;
            min-width: 320px !important;
            margin: 5px !important;
            max-height: 85vh !important;
        }

        .el-message-box__header {
            padding: 15px 15px 10px 15px !important;
        }

        .el-message-box__content {
            padding: 0 15px 10px 15px !important;
            max-height: calc(85vh - 100px) !important;
            overflow-y: auto !important;

            h4 {
                font-size: 14px !important;
                margin-bottom: 10px !important;
            }

            ol {
                padding-left: 40px !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
                line-height: 1.6 !important;
                list-style-position: outside !important;
                box-sizing: border-box !important;

                li {
                    font-size: 13px !important;
                    margin-bottom: 8px !important;
                    margin-left: 0 !important;
                    margin-right: 0 !important;
                    padding-left: 8px !important;
                    padding-right: 0 !important;
                    text-indent: 0 !important;
                    box-sizing: border-box !important;
                }
            }

            p {
                padding: 10px !important;
                font-size: 11px !important;
                margin-top: 15px !important;
            }
        }

        .el-message-box__btns {
            padding: 15px !important;
        }
    }

    /* 主题切换按钮 - 移动端优化 */
    .theme-toggle {
        width: 48px;
        height: 48px;
        margin-right: 0.8rem;

        /* 确保图标在更大的按钮中居中 */
        svg {
            width: 1.8em;
            height: 1.8em;
        }
    }

    /* 认证按钮 - 移动端优化 */
    .auth-btn {
        padding: 0.8rem 1.2rem;
        font-size: 1rem;
        min-height: 48px;
        font-weight: 600;
        border-radius: 14px;

        /* 确保按钮有足够的触摸区域 */
        min-width: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 用户信息显示 - 移动端优化 */
    .user-info {
        padding: 0.8rem 1.2rem;
        min-height: 48px;
        border-radius: 14px;
        gap: 0.8rem;

        /* 确保用户图标足够大 */
        svg {
            width: 1.8em;
            height: 1.8em;
        }

        .balance {
            font-size: 1rem;
            font-weight: 600;
        }
    }
}

/* 小屏幕设备优化 (手机横屏和小平板) */
@media (max-width: 480px) {
    .app {
        padding: 0.5rem;
    }

    .header {
        margin-bottom: 1.5rem;
        padding: 0.5rem;

        .site-title {
            font-size: 1.3rem;
            line-height: 1.4;
            padding: 0 0.5rem;
            word-break: break-word;
            hyphens: auto;
            /* 在小屏幕上可能需要更紧凑的显示 */
            letter-spacing: -0.3px;
        }

        .user-section {
            gap: 0.8rem;

            /* 在小屏幕上可能需要垂直排列 */
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: center;
        }
    }

    /* 小屏幕上的按钮优化 */
    .theme-toggle {
        width: 46px;
        height: 46px;
        margin-right: 0.6rem;
    }

    .auth-btn {
        padding: 0.7rem 1rem;
        font-size: 0.95rem;
        min-height: 46px;
        min-width: 75px;
    }

    .user-info {
        padding: 0.7rem 1rem;
        min-height: 46px;

        .balance {
            font-size: 0.95rem;
        }
    }

    .cards-container {
        gap: 1rem;
        padding: 0.25rem;
    }

    .card {
        padding: 1rem;

        .card-content {
            h3 {
                font-size: 1rem;
            }

            .icon {
                font-size: 1.8rem;
            }
        }
    }

    .modal-content {
        margin: 0.5rem;
        border-radius: 12px;
    }

    .modal-header,
    .modal-body {
        padding: 0.8rem;
    }

    .auth-form {
        padding: 0.8rem;
        gap: 0.8rem;
    }
}

/* 忘记密码相关样式 */
.forgot-password-link {
    text-align: center;
    margin-top: 1rem;
}

.forgot-password-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password-link a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

.back-link {
    text-align: center;
    margin-top: 1rem;
}

.back-link a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.back-link a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* 深色主题下的忘记密码链接 */
:root.dark-theme .forgot-password-link a {
    color: var(--primary-color-dark);
}

:root.dark-theme .forgot-password-link a:hover {
    color: var(--primary-hover-dark);
}

:root.dark-theme .back-link a {
    color: var(--text-secondary-dark);
}

:root.dark-theme .back-link a:hover {
    color: var(--primary-color-dark);
}

/* 修改密码按钮样式 */
.change-password-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    transition: all 0.3s ease;
}

.change-password-btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.change-password-btn:active {
    transform: translateY(0);
}

/* 深色主题下的修改密码按钮 */
:root.dark-theme .change-password-btn {
    background: linear-gradient(135deg, #818cf8 0%, #a855f7 100%);
}

:root.dark-theme .change-password-btn:hover {
    background: linear-gradient(135deg, #6366f1 0%, #9333ea 100%);
    box-shadow: 0 4px 12px rgba(129, 140, 248, 0.3);
}


/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .card {
        /* 增加触摸目标大小 */
        min-height: 120px;

        &:active {
            transform: scale(0.98);
            transition: transform 0.1s ease;
        }
    }

    /* 确保所有交互元素符合触摸标准 */
    .auth-btn,
    .theme-toggle,
    .modal-close,
    .user-info {
        min-height: 48px;
        min-width: 48px;

        /* 添加触摸反馈 */
        &:active {
            transform: scale(0.96);
            transition: transform 0.1s ease;
        }
    }

    /* 用户区域按钮间距优化 */
    .user-section {
        gap: 1rem !important;

        /* 确保按钮之间有足够间距避免误触 */
        .auth-btn+.auth-btn {
            margin-left: 0.5rem;
        }
    }

    /* 移除悬停效果，因为触摸设备不需要 */
    .card:hover {
        transform: none;
        box-shadow: 0 8px 32px var(--shadow-hover);
    }

    /* 主题切换按钮触摸优化 */
    .theme-toggle {
        /* 增加触摸区域，但保持视觉大小 */
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
        }
    }
}

/* 移动端 Popover 优化 */
@media screen and (max-width: 768px) {
    .api-description-popover {
        /* 移动端 popover 样式调整 */
        max-width: 90vw !important;
        width: auto !important;

        .popover-content {
            .popover-close-btn {
                display: flex !important;
                background: rgba(67, 97, 238, 0.1) !important;

                svg {
                    color: #4361EE !important;
                }

                &:hover {
                    background: rgba(67, 97, 238, 0.2) !important;
                }
            }
        }
    }

    /* 深色主题下的移动端 popover */
    :root.dark-theme .api-description-popover {
        .popover-content {
            .popover-close-btn {
                background: rgba(129, 140, 248, 0.15) !important;

                svg {
                    color: #a5b4fc !important;
                }

                &:hover {
                    background: rgba(129, 140, 248, 0.25) !important;
                }
            }
        }
    }
}

/* 移动端专用优化 - 针对真实移动设备 */
@media screen and (max-width: 768px) and (pointer: coarse) {
    .header {
        .user-section {
            /* 在移动设备上确保按钮布局更友好 */
            min-height: 60px;
            padding: 0.5rem;

            /* 如果空间不够，允许换行 */
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            gap: 1rem;
        }
    }

    /* 登录状态下的用户信息优化 */
    .user-info {
        /* 确保用户信息在移动端足够大 */
        min-height: 50px;
        padding: 0.8rem 1rem;

        /* 优化文字显示 */
        .balance {
            white-space: nowrap;
            font-weight: 600;
        }
    }

    /* 未登录状态下的按钮组优化 */
    .auth-btn {
        /* 确保按钮在移动端足够大且易于点击 */
        min-height: 50px;
        padding: 0.8rem 1.2rem;
        font-size: 1rem;
        font-weight: 600;

        /* 防止按钮文字换行 */
        white-space: nowrap;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
    .card {
        border-width: 0.5px;
    }

    .modal-content {
        border-width: 0.5px;
    }
}

/* 横屏手机优化 */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .header {
        margin-bottom: 1rem;

        .user-section {
            gap: 0.8rem;
        }
    }

    .theme-toggle,
    .auth-btn,
    .user-info {
        /* 在横屏模式下稍微减小高度以节省空间 */
        min-height: 44px;
    }
}
</style>
