import urllib.parse
import sqlalchemy
import time
from fastapi import APIRouter, HTTPException, Request, Response
import logging
from curl_cffi import requests as curl_requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.db import engine, with_db_retry, rate_limit
    from api.models import subscriptions, users
    from api.auth import get_user_by_token
    from api.utils.common import get_client_ip
    from api.utils.api_client import CSKedayaAPI
except:
    from db import engine, with_db_retry, rate_limit
    from models import subscriptions, users
    from auth import get_user_by_token
    from utils.common import get_client_ip
    from utils.api_client import CSKedayaAPI

router = APIRouter(prefix="/api/v1", tags=["subscription"])


@router.get("/subscription")
async def get_subscription(token: str, req: Request = None):
    """获取订阅地址的内容和协议头"""
    client_ip = get_client_ip(req) if req else "unknown"

    # 通过token获取用户信息
    user_dict = get_user_by_token(token)
    if not user_dict:
        raise HTTPException(status_code=401, detail="无效的token")

    # 优化：减少清理频率，只在特定条件下执行清理
    # 使用简单的时间戳缓存来避免频繁清理
    current_time = time.time()

    # 初始化API客户端（确保在整个函数中都可以使用）
    api = CSKedayaAPI(
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************._-JqecFVqEZqDZxKru_x7mN56odzHBdlwhCrvKqg09g",
        refresh_domain=True,  # 使用最新的域名配置
    )

    # 检查是否需要清理（每5分钟最多清理一次）
    cache_key = "last_cleanup_time"
    last_cleanup = getattr(get_subscription, cache_key, 0)

    if current_time - last_cleanup > 300:  # 5分钟 = 300秒
        api.clean_expired_subscriptions()
        setattr(get_subscription, cache_key, current_time)

    with rate_limit(f"subscription:{client_ip}", max_requests=1, expire=5):

        @with_db_retry(max_retries=3)
        def do_get_subscription():
            with engine.connect() as db:
                with db.begin():
                    # 检查余额
                    price = 500  # 获取订阅地址的费用

                    # 首先检查用户是否已有分配的订阅
                    user_sub_result = db.execute(
                        subscriptions.select()
                        .where(subscriptions.c.username == user_dict["username"])
                        .order_by(subscriptions.c.created_at.desc())
                        .limit(1)
                    )
                    user_subscription = user_sub_result.fetchone()

                    # 如果用户有已分配的订阅，尝试获取其内容并检查流量和时间限制
                    if user_subscription:
                        try:
                            logger.info(
                                f"订阅检查：用户 {user_dict['username']} 已有订阅地址，检查是否可复用"
                            )
                            if hasattr(user_subscription, "_mapping"):
                                # 数据库对象
                                subscription_token = user_subscription._mapping[
                                    subscriptions.c.token
                                ]
                                subscription_url = f"https://{api.subscribe}/api/v1/client/subscribe?token={subscription_token}"
                            else:
                                # 手动创建的对象
                                subscription_token = user_subscription.token
                                subscription_url = f"https://{api.subscribe}/api/v1/client/subscribe?token={subscription_token}"

                            domain = urllib.parse.urlparse(subscription_url).netloc

                            response = curl_requests.get(
                                subscription_url,
                                headers={
                                    "User-Agent": "ClashForAndroid/2.5.9.foss",
                                    "Connection": "close",
                                    "Host": domain,
                                    "Accept": "*/*",
                                    "Accept-Encoding": "gzip, deflate, br",
                                },
                                timeout=30,
                                allow_redirects=True,
                            )

                            # 检查响应是否有效
                            if response.status_code == 200:
                                # 获取响应头和内容
                                headers = dict(response.headers)
                                content = response.content

                                # 解析subscription-userinfo头
                                subscription_info = headers.get(
                                    "subscription-userinfo", ""
                                )

                                # 解析上传、下载、总流量和过期时间
                                info_dict = {}
                                if subscription_info:
                                    parts = subscription_info.split(";")
                                    for part in parts:
                                        part = part.strip()
                                        if "=" in part:
                                            key, value = part.split("=", 1)
                                            info_dict[key.strip()] = value.strip()

                                # 获取流量使用情况和过期时间
                                upload = int(info_dict.get("upload", "0"))
                                download = int(info_dict.get("download", "0"))
                                total = int(info_dict.get("total", "0"))
                                expire = int(info_dict.get("expire", "0"))

                                # 检查流量和时间限制，使用UTC+8时区
                                # 获取当前UTC+8时间戳
                                utc8_offset = 8 * 3600  # UTC+8的秒数偏移
                                current_timestamp = int(time.time()) + utc8_offset
                                used_traffic = upload + download
                                traffic_ok = total != 0 and used_traffic < total
                                time_ok = expire != 0 and current_timestamp < expire

                                logger.info(
                                    f"流量检查：已使用={used_traffic}, 总流量={total}, 是否OK={traffic_ok}"
                                )
                                logger.info(
                                    f"时间检查：当前={current_timestamp}, 过期={expire}, 是否OK={time_ok}"
                                )

                                # 如果流量和时间都满足条件，直接返回旧订阅内容
                                if traffic_ok and time_ok:
                                    logger.info(
                                        f"订阅复用：用户 {user_dict['username']} 订阅未超流量和时间限制，复用旧订阅"
                                    )

                                    # 替换内容中的"宝贝云"为"可达鸭"
                                    try:
                                        decoded_content = content.decode(
                                            "utf-8", errors="replace"
                                        )
                                        modified_content = decoded_content.replace(
                                            "宝贝云", "可达鸭"
                                        )
                                        # 替换内容中的"t.me/bbyvpn"为"t.me/jpspjlq"
                                        modified_content = modified_content.replace(
                                            "t.me/bbyvpn", "t.me/jpspjlq"
                                        )
                                        # 替换内容中的"bit.ly/bbyvpn"为"kedaya.xyz"
                                        modified_content = modified_content.replace(
                                            "bit.ly/bbyvpn", "kedaya.xyz"
                                        )
                                        content = modified_content.encode("utf-8")
                                    except Exception as e:
                                        logger.error(
                                            f"内容替换：替换内容时出错: {str(e)}"
                                        )

                                    # 创建响应对象
                                    content_type = response.headers.get(
                                        "Content-Type", "text/plain"
                                    )
                                    R_headers = {}
                                    # 复制原始响应头
                                    for k, v in headers.items():
                                        if k.lower() != "content-length":
                                            if k == "strict-transport-security":
                                                R_headers[k] = v
                                            if k == "profile-update-interval":
                                                R_headers[k] = v

                                    R_headers["subscription-userinfo"] = (
                                        subscription_info
                                    )
                                    # 设置Content-Disposition头
                                    R_headers["Content-Disposition"] = (
                                        "attachment;filename*=UTF-8''%E5%8F%AF%E8%BE%BE%E9%B8%AD"
                                    )
                                    R_headers["Profile-Web-Page-Url"] = (
                                        "https://www.kedaya.xyz"
                                    )
                                    R_headers["Access-Control-Allow-Origin"] = "*"

                                    # 确保内容类型正确
                                    if "text/html" in content_type:
                                        R_headers["Content-Type"] = (
                                            "text/html; charset=UTF-8"
                                        )
                                    elif "application/json" in content_type:
                                        R_headers["Content-Type"] = (
                                            "application/json; charset=UTF-8"
                                        )
                                    elif "text/plain" in content_type:
                                        R_headers["Content-Type"] = (
                                            "text/plain; charset=UTF-8"
                                        )
                                    logger.debug(f"响应头：R_headers: {R_headers}")
                                    final_response = Response(
                                        content=content,
                                        media_type=content_type,
                                        headers=R_headers,
                                    )
                                    return final_response
                        except Exception as e:
                            logger.error(f"订阅检查：检查现有订阅时出错: {str(e)}")
                            import traceback

                            traceback.print_exc()

                    # 如果用户没有订阅或者现有订阅不满足条件，获取新订阅
                    logger.info(
                        f"订阅分配：为用户 {user_dict['username']} 分配新的订阅地址"
                    )

                    if user_dict["balance"] < price:
                        raise HTTPException(status_code=400, detail="余额不足")

                    # 优化：一次性获取足够的可用订阅记录，减少数据库查询次数
                    subscription = None

                    # 一次性获取20条可用订阅记录
                    result = db.execute(
                        subscriptions.select()
                        .where(
                            sqlalchemy.or_(
                                subscriptions.c.username.is_(None),
                                subscriptions.c.username == "system",
                            )
                        )
                        .order_by(subscriptions.c.created_at.desc())
                        .limit(20)  # 一次性获取足够的记录
                    )
                    available_subscriptions = result.fetchall()

                    # 遍历找到第一个真正可用的订阅
                    for sub in available_subscriptions:
                        # 检查这个订阅是否真的可用（username为None或"system"）
                        if hasattr(sub, "_mapping"):
                            sub_username = sub._mapping[subscriptions.c.username]
                        else:
                            sub_username = sub.username

                        if sub_username is None or sub_username == "system":
                            subscription = sub
                            logger.info(
                                f"找到可用订阅，ID: {sub._mapping[subscriptions.c.id] if hasattr(sub, '_mapping') else sub.id}"
                            )
                            break

                    # 如果没有订阅地址，返回错误（不再触发后台生成）
                    if not subscription:
                        raise HTTPException(
                            status_code=503, detail="当前无可用订阅地址，请稍后再试"
                        )

                    # 使用订阅地址获取内容
                    try:
                        # 判断subscription是数据库对象还是我们手动创建的对象
                        if hasattr(subscription, "_mapping") and isinstance(
                            subscription._mapping, dict
                        ):
                            # 数据库对象
                            subscription_id = subscription._mapping[subscriptions.c.id]
                            subscription_email = subscription._mapping[
                                subscriptions.c.email
                            ]
                            subscription_password = subscription._mapping[
                                subscriptions.c.password
                            ]
                            subscription_token = subscription._mapping[
                                subscriptions.c.token
                            ]
                            subscription_url = f"https://{api.subscribe}/api/v1/client/subscribe?token={subscription_token}"
                        else:
                            # 手动创建的对象
                            subscription_id = subscription.id
                            subscription_email = subscription.email
                            subscription_password = subscription.password
                            subscription_token = subscription.token
                            subscription_url = f"https://{api.subscribe}/api/v1/client/subscribe?token={subscription_token}"

                        domain = urllib.parse.urlparse(subscription_url).netloc

                        logger.info(f"正在获取订阅内容，URL: {subscription_url}")

                        response = curl_requests.get(
                            subscription_url,
                            headers={
                                "User-Agent": "ClashForAndroid/2.5.9.foss",
                                "Connection": "close",
                                "Host": domain,
                                "Accept": "*/*",
                                "Accept-Encoding": "gzip, deflate, br",
                            },
                            timeout=30,  # 增加超时时间
                            allow_redirects=True,  # 允许重定向
                        )

                        # 获取响应头和内容
                        headers = dict(response.headers)
                        content = response.content  # 使用二进制内容而不是文本

                        if len(content) == 0:
                            logger.warning("警告：远程订阅接口返回了空内容！")
                            raise HTTPException(
                                status_code=500, detail="远程订阅接口返回了空内容"
                            )

                        # 不再删除订阅地址，而是更新为用户专属
                        if subscription_id > 0:
                            try:
                                # 更新订阅地址关联到当前用户
                                db.execute(
                                    subscriptions.update()
                                    .where(subscriptions.c.id == subscription_id)
                                    .values(username=user_dict["username"])
                                )
                                logger.info(
                                    f"已将订阅ID {subscription_id} 分配给用户 {user_dict['username']}"
                                )
                            except Exception as e:
                                logger.error(f"更新订阅地址关联时出错: {str(e)}")

                        # 扣除用户积分
                        new_balance = user_dict["balance"] - price
                        db.execute(
                            users.update()
                            .where(users.c.username == user_dict["username"])
                            .values(balance=new_balance)
                        )
                        # 替换内容中的"宝贝云"为"可达鸭"
                        try:
                            # 尝试解码内容
                            decoded_content = content.decode("utf-8", errors="replace")
                            # 替换文本
                            modified_content = decoded_content.replace(
                                "宝贝云", "可达鸭"
                            )
                            # 替换内容中的"t.me/bbyvpn"为"t.me/jpspjlq"
                            modified_content = modified_content.replace(
                                "t.me/bbyvpn", "t.me/jpspjlq"
                            )
                            # 替换内容中的"bit.ly/bbyvpn"为"kedaya.xyz"
                            modified_content = modified_content.replace(
                                "bit.ly/bbyvpn", "kedaya.xyz"
                            )
                            # 重新编码
                            content = modified_content.encode("utf-8")
                        except Exception as e:
                            logger.error(f"内容替换：替换内容时出错: {str(e)}")
                        # 创建直接返回二进制内容的响应
                        content_type = response.headers.get(
                            "Content-Type", "text/plain"
                        )

                        R_headers = {}
                        # 复制原始响应头
                        for k, v in headers.items():
                            if k.lower() != "content-length":
                                if k == "subscription-userinfo":
                                    R_headers[k] = v
                                if k == "strict-transport-security":
                                    R_headers[k] = v
                                if k == "profile-update-interval":
                                    R_headers[k] = v

                        # 设置Content-Disposition头
                        R_headers["Content-Disposition"] = (
                            "attachment;filename*=UTF-8''%E5%8F%AF%E8%BE%BE%E9%B8%AD"
                        )
                        R_headers["Profile-Web-Page-Url"] = "https://www.kedaya.xyz"
                        R_headers["Access-Control-Allow-Origin"] = "*"

                        # 确保内容类型正确
                        if "text/html" in content_type:
                            R_headers["Content-Type"] = "text/html; charset=UTF-8"
                        elif "application/json" in content_type:
                            R_headers["Content-Type"] = (
                                "application/json; charset=UTF-8"
                            )
                        elif "text/plain" in content_type:
                            R_headers["Content-Type"] = "text/plain; charset=UTF-8"

                        final_response = Response(
                            content=content,
                            media_type=content_type,
                            headers=R_headers,
                        )
                        return final_response
                    except HTTPException:
                        raise
                    except Exception as e:
                        logger.error(f"获取订阅内容时出错: {str(e)}")
                        import traceback

                        traceback.print_exc()
                        raise HTTPException(
                            status_code=500, detail=f"获取订阅内容时出错: {str(e)}"
                        )

        return do_get_subscription()
