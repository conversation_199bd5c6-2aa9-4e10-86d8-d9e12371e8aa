#!/bin/bash

# KedayaAPI Docker 启动脚本

# 默认使用开发环境
COMPOSE_FILE="docker-compose.yml"
ENV_NAME="开发"

# 检查命令行参数
if [ "$1" = "prod" ] || [ "$1" = "production" ]; then
    COMPOSE_FILE="docker-compose.prod.yml"
    ENV_NAME="生产"
fi

echo "🚀 启动 KedayaAPI Docker ${ENV_NAME}环境..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查 docker-compose 是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装"
    exit 1
fi

# 停止并删除现有容器
echo "🛑 停止现有容器..."
docker-compose -f $COMPOSE_FILE down

# 构建并启动服务
echo "🔨 构建并启动${ENV_NAME}服务..."
docker-compose -f $COMPOSE_FILE up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose -f $COMPOSE_FILE ps

# 初始化数据库
echo "🗄️ 初始化数据库..."
sleep 5
curl -s "http://localhost:8000/api/admin/init-db?admin_key=admin-secret-key" || echo "数据库初始化可能需要手动执行"

echo ""
echo "✅ ${ENV_NAME}环境服务启动完成！"
echo ""
echo "🌐 访问地址："
if [ "$COMPOSE_FILE" = "docker-compose.yml" ]; then
    echo "  前端开发服务器: http://localhost:3000"
    echo "  后端API服务:    http://localhost:8000"
    echo "  Nginx代理:      http://localhost:80"
else
    echo "  生产环境网站:   http://localhost:80"
    echo "  后端API服务:    http://localhost:8000"
fi
echo "  PostgreSQL:     localhost:5432"
echo ""
echo "📝 查看日志："
echo "  docker-compose -f $COMPOSE_FILE logs -f [service_name]"
echo ""
echo "🛑 停止服务："
echo "  docker-compose -f $COMPOSE_FILE down"
echo ""
echo "💡 使用说明："
echo "  开发环境: ./docker-start.sh"
echo "  生产环境: ./docker-start.sh prod"
