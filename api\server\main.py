from fastapi import FastAPI, Response, Request, Body, HTTPException
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
from email_module import router as email_router
import psutil
import os
import signal
import sys
from curl_cffi import requests
from pydantic import BaseModel
from typing import Optional


# 创建FastAPI实例
app = FastAPI(
    title="电子邮件管理API",
    description="用于创建和管理电子邮件账号的API服务",
    version="1.0.0",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该替换为特定的域
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(email_router)


@app.get("/")
async def index():
    return "请手动前往 https://www.kedaya.xyz/"


@app.get("/api/v1/subscription")
async def porxy(token: str = ""):
    url = f"https://www.kedaya.xyz/api/v1/subscription?token={token}"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "upgrade-insecure-requests": "1",
        "sec-fetch-site": "none",
        "sec-fetch-mode": "navigate",
        "sec-fetch-user": "?1",
        "sec-fetch-dest": "document",
        "accept-language": "zh-CN,zh;q=0.9",
        "priority": "u=0, i",
    }

    try:
        response = requests.get(url, headers=headers, timeout=60)
    except requests.exceptions.Timeout:
        raise HTTPException(status_code=504, detail="请求超时，目标服务器响应缓慢")
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=502, detail=f"请求失败: {str(e)}")

    return Response(
        content=response.text,
        status_code=response.status_code,
        headers=dict(response.headers),
        media_type=response.headers.get("content-type", "text/plain"),
    )


async def telegram_bot(
    bot_token: str, user_id: str, title: str, content: str, api_host: str = None
) -> dict:
    """
    使用 telegram 机器人 推送消息。
    """
    print("tg 服务启动")

    if api_host:
        url = f"{api_host}/bot{bot_token}/sendMessage"
    else:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    payload = {
        "chat_id": str(user_id),
        "text": f"{title}\n\n{content}",
        "disable_web_page_preview": "true",
    }

    try:
        response = requests.post(
            url=url, headers=headers, params=payload, timeout=10
        ).json()

        if response["ok"]:
            print("tg 推送成功！")
            return {"success": True, "message": "tg 推送成功！"}
        else:
            print("tg 推送失败！")
            return {
                "success": False,
                "message": "tg 推送失败！",
                "error": response.get("description", "未知错误"),
            }
    except Exception as e:
        print(f"tg 推送出错: {str(e)}")
        return {"success": False, "message": "tg 推送出错", "error": str(e)}


# 定义Telegram消息的请求模型
class TelegramMessage(BaseModel):
    bot_token: str
    user_id: str
    title: str
    content: str
    api_host: Optional[str] = None


@app.post("/api/telegram/send")
async def send_telegram_message(message: TelegramMessage):
    """
    发送 Telegram 消息的 API 端点，接收JSON格式的参数
    """
    if not message.bot_token or not message.user_id:
        raise HTTPException(
            status_code=400, detail="缺少必要参数：bot_token 或 user_id"
        )

    result = await telegram_bot(
        bot_token=message.bot_token,
        user_id=message.user_id,
        title=message.title,
        content=message.content,
        api_host=message.api_host,
    )

    if result["success"]:
        return {"status": "success", "message": result["message"]}
    else:
        raise HTTPException(status_code=500, detail=result["message"])


def kill_process_on_port(port):
    """
    关闭占用指定端口的进程
    """
    try:
        for proc in psutil.process_iter():
            try:
                connections = proc.net_connections()
                for conn in connections:
                    if hasattr(conn, "laddr") and conn.laddr.port == port:
                        print(
                            f"发现端口 {port} 被进程 {proc.pid} ({proc.name()}) 占用，正在关闭..."
                        )
                        if sys.platform == "win32":
                            os.kill(proc.pid, signal.SIGTERM)
                        else:
                            os.kill(proc.pid, signal.SIGKILL)
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
    except Exception as e:
        print(f"关闭进程时出错: {str(e)}")
    return False


# 启动服务器
if __name__ == "__main__":
    PORT = int(os.getenv("PORT", "7777"))
    print(f"正在检查端口 {PORT} 是否被占用...")
    if kill_process_on_port(PORT):
        print(f"已关闭占用端口 {PORT} 的进程")
    uvicorn.run(app, host="0.0.0.0", port=PORT)
