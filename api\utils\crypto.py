import base64
import gzip
from io import BytesIO
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from Crypto.Cipher import AES
    from Crypto.Util.Padding import unpad
except:
    import pip

    pip.main(["install", "pycryptodome"])
    from Crypto.Cipher import AES
    from Crypto.Util.Padding import unpad


def decrypt_aes(encrypted_text: str) -> str:
    """
    AES/CBC/PKCS7 解密函数
    :param encrypted_text: base64编码的加密文本
    :return: 解密后的文本
    """
    try:
        # 密钥和IV
        key = "TyK7vWWmnYdm2av7"
        iv = "1445263737702655"

        # base64解码
        encrypted_data = base64.b64decode(encrypted_text)

        # 创建AES解密器
        cipher = AES.new(key.encode("utf-8"), AES.MODE_CBC, iv.encode("utf-8"))

        # 解密
        decrypted_data = cipher.decrypt(encrypted_data)

        # 去除PKCS7填充
        unpadded_data = unpad(decrypted_data, AES.block_size)

        # 转换为字符串
        return unpadded_data.decode("utf-8")
    except Exception as e:
        logger.error(f"加密解密：解密失败: {str(e)}")
        return None


def yt(encrypted_text, key):
    def decrypt_aes(encrypted_text, key):
        # 创建AES解密器 (ECB模式)
        cipher = AES.new(key.encode("utf-8"), AES.MODE_ECB)

        # 解密
        try:
            # 解密base64数据
            encrypted_bytes = base64.b64decode(encrypted_text)
            decrypted_bytes = cipher.decrypt(encrypted_bytes)

            # 移除PKCS7填充
            padding_len = decrypted_bytes[-1]
            if padding_len > 16 or padding_len < 1:  # 检查填充长度是否合理
                raise ValueError("不正确的填充")

            # 检查所有填充字节是否一致
            for i in range(1, padding_len + 1):
                if decrypted_bytes[-i] != padding_len:
                    raise ValueError("填充验证失败")

            decrypted_bytes = decrypted_bytes[:-padding_len]

            # 检查是否是gzip格式 (1f8b开头)
            if (
                len(decrypted_bytes) > 2
                and decrypted_bytes[0] == 0x1F
                and decrypted_bytes[1] == 0x8B
            ):
                return decompress_gzip(decrypted_bytes)
            else:
                # 如果不是压缩格式，直接返回解密结果
                return decrypted_bytes.decode("utf-8", errors="replace")
        except Exception as e:
            raise Exception(f"解密失败: {str(e)}")

    def decompress_gzip(byte_array):
        try:
            with BytesIO(byte_array) as f:
                with gzip.GzipFile(fileobj=f, mode="rb") as g:
                    decompressed_data = g.read()
            return decompressed_data.decode("utf-8")
        except Exception as e:
            raise Exception(f"gzip解压缩失败: {str(e)}")

    decrypted_text = decrypt_aes(encrypted_text, key)

    # 移除首尾的双引号（如果存在）
    if decrypted_text and isinstance(decrypted_text, str):
        if decrypted_text[0] == '"':
            decrypted_text = decrypted_text[1:]
        if decrypted_text[-1] == '"':
            decrypted_text = decrypted_text[:-1]

    return decrypted_text
