<template>
    <div class="modal" @click="closeModal">
        <div class="modal-content auth-modal" @click.stop>
            <div class="modal-header">
                <h2>{{ type === 'login' ? '登录' : '注册' }}</h2>
                <button class="modal-close" @click="closeModal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <form class="auth-form" @submit.prevent="handleAuth">
                    <div class="form-group">
                        <label>邮箱</label>
                        <input type="email" v-model="form.email" required placeholder="请输入QQ邮箱">
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" v-model="form.password" required>
                    </div>
                    <div class="form-group" v-if="type === 'register'">
                        <label>邀请码</label>
                        <input type="text" v-model="form.inviteCode" placeholder="选填，填写邀请人邮箱">
                    </div>
                    <!-- 添加验证码输入框和发送验证码按钮 -->
                    <div class="form-group verification-code-group" v-if="type === 'register'">
                        <label>验证码</label>
                        <div class="verification-code-input">
                            <input type="text" v-model="form.verificationCode" required placeholder="请输入验证码">
                            <LoadingButton class="send-code-btn" :loading="sendingCode" 
                                           :disabled="!form.email || cooldown > 0 || sendingCode" 
                                           @click.prevent="sendVerificationCode">
                                {{ cooldown > 0 ? `${cooldown}秒后重新发送` : '发送验证码' }}
                            </LoadingButton>
                        </div>
                    </div>
                    <!-- 添加 Turnstile 组件 -->
                    <div class="turnstile-container">
                        <div id="turnstile-widget"></div>
                    </div>
                    <LoadingButton class="submit-btn" :loading="loading" type="submit">
                        {{ type === 'login' ? '登录' : '注册' }}
                    </LoadingButton>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { useStore } from 'vuex'
import LoadingButton from '../LoadingButton.vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
    type: {
        type: String,
        required: true,
        validator: value => ['login', 'register'].includes(value)
    },
    loading: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['close', 'submit'])

const form = ref({
    email: '',
    password: '',
    inviteCode: '',
    verificationCode: ''
})

const turnstileToken = ref('')
const turnstileLoaded = ref(false)
const sendingCode = ref(false)
const cooldown = ref(0)
let cooldownTimer = null

const closeModal = () => {
    emit('close')
}

const handleAuth = () => {
    if (!form.value.email.toLowerCase().includes('qq.com')) {
        ElMessage.error('只支持QQ邮箱注册')
        return
    }

    if (!turnstileToken.value) {
        ElMessage.error('请完成人机验证')
        return
    }

    if (props.type === 'register' && !form.value.verificationCode) {
        ElMessage.error('请输入验证码')
        return
    }

    emit('submit', {
        ...form.value,
        turnstileToken: turnstileToken.value
    })
}

// 发送验证码函数
const sendVerificationCode = async () => {
    if (!form.value.email) {
        ElMessage.error('请先填写邮箱')
        return
    }

    if (!form.value.email.toLowerCase().includes('qq.com')) {
        ElMessage.error('只支持QQ邮箱注册')
        return
    }

    if (!turnstileToken.value) {
        ElMessage.error('请完成人机验证')
        return
    }

    try {
        sendingCode.value = true
        const response = await fetch('/api/send-verification-code', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: form.value.email,
                turnstileToken: turnstileToken.value
            })
        })

        const data = await response.json()

        if (response.ok) {
            ElMessage.success(data.message || '验证码发送成功，请查收邮件')
            // 开始倒计时
            cooldown.value = 60
            cooldownTimer = setInterval(() => {
                cooldown.value--
                if (cooldown.value <= 0) {
                    clearInterval(cooldownTimer)
                }
            }, 1000)
        } else {
            ElMessage.error(data.detail || '验证码发送失败')
        }
    } catch (error) {
        console.error('发送验证码出错:', error)
        ElMessage.error('网络错误，请稍后重试')
    } finally {
        sendingCode.value = false
    }
}

// 加载 Turnstile 脚本
const loadTurnstile = () => {
    if (window.turnstile) {
        turnstileLoaded.value = true
        initTurnstile()
        return
    }

    const script = document.createElement('script')
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js'
    script.async = true
    script.defer = true
    script.onload = () => {
        turnstileLoaded.value = true
        initTurnstile()
    }
    document.head.appendChild(script)
}

// 重新渲染 Turnstile 组件
const resetTurnstile = () => {
    if (!window.turnstile) return

    const widget = document.getElementById('turnstile-widget')
    if (widget) {
        // 清空容器内容
        widget.innerHTML = ''
        // 重置token
        turnstileToken.value = ''
    }
}

// 初始化 Turnstile
const initTurnstile = () => {
    if (!window.turnstile) return

    // 先重置现有实例
    resetTurnstile()

    // 获取当前主题
    const store = useStore()
    const isDarkTheme = store.getters.currentTheme === 'dark'

    // 根据当前主题设置Turnstile主题
    const turnstileTheme = isDarkTheme ? 'dark' : 'light'

    try {
        window.turnstile.render('#turnstile-widget', {
            sitekey: '0x4AAAAAABAsD1ldDuzN75My',
            theme: turnstileTheme,
            callback: function (token) {
                turnstileToken.value = token
            },
        })
    } catch (error) {
        console.error('Turnstile 初始化失败:', error)
        // 如果初始化失败，稍后重试
        setTimeout(() => {
            initTurnstile()
        }, 500)
    }
}

// 监听模态框的打开，重新加载 Turnstile
onMounted(() => {
    loadTurnstile()

    // 创建store实例
    const store = useStore()

    // 监听主题变化
    watch(() => store.getters.currentTheme, () => {
        if (turnstileLoaded.value) {
            // 重新初始化 Turnstile
            setTimeout(() => {
                initTurnstile()
            }, 100)
        }
    })
})

watch(() => props.type, () => {
    // 重置表单
    form.value = { email: '', password: '', inviteCode: '', verificationCode: '' }
    // 重置 Turnstile
    turnstileToken.value = ''
    if (turnstileLoaded.value) {
        setTimeout(() => {
            initTurnstile()
        }, 100)
    }
    // 清除倒计时
    clearInterval(cooldownTimer)
    cooldown.value = 0
})
</script>

<style scoped>
.verification-code-group {
    margin-bottom: 15px;
}

.verification-code-input {
    display: flex;
    gap: 10px;
}

.verification-code-input input {
    flex: 1;
}

.send-code-btn {
    white-space: nowrap;
    font-size: 0.9rem;
    padding: 0 12px;
}
</style> 