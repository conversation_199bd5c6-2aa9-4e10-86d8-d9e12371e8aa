<template>
    <div class="modal" @click="closeModal">
        <div class="modal-content user-modal" @click.stop>
            <div class="modal-header">
                <h2>用户信息</h2>
                <button class="modal-close" @click="closeModal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="user-details">
                    <p>
                        <strong>邮箱</strong>
                        <span>{{ userInfo?.email }}</span>
                    </p>
                    <p>
                        <strong>余额</strong>
                        <span>{{ userInfo?.balance }}</span>
                        <LoadingButton class="recharge-btn" @click="showCardModal" style="margin-left: 10px;">卡密充值</LoadingButton>
                    </p>
                    <p>
                        <strong>邀请人</strong>
                        <span>{{ userInfo?.inviter || '无' }}</span>
                    </p>
                    <p>
                        <strong>邀请收益</strong>
                        <span>{{ userInfo?.invite_reward || 0 }}</span>
                    </p>
                    <div>
                        <strong>Token</strong>
                        <pre class="token-container" @click="copyToken"><code>{{ userInfo?.token }}</code></pre>
                    </div>
                    <div class="button-group">
                        <LoadingButton class="reset-token-btn" @click="handleResetToken" :loading="loading.resetToken">
                            重置 Token
                        </LoadingButton>
                        <LoadingButton class="logout-btn" @click="handleLogout" :loading="loading.logout">
                            退出登录
                        </LoadingButton>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import LoadingButton from '../LoadingButton.vue'
import { ElMessage, ElTag } from 'element-plus'
import { useStore } from 'vuex'

const props = defineProps({
    userInfo: {
        type: Object,
        required: true
    },
    loading: {
        type: Object,
        default: () => ({
            resetToken: false,
            logout: false
        })
    }
})

const emit = defineEmits(['close', 'resetToken', 'logout', 'showCardModal', 'copyToken'])

const store = useStore()

// 获取当前主题
const isDarkTheme = computed(() => {
    return store.getters.currentTheme === 'dark'
})

const closeModal = () => {
    emit('close')
}

const handleResetToken = () => {
    emit('resetToken')
}

const handleLogout = () => {
    emit('logout')
}

const showCardModal = () => {
    emit('showCardModal')
}

const copyToken = () => {
    emit('copyToken')
}
</script> 