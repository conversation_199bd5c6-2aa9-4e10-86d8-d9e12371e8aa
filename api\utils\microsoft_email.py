from curl_cffi import requests
import hashlib
import base64
import secrets
import uuid
import json
import re
import logging
from urllib.parse import urlencode, parse_qs, urlparse
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)


class MicrosoftEmailClient:
    """微软邮箱客户端，实现OAuth2认证和邮件获取功能"""

    def __init__(self, proxy: Optional[str] = None):
        """
        初始化微软邮箱客户端

        Args:
            proxy: 代理地址，格式为 "host:port"
        """
        self.proxy = proxy
        # 使用curl_cffi创建session，模拟Chrome浏览器
        self.session = requests.Session(impersonate="chrome110")

        # 设置代理
        if proxy:
            self.session.proxies = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}",
            }

        # 设置通用请求头
        self.session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
                "Accept-Language": "zh-CN,zh;q=0.9",
            }
        )

        # OAuth2 配置
        self.client_id = "9199bf20-a13f-4107-85dc-02114787ef48"
        self.redirect_uri = "https://outlook.live.com/mail/"
        self.scope = "https://outlook.office.com/.default openid profile offline_access"

        # 认证相关变量
        self.code_verifier = None
        self.code_challenge = None
        self.guid = None
        self.cookies = {}
        self.access_token = None
        self.refresh_token = None
        self.id_token = None
        self.puid = None
        self.property_set_id = None

    def generate_code_verifier(self) -> str:
        """
        生成code_verifier，用于OAuth2 PKCE流程

        Returns:
            Base64URL编码的随机字符串
        """
        # 生成32字节的随机数据
        random_bytes = secrets.token_bytes(32)

        # 转换为Base64URL编码（移除填充字符，替换特殊字符）
        code_verifier = (
            base64.urlsafe_b64encode(random_bytes).decode("utf-8").rstrip("=")
        )

        self.code_verifier = code_verifier
        return code_verifier

    def generate_code_challenge(self, code_verifier: str) -> str:
        """
        生成code_challenge，用于OAuth2 PKCE流程

        Args:
            code_verifier: 之前生成的code_verifier

        Returns:
            SHA256哈希后的Base64URL编码字符串
        """
        # 计算SHA256哈希
        sha256_hash = hashlib.sha256(code_verifier.encode("utf-8")).digest()

        # 转换为Base64URL编码
        code_challenge = (
            base64.urlsafe_b64encode(sha256_hash).decode("utf-8").rstrip("=")
        )

        self.code_challenge = code_challenge
        return code_challenge

    def generate_guid(self) -> str:
        """
        生成GUID

        Returns:
            小写的GUID字符串
        """
        guid = str(uuid.uuid4()).lower()
        self.guid = guid
        return guid

    def get_authorization_url(self) -> str:
        """
        获取OAuth2授权URL和初始cookie

        Returns:
            授权URL字符串，如果失败返回None
        """
        try:
            # 生成必要的参数
            if not self.code_verifier:
                self.generate_code_verifier()
            if not self.code_challenge:
                self.generate_code_challenge(self.code_verifier)
            if not self.guid:
                self.generate_guid()

            # 构建state参数
            state_data = {"id": self.guid, "meta": {"interactionType": "silent"}}
            state_json = json.dumps(state_data)
            state_encoded = base64.b64encode(state_json.encode("utf-8")).decode("utf-8")

            # 构建授权URL参数
            auth_params = {
                "client_id": self.client_id,
                "scope": self.scope,
                "redirect_uri": self.redirect_uri,
                "client-request-id": self.guid,
                "response_mode": "fragment",
                "client_info": "1",
                "prompt": "select_account",
                "nonce": self.guid,
                "state": state_encoded,
                "claims": '{"access_token":{"xms_cc":{"values":["CP1"]}}}',
                "x-client-SKU": "msal.js.browser",
                "x-client-VER": "4.12.0",
                "response_type": "code",
                "code_challenge": self.code_challenge,
                "code_challenge_method": "S256",
                "cobrandid": "ab0455a0-8d03-46b9-b18b-df2f57b9e44c",
                "fl": "dob,flname,wld",
                "sso_reload": "true",
            }

            # 构建完整的授权URL
            auth_url = f"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?{urlencode(auth_params)}"

            # 发送请求获取cookie和重定向URL
            headers = {
                "Host": "login.microsoftonline.com",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Sec-Fetch-Site": "cross-site",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Dest": "document",
                "Referer": "https://outlook.live.com/",
            }

            response = self.session.get(
                auth_url, headers=headers, allow_redirects=False
            )

            if response.status_code not in [200, 302]:
                logger.error(f"获取授权URL失败，状态码: {response.status_code}")
                return None

            # 更新cookies
            self.cookies.update(response.cookies.get_dict())

            # 从响应中提取GitHub Fed URL
            if response.status_code == 200:
                content = response.text
                # 查找urlGitHubFed参数
                match = re.search(r'"urlGitHubFed":"([^"]+)"', content)
                if match:
                    github_fed_url = match.group(1)
                    # 解码URL中的转义字符
                    github_fed_url = github_fed_url.replace("\\u0026", "&")
                    return github_fed_url

            logger.error("未能从响应中提取GitHub Fed URL")
            return None

        except Exception as e:
            logger.error(f"获取授权URL时发生错误: {str(e)}")
            return None

    def submit_email(self, auth_url: str, email: str) -> Tuple[bool, str, str]:
        """
        提交邮箱地址

        Args:
            auth_url: 授权URL
            email: 邮箱地址

        Returns:
            (成功标志, 下一步URL, PPFT参数)
        """
        try:
            # 构建请求URL
            request_url = f"{auth_url}&jsh=&jshp=&username={urlencode({'': email})[1:]}&login_hint={urlencode({'': email})[1:]}"

            headers = {
                "Host": "login.live.com",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Sec-Fetch-Site": "cross-site",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-User": "?1",
                "Sec-Fetch-Dest": "document",
                "Referer": "https://login.microsoftonline.com/",
            }

            response = self.session.get(request_url, headers=headers)

            if response.status_code != 200:
                logger.error(f"提交邮箱失败，状态码: {response.status_code}")
                return False, "", ""

            # 更新cookies
            self.cookies.update(response.cookies.get_dict())

            # 提取PPFT参数
            ppft_match = re.search(r'name="PPFT".*?value="([^"]+)"', response.text)
            if not ppft_match:
                logger.error("未能从响应中提取PPFT参数")
                return False, "", ""

            ppft = ppft_match.group(1)

            # 提取下一步URL
            url_match = re.search(r"urlPost:'([^']+)'", response.text)
            if not url_match:
                logger.error("未能从响应中提取下一步URL")
                return False, "", ""

            next_url = url_match.group(1)

            return True, next_url, ppft

        except Exception as e:
            logger.error(f"提交邮箱时发生错误: {str(e)}")
            return False, "", ""

    def submit_password(
        self, url: str, email: str, password: str, ppft: str
    ) -> Tuple[bool, str, str, Dict[str, str]]:
        """
        提交密码

        Args:
            url: 提交密码的URL
            email: 邮箱地址
            password: 密码
            ppft: PPFT参数

        Returns:
            (成功标志, 下一步URL, 新的PPFT参数, 安全参数字典)
        """
        try:
            headers = {
                "Host": "login.live.com",
                "Connection": "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded",
                "Origin": "https://login.live.com",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-User": "?1",
                "Sec-Fetch-Dest": "document",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            }

            data = {
                "ps": "2",
                "psRNGCDefaultType": "",
                "psRNGCEntropy": "",
                "psRNGCSLK": "",
                "canary": "",
                "ctx": "",
                "hpgrequestid": "",
                "PPFT": ppft,
                "PPSX": "Passpo",
                "NewUser": "1",
                "FoundMSAs": "",
                "fspost": "0",
                "i21": "0",
                "CookieDisclosure": "0",
                "IsFidoSupported": "1",
                "isSignupPost": "0",
                "isRecoveryAttemptPost": "0",
                "i13": "0",
                "login": email,
                "loginfmt": email,
                "type": "11",
                "LoginOptions": "3",
                "lrt": "",
                "lrtPartition": "",
                "hisRegion": "",
                "hisScaleUnit": "",
                "passwd": password,
            }

            response = self.session.post(url, headers=headers, data=data)

            if response.status_code != 200:
                logger.error(f"提交密码失败，状态码: {response.status_code}")
                return False, "", "", {}

            # 更新cookies
            self.cookies.update(response.cookies.get_dict())

            # 提取新的PPFT参数
            ppft_match = re.search(r",sFT:'([^']+)'", response.text)
            new_ppft = ppft_match.group(1) if ppft_match else ""

            # 提取下一步URL
            url_match = re.search(r"urlPost:'([^']+)'", response.text)
            next_url = url_match.group(1) if url_match else ""

            # 如果没有找到常规的URL，可能需要处理安全保护
            security_params = {}
            if not next_url:
                url_match = re.search(r'fmHF" action="([^"]+)"', response.text)
                next_url = url_match.group(1) if url_match else ""

                # 提取安全参数
                pprid_match = re.search(r'pprid" value="([^"]+)"', response.text)
                ipt_match = re.search(r'ipt" value="([^"]+)"', response.text)
                uaid_match = re.search(r'uaid" value="([^"]+)"', response.text)

                if pprid_match and ipt_match and uaid_match:
                    security_params = {
                        "pprid": pprid_match.group(1),
                        "ipt": ipt_match.group(1),
                        "uaid": uaid_match.group(1),
                    }

            return True, next_url, new_ppft, security_params

        except Exception as e:
            logger.error(f"提交密码时发生错误: {str(e)}")
            return False, "", "", {}

    def get_authorization_code(self, url: str, ppft: str) -> Tuple[bool, str]:
        """
        获取授权码

        Args:
            url: 获取授权码的URL
            ppft: PPFT参数

        Returns:
            (成功标志, 授权码)
        """
        try:
            headers = {
                "Host": "login.live.com",
                "Connection": "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded",
                "Origin": "https://login.live.com",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-User": "?1",
                "Sec-Fetch-Dest": "document",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            }

            data = {
                "PPFT": ppft,
                "canary": "",
                "LoginOptions": "3",
                "type": "28",
                "hpgrequestid": "",
                "ctx": "",
            }

            response = self.session.post(
                url, headers=headers, data=data, allow_redirects=False
            )

            if response.status_code not in [302, 200]:
                logger.error(f"获取授权码失败，状态码: {response.status_code}")
                return False, ""

            # 从Location头中提取授权码
            location = response.headers.get("Location", "")
            if location:
                # 解析URL中的code参数
                parsed_url = urlparse(location)
                if parsed_url.fragment:
                    # 处理fragment中的参数
                    fragment_params = parse_qs(parsed_url.fragment)
                    if "code" in fragment_params:
                        code = fragment_params["code"][0]
                        return True, code

                # 处理query中的参数
                query_params = parse_qs(parsed_url.query)
                if "code" in query_params:
                    code = query_params["code"][0]
                    return True, code

            logger.error("未能从响应中提取授权码")
            return False, ""

        except Exception as e:
            logger.error(f"获取授权码时发生错误: {str(e)}")
            return False, ""

    def get_refresh_token(self, code: str) -> Tuple[bool, str]:
        """
        获取refresh_token（获取token第一步）

        Args:
            code: 授权码

        Returns:
            (成功标志, refresh_token)
        """
        try:
            if not self.guid:
                self.generate_guid()

            if not self.code_verifier:
                logger.error("code_verifier未初始化")
                return False, ""

            headers = {
                "Host": "login.microsoftonline.com",
                "Connection": "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                "Accept": "*/*",
                "Origin": "https://outlook.live.com",
                "Sec-Fetch-Site": "cross-site",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": "https://outlook.live.com/",
            }

            data = {
                "client_id": self.client_id,
                "redirect_uri": self.redirect_uri,
                "scope": self.scope,
                "code": code,
                "x-client-SKU": "msal.js.browser",
                "x-client-VER": "4.12.0",
                "x-ms-lib-capability": "retry-after, h429",
                "x-client-current-telemetry": "5|865,0,,,|,,",
                "x-client-last-telemetry": "5|0|863,d657f8c7-b5e9-43c6-f820-1d1906605ffe|login_required|1,0",
                "code_verifier": self.code_verifier,
                "grant_type": "authorization_code",
                "client_info": "1",
                "claims": '{"access_token":{"xms_cc":{"values":["CP1"]}}}',
                "X-AnchorMailbox": "Oid:00000000-0000-0000-4206-bfbf6f606ea8@9188040d-6c67-4c5b-b112-36a304b66dad",
            }

            url = f"https://login.microsoftonline.com/common/oauth2/v2.0/token?client-request-id={self.generate_guid()}"

            # 尝试最多3次
            for _ in range(3):
                response = self.session.post(url, headers=headers, data=data)

                if response.status_code == 200:
                    break

                time.sleep(1)

            if response.status_code != 200:
                logger.error(f"获取refresh_token失败，状态码: {response.status_code}")
                return False, ""

            # 解析响应JSON
            try:
                response_json = response.json()
                refresh_token = response_json.get("refresh_token", "")

                if not refresh_token:
                    logger.error("响应中未包含refresh_token")
                    return False, ""

                self.refresh_token = refresh_token
                return True, refresh_token

            except ValueError:
                logger.error("解析响应JSON失败")
                return False, ""

        except Exception as e:
            logger.error(f"获取refresh_token时发生错误: {str(e)}")
            return False, ""

    def get_access_token(self, refresh_token: str = None) -> Tuple[bool, str, str]:
        """
        获取access_token和id_token（获取token第二步）

        Args:
            refresh_token: 可选的refresh_token，如果不提供则使用实例中存储的

        Returns:
            (成功标志, access_token, id_token)
        """
        try:
            if not refresh_token and not self.refresh_token:
                logger.error("refresh_token未提供且实例中未存储")
                return False, "", ""

            if not refresh_token:
                refresh_token = self.refresh_token

            if not self.guid:
                self.generate_guid()

            headers = {
                "Host": "login.microsoftonline.com",
                "Connection": "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                "Accept": "*/*",
                "Origin": "https://outlook.live.com",
                "Sec-Fetch-Site": "cross-site",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": "https://outlook.live.com/",
            }

            data = {
                "client_id": self.client_id,
                "redirect_uri": "https://outlook.live.com/mail/oauthRedirect.html",
                "scope": "service::outlook.office.com::MBI_SSL openid profile offline_access",
                "grant_type": "refresh_token",
                "client_info": "1",
                "x-client-SKU": "msal.js.browser",
                "x-client-VER": "4.12.0",
                "x-ms-lib-capability": "retry-after, h429",
                "x-client-current-telemetry": "5|61,0,,,|,,",
                "x-client-last-telemetry": "5|0|||0,0",
                "refresh_token": refresh_token,
                "claims": '{"access_token":{"xms_cc":{"values":["CP1"]}}}',
                "X-AnchorMailbox": "Oid:00000000-0000-0000-4206-bfbf6f606ea8@9188040d-6c67-4c5b-b112-36a304b66dad",
            }

            url = f"https://login.microsoftonline.com/consumers/oauth2/v2.0/token?client-request-id={self.generate_guid()}"

            # 尝试最多3次
            for _ in range(3):
                response = self.session.post(url, headers=headers, data=data)

                if response.status_code == 200:
                    break

                time.sleep(1)

            if response.status_code != 200:
                logger.error(f"获取access_token失败，状态码: {response.status_code}")
                return False, "", ""

            # 解析响应JSON
            try:
                response_json = response.json()
                access_token = response_json.get("access_token", "")
                id_token = response_json.get("id_token", "")

                if not access_token or not id_token:
                    logger.error("响应中未包含access_token或id_token")
                    return False, "", ""

                self.access_token = access_token
                self.id_token = id_token

                # 从id_token中提取PUID
                try:
                    # id_token是一个JWT，分为三部分：header.payload.signature
                    # 我们需要解码payload部分
                    payload = id_token.split(".")[1]
                    # 添加填充以便正确解码
                    payload += "=" * ((4 - len(payload) % 4) % 4)
                    decoded_payload = base64.urlsafe_b64decode(payload).decode("utf-8")
                    payload_json = json.loads(decoded_payload)

                    puid = payload_json.get("puid", "")
                    if puid:
                        self.puid = f"{puid}@84df9e7f-e9f6-40af-b435-aaaaaaaaaaaa"
                except Exception as e:
                    logger.warning(f"从id_token中提取PUID时出错: {str(e)}")

                return True, access_token, id_token

            except ValueError:
                logger.error("解析响应JSON失败")
                return False, "", ""

        except Exception as e:
            logger.error(f"获取access_token时发生错误: {str(e)}")
            return False, "", ""
