"""
微软邮箱登录缓存管理模块
针对Vercel无服务器环境优化，实现登录状态缓存以提高性能
"""

import hashlib
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Tuple
import sqlalchemy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.models import microsoft_login_cache
    from api.db import get_db, with_db_retry
    from api.utils.microsoft_email import MicrosoftEmailClient
except:
    from models import microsoft_login_cache
    from db import get_db, with_db_retry
    from utils.microsoft_email import MicrosoftEmailClient


class MicrosoftEmailCacheManager:
    """微软邮箱登录缓存管理器"""

    # 缓存有效期（1小时）
    CACHE_EXPIRY_HOURS = 1

    @staticmethod
    def _generate_account_hash(account: str, password: str) -> str:
        """
        生成账号密码的哈希值，用于缓存键

        Args:
            account: 邮箱账号
            password: 密码

        Returns:
            SHA256哈希值
        """
        combined = f"{account.lower()}:{password}"
        return hashlib.sha256(combined.encode()).hexdigest()

    @staticmethod
    @with_db_retry(max_retries=2)
    def get_cached_login(account: str, password: str, username: str) -> Optional[Dict]:
        """
        获取缓存的登录信息

        Args:
            account: 邮箱账号
            password: 密码
            username: 当前用户名

        Returns:
            缓存的登录信息字典，如果没有有效缓存则返回None
        """
        try:
            account_hash = MicrosoftEmailCacheManager._generate_account_hash(
                account, password
            )
            current_time = datetime.now(timezone.utc)

            with get_db() as db:
                # 查询有效的缓存记录
                stmt = sqlalchemy.select(microsoft_login_cache).where(
                    sqlalchemy.and_(
                        microsoft_login_cache.c.account_hash == account_hash,
                        microsoft_login_cache.c.expires_at > current_time,
                    )
                )

                result = db.execute(stmt).fetchone()

                if result:
                    # 更新最后使用时间
                    update_stmt = (
                        microsoft_login_cache.update()
                        .where(microsoft_login_cache.c.id == result.id)
                        .values(
                            last_used=current_time,
                            username=username,  # 更新使用该缓存的用户
                        )
                    )
                    db.execute(update_stmt)
                    db.commit()

                    logger.info(f"找到有效的登录缓存，账号: {account}")

                    return {
                        "refresh_token": result.refresh_token,
                        "access_token": result.access_token,
                        "id_token": result.id_token,
                        "puid": result.puid,
                        "property_set_id": result.property_set_id,
                        "cached": True,
                    }

                logger.info(f"未找到有效的登录缓存，账号: {account}")
                return None

        except Exception as e:
            logger.error(f"获取登录缓存时发生错误: {str(e)}")
            return None

    @staticmethod
    @with_db_retry(max_retries=2)
    def save_login_cache(
        account: str,
        password: str,
        username: str,
        refresh_token: str,
        access_token: str = None,
        id_token: str = None,
        puid: str = None,
        property_set_id: str = None,
    ) -> bool:
        """
        保存登录缓存

        Args:
            account: 邮箱账号
            password: 密码
            username: 用户名
            refresh_token: 刷新令牌
            access_token: 访问令牌
            id_token: ID令牌
            puid: PUID
            property_set_id: 属性集ID

        Returns:
            是否保存成功
        """
        try:
            account_hash = MicrosoftEmailCacheManager._generate_account_hash(
                account, password
            )
            current_time = datetime.now(timezone.utc)
            expires_at = current_time + timedelta(
                hours=MicrosoftEmailCacheManager.CACHE_EXPIRY_HOURS
            )

            with get_db() as db:
                # 检查是否已存在相同的缓存记录
                existing_stmt = sqlalchemy.select(microsoft_login_cache).where(
                    microsoft_login_cache.c.account_hash == account_hash
                )
                existing_result = db.execute(existing_stmt).fetchone()

                if existing_result:
                    # 更新现有记录
                    update_stmt = (
                        microsoft_login_cache.update()
                        .where(microsoft_login_cache.c.account_hash == account_hash)
                        .values(
                            account=account,
                            refresh_token=refresh_token,
                            access_token=access_token,
                            id_token=id_token,
                            puid=puid,
                            property_set_id=property_set_id,
                            login_time=current_time,
                            expires_at=expires_at,
                            last_used=current_time,
                            username=username,
                        )
                    )
                    db.execute(update_stmt)
                    logger.info(f"更新登录缓存成功，账号: {account}")
                else:
                    # 插入新记录
                    insert_stmt = microsoft_login_cache.insert().values(
                        account=account,
                        account_hash=account_hash,
                        refresh_token=refresh_token,
                        access_token=access_token,
                        id_token=id_token,
                        puid=puid,
                        property_set_id=property_set_id,
                        login_time=current_time,
                        expires_at=expires_at,
                        last_used=current_time,
                        username=username,
                    )
                    db.execute(insert_stmt)
                    logger.info(f"保存登录缓存成功，账号: {account}")

                db.commit()
                return True

        except Exception as e:
            logger.error(f"保存登录缓存时发生错误: {str(e)}")
            return False

    @staticmethod
    @with_db_retry(max_retries=2)
    def cleanup_expired_cache() -> int:
        """
        清理过期的缓存记录

        Returns:
            清理的记录数量
        """
        try:
            current_time = datetime.now(timezone.utc)

            with get_db() as db:
                # 删除过期的缓存记录
                delete_stmt = microsoft_login_cache.delete().where(
                    microsoft_login_cache.c.expires_at < current_time
                )
                result = db.execute(delete_stmt)
                db.commit()

                deleted_count = result.rowcount
                if deleted_count > 0:
                    logger.info(f"清理了 {deleted_count} 条过期的登录缓存记录")

                return deleted_count

        except Exception as e:
            logger.error(f"清理过期缓存时发生错误: {str(e)}")
            return 0

    @staticmethod
    def create_cached_client(
        cached_data: Dict, proxy: Optional[str] = None
    ) -> MicrosoftEmailClient:
        """
        使用缓存数据创建微软邮箱客户端

        Args:
            cached_data: 缓存的登录数据
            proxy: 代理地址

        Returns:
            配置好的微软邮箱客户端
        """
        client = MicrosoftEmailClient(proxy=proxy)

        # 设置缓存的认证信息
        client.refresh_token = cached_data.get("refresh_token")
        client.access_token = cached_data.get("access_token")
        client.id_token = cached_data.get("id_token")
        client.puid = cached_data.get("puid")
        client.property_set_id = cached_data.get("property_set_id")

        return client

    @staticmethod
    def refresh_access_token_if_needed(client: MicrosoftEmailClient) -> bool:
        """
        如果需要，刷新访问令牌

        Args:
            client: 微软邮箱客户端

        Returns:
            是否刷新成功
        """
        try:
            if not client.access_token and client.refresh_token:
                # 如果没有access_token但有refresh_token，尝试获取
                success, access_token, id_token = client.get_access_token(
                    client.refresh_token
                )
                if success:
                    logger.info("成功刷新访问令牌")
                    return True
                else:
                    logger.error("刷新访问令牌失败")
                    return False

            return True  # 如果已有access_token，认为是有效的

        except Exception as e:
            logger.error(f"刷新访问令牌时发生错误: {str(e)}")
            return False

    @staticmethod
    @with_db_retry(max_retries=2)
    def clear_login_cache(
        account: str, password: str, username: str = "unknown"
    ) -> bool:
        """
        清除指定账号的登录缓存
        当检测到token失效时调用

        Args:
            account: 邮箱账号
            password: 密码
            username: 用户名

        Returns:
            是否清除成功
        """
        try:
            account_hash = MicrosoftEmailCacheManager._generate_account_hash(
                account, password
            )

            with get_db() as db:
                # 删除指定账号的缓存记录
                delete_stmt = microsoft_login_cache.delete().where(
                    microsoft_login_cache.c.account_hash == account_hash
                )
                result = db.execute(delete_stmt)
                db.commit()

                deleted_count = result.rowcount
                if deleted_count > 0:
                    logger.info(f"已清除账号 {account} 的无效登录缓存")
                else:
                    logger.info(f"账号 {account} 没有找到缓存记录")

                return True

        except Exception as e:
            logger.error(f"清除登录缓存时发生错误: {str(e)}")
            return False

    @staticmethod
    @with_db_retry(max_retries=2)
    def clear_all_cache() -> int:
        """
        清除所有登录缓存
        用于调试或维护

        Returns:
            清除的记录数量
        """
        try:
            with get_db() as db:
                # 删除所有缓存记录
                delete_stmt = microsoft_login_cache.delete()
                result = db.execute(delete_stmt)
                db.commit()

                deleted_count = result.rowcount
                logger.info(f"已清除所有登录缓存，共 {deleted_count} 条记录")

                return deleted_count

        except Exception as e:
            logger.error(f"清除所有缓存时发生错误: {str(e)}")
            return 0
