# KedayaAPI 简化部署 Dockerfile
# 使用本地已构建的静态文件，避免网络问题

FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制并安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY api/ ./api/
COPY main.py ./
COPY init.sql ./

# 复制已构建的前端静态文件
COPY dist/ ./dist/

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["python", "main.py"]
