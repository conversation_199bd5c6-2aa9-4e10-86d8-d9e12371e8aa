# 空的__init__.py文件，使routes目录成为一个Python包
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    # 尝试标准导入路径
    from api.routes import (
        auth,
        email,
        ai,
        captcha,
        qrcode,
        subscription,
        card,
        admin,
        microsoft_email,
    )
except:
    from routes import (
        auth,
        email,
        ai,
        captcha,
        qrcode,
        subscription,
        card,
        admin,
        microsoft_email,
    )


# 在模块级别导出所有路由模块
__all__ = [
    "auth",
    "email",
    "ai",
    "captcha",
    "qrcode",
    "subscription",
    "card",
    "admin",
    "microsoft_email",
]
