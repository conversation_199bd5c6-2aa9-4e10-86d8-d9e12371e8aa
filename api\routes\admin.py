import os
import json
import sqlalchemy
import concurrent.futures
from fastapi import APIRouter, HTTPException, Request
import base64
import asyncio
from datetime import datetime, timezone, timedelta

try:
    from api.db import engine, cleanup_expired_records
    from api.models import subscriptions
    from api.utils.api_client import CSKedayaAPI
    from api.utils.email import cleanup_expired_html_records
except:
    from db import engine, cleanup_expired_records
    from models import subscriptions
    from utils.api_client import CSKedayaAPI
    from utils.email import cleanup_expired_html_records

router = APIRouter(prefix="/api/admin", tags=["admin"])


@router.get("/generate-subscriptions")
async def generate_subscriptions(
    admin_key: str = None, count: int = 5, threads: int = 3
):
    """强制生成新的系统订阅，删除超过4小时的旧订阅"""
    # 启动一次清理过期记录的过程
    try:
        asyncio.create_task(asyncio.to_thread(cleanup_expired_records))
        asyncio.create_task(asyncio.to_thread(cleanup_expired_html_records))
    except Exception:
        # 忽略清理错误，不影响主流程
        pass
    # 简单的管理员密钥验证
    if not admin_key or admin_key != os.getenv("ADMIN_KEY", "admin-secret-key"):
        raise HTTPException(status_code=403, detail="无效的管理员密钥")

    try:
        # 使用CSKedayaAPI生成新的订阅
        api = CSKedayaAPI(
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************._-JqecFVqEZqDZxKru_x7mN56odzHBdlwhCrvKqg09g",
            refresh_domain=True,  # 使用最新的域名配置
        )

        # 只删除超过4小时的系统订阅
        with engine.connect() as conn:
            with conn.begin():
                # 计算4小时前的时间点（使用UTC+8时区，与数据库中的时间格式一致）
                utc8_tz = timezone(timedelta(hours=8))
                four_hours_ago = datetime.now(utc8_tz) - timedelta(hours=4)

                # 删除超过4小时的系统订阅
                delete_result = conn.execute(
                    subscriptions.delete().where(
                        sqlalchemy.and_(
                            sqlalchemy.or_(
                                subscriptions.c.username is None,
                                subscriptions.c.username == "system",
                            ),
                            subscriptions.c.created_at < four_hours_ago,
                        )
                    )
                )
                print(
                    f"已删除 {delete_result.rowcount} 个超过4小时的旧订阅（创建时间早于 {four_hours_ago}）"
                )

        # 强制生成指定数量的新订阅
        results = []

        # 定义单个订阅生成任务
        def generate_single_subscription():
            return api.generate_subscription()

        # 使用线程池执行任务
        with concurrent.futures.ThreadPoolExecutor(max_workers=threads) as executor:
            # 提交所有任务
            future_to_subscription = {
                executor.submit(generate_single_subscription): i for i in range(count)
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_subscription):
                try:
                    subscription = future.result()
                    if subscription:
                        results.append(subscription)
                except Exception as e:
                    print(f"生成订阅时出错: {str(e)}")

        if results:
            return {
                "status": True,
                "message": f"强制生成 {len(results)} 个新订阅（已删除超过4小时的旧订阅），使用 {threads} 个线程",
                "data": results,
            }
        else:
            return {"status": False, "message": "生成订阅失败，未能创建任何新订阅"}
    except Exception as e:
        return {"status": False, "message": f"生成订阅失败: {str(e)}"}


@router.get("/init-db")
async def admin_init_db(admin_key: str = None):
    """管理员手动初始化数据库"""
    try:
        from api.db import init_db
    except:
        from db import init_db

    # 简单的管理员密钥验证
    if not admin_key or admin_key != os.getenv("ADMIN_KEY", "admin-secret-key"):
        raise HTTPException(status_code=403, detail="无效的管理员密钥")

    try:
        # 手动初始化数据库
        init_db()
        return {"status": True, "message": "数据库初始化成功，所有表已创建"}
    except Exception as e:
        print(f"数据库初始化失败: {str(e)}")
        return {"status": False, "message": f"数据库初始化失败: {str(e)}"}


@router.get("/domains")
async def get_domains(admin_key: str = None, refresh: bool = False):
    """获取当前使用的域名配置"""
    # 简单的管理员密钥验证
    if not admin_key or admin_key != os.getenv("ADMIN_KEY", "admin-secret-key"):
        raise HTTPException(status_code=403, detail="无效的管理员密钥")

    try:
        # 创建一个API客户端以检查当前域名
        api = CSKedayaAPI(refresh_domain=refresh)

        # 获取配置URL的内容
        config_url = "https://soon-1333812162.cos.accelerate.myqcloud.com/testoss.txt"
        response = None
        try:
            response = api.session.get(config_url, timeout=10)
            if response.status_code == 200:
                # 解码Base64内容
                decoded_content = base64.b64decode(response.text).decode("utf-8")
                config = json.loads(decoded_content)
            else:
                config = {"error": f"获取配置失败，状态码: {response.status_code}"}
        except Exception as e:
            config = {"error": f"获取配置异常: {str(e)}"}

        return {
            "status": True,
            "current_domain": api.domain,
            "default_domain": CSKedayaAPI.DEFAULT_DOMAIN,
            "refresh_performed": refresh,
            "config_url": config_url,
            "config": config if response and response.status_code == 200 else None,
        }
    except Exception as e:
        return {"status": False, "message": f"获取域名配置失败: {str(e)}"}
