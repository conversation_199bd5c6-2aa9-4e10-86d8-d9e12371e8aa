# -*- coding: utf-8 -*-
import numpy as np
from PIL import Image, ImageFilter
import io
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("geetest_gap")


class SlideCrack(object):
    """滑块验证码识别处理类"""

    def __init__(self, gap_bytes, bg_bytes):
        """
        初始化

        :param gap_bytes: 滑块图片的字节数据
        :param bg_bytes: 背景图片的字节数据
        """
        self.gap_bytes = gap_bytes
        self.bg_bytes = bg_bytes

    @staticmethod
    def bytes_to_pil_img(img_bytes):
        """
        将字节流转换为PIL图片对象

        :param img_bytes: 图片字节数据
        :return: PIL图片对象
        """
        try:
            return Image.open(io.BytesIO(img_bytes)).convert("RGB")
        except Exception as e:
            logger.error(f"转换图片数据失败: {str(e)}")
            raise

    def clear_white(self):
        """
        清除图片的空白区域，主要清除滑块的空白

        :return: 处理后的PIL图片
        """
        try:
            img = self.bytes_to_pil_img(self.gap_bytes)
            img_np = np.array(img)
            if img_np is None:
                raise Exception("无法读取滑块图片数据")

            rows, cols, channel = img_np.shape
            min_x = rows
            min_y = cols
            max_x = 0
            max_y = 0

            # 遍历图片像素，找出非空白区域的边界
            for x in range(rows):
                for y in range(cols):
                    t = set(img_np[x, y])
                    if len(t) >= 2:
                        if x < min_x:
                            min_x = x
                        if x > max_x:
                            max_x = x
                        if y < min_y:
                            min_y = y
                        if y > max_y:
                            max_y = y

            # 确保找到了有效区域
            if min_x >= max_x or min_y >= max_y:
                raise Exception("无法找到滑块的有效区域")

            img1 = img.crop((min_y, min_x, max_y, max_x))
            return img1
        except Exception as e:
            logger.error(f"清除滑块空白区域失败: {str(e)}")
            raise

    @staticmethod
    def image_edge_detection(img):
        """
        图片边缘检测，使用PIL的FIND_EDGES

        :param img: 输入PIL图片
        :return: 边缘检测后的灰度PIL图片
        """
        try:
            gray_img = img.convert("L")
            edges = gray_img.filter(ImageFilter.FIND_EDGES)
            return edges
        except Exception as e:
            logger.error(f"图片边缘检测失败: {str(e)}")
            raise

    @staticmethod
    def pil_to_gray_np(img):
        """
        PIL图片转灰度numpy数组
        """
        return np.array(img.convert("L"))

    @staticmethod
    def template_match(slide_img, bg_img):
        """
        用numpy实现模板匹配，返回最佳x坐标
        :param slide_img: PIL图片（滑块）
        :param bg_img: PIL图片（背景）
        :return: 匹配位置的x坐标
        """
        try:
            tpl = SlideCrack.pil_to_gray_np(slide_img)
            target = SlideCrack.pil_to_gray_np(bg_img)
            th, tw = tpl.shape
            h, w = target.shape
            max_corr = -1
            max_x = 0
            for x in range(w - tw + 1):
                for y in range(h - th + 1):
                    window = target[y : y + th, x : x + tw]
                    if window.shape != tpl.shape:
                        continue
                    corr = np.corrcoef(tpl.flatten(), window.flatten())[0, 1]
                    if corr > max_corr:
                        max_corr = corr
                        max_x = x
            if max_corr < 0.3:
                logger.warning(f"模板匹配可信度较低: {max_corr}")
            return max_x
        except Exception as e:
            logger.error(f"模板匹配失败: {str(e)}")
            raise

    def discern(self):
        try:
            slide_img = self.clear_white()

            slide_edge = self.image_edge_detection(slide_img)

            bg_img = self.bytes_to_pil_img(self.bg_bytes)

            bg_edge = self.image_edge_detection(bg_img)

            x = self.template_match(slide_edge, bg_edge)
            return x
        except Exception as e:
            logger.error(f"识别滑块位置失败: {str(e)}")
            raise


def get_gap(slide_bytes, bg_bytes):
    """
    根据字节流数据计算缺口位置

    :param slide_bytes: 滑块图片的字节流
    :param bg_bytes: 背景图片的字节流
    :return: 缺口位置的x坐标
    """
    try:
        sc = SlideCrack(slide_bytes, bg_bytes)
        return sc.discern()
    except Exception as e:
        logger.error(f"获取缺口位置失败: {str(e)}")
        raise


def restore_picture(oldbg_bytes, oldallbg_bytes):
    """
    还原图片

    :param oldbg_bytes: 原始背景图片的字节流
    :param oldallbg_bytes: 原始完整背景图片的字节流
    :return: 处理后的背景图片和完整背景图片的字节流
    """
    try:
        bytes_list = [oldbg_bytes, oldallbg_bytes]
        result_bytes = []

        for index, img_bytes in enumerate(bytes_list):
            try:
                image = Image.open(io.BytesIO(img_bytes))
                s = Image.new("RGBA", (260, 160))
                ut = [
                    39,
                    38,
                    48,
                    49,
                    41,
                    40,
                    46,
                    47,
                    35,
                    34,
                    50,
                    51,
                    33,
                    32,
                    28,
                    29,
                    27,
                    26,
                    36,
                    37,
                    31,
                    30,
                    44,
                    45,
                    43,
                    42,
                    12,
                    13,
                    23,
                    22,
                    14,
                    15,
                    21,
                    20,
                    8,
                    9,
                    25,
                    24,
                    6,
                    7,
                    3,
                    2,
                    0,
                    1,
                    11,
                    10,
                    4,
                    5,
                    19,
                    18,
                    16,
                    17,
                ]
                height_half = 80
                # 图片拼接还原
                for inx in range(52):
                    c = ut[inx] % 26 * 12 + 1
                    u = height_half if ut[inx] > 25 else 0
                    l_ = image.crop(box=(c, u, c + 10, u + 80))
                    s.paste(l_, box=(inx % 26 * 10, 80 if inx > 25 else 0))

                # 将图片转换为字节流
                img_byte_arr = io.BytesIO()
                s.save(img_byte_arr, format="PNG")
                result_bytes.append(img_byte_arr.getvalue())
            except Exception as e:
                logger.error(f"处理第{index}张图片失败: {str(e)}")
                raise

        # 返回处理后的背景图片和完整背景图片的字节流
        return result_bytes[0], result_bytes[1]
    except Exception as e:
        logger.error(f"还原图片失败: {str(e)}")
        raise
