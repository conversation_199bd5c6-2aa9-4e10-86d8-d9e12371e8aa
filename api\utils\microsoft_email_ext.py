"""
微软邮箱客户端扩展模块 - 邮件获取功能
"""

import json
import logging
import re
import uuid
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timezone
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

# 临时链接有效期（秒）
TEMP_LINK_EXPIRY = 600  # 10分钟

try:
    from api.models import temp_html_storage
    from api.db import get_db
except:
    try:
        from models import temp_html_storage
        from db import get_db
    except:
        # 如果无法导入，提供空函数
        def get_db():
            return None


def get_dynamic_domains():
    return "api.kedaya.xyz"


# 保存HTML内容并生成临时链接ID
def save_html_content(html_content, email_id, username=None):
    try:
        # 计算内容哈希值
        import hashlib

        content_hash = hashlib.md5(html_content.encode()).hexdigest()

        current_time = int(time.time())
        expires_at = current_time + TEMP_LINK_EXPIRY

        # 创建唯一的ID
        unique_id = str(uuid.uuid4())

        # 处理email_id长度问题 - 如果超过50个字符，使用哈希值
        processed_email_id = email_id
        if email_id and len(email_id) > 50:
            # 使用MD5哈希来缩短email_id，保留前缀以便识别
            email_hash = hashlib.md5(email_id.encode()).hexdigest()
            processed_email_id = (
                f"hash_{email_hash[:40]}"  # hash_ + 40字符哈希 = 45字符
            )
        elif email_id and len(email_id) > 45:
            # 如果长度在45-50之间，直接截断
            processed_email_id = email_id[:45]

        try:
            # 使用与email.py相同的数据库连接方式
            with get_db() as db:
                # 检查是否存在相同哈希值的记录
                existing_record = db.execute(
                    temp_html_storage.select().where(
                        temp_html_storage.c.content_hash == content_hash
                    )
                ).fetchone()

                if existing_record:
                    # 更新过期时间
                    db.execute(
                        temp_html_storage.update()
                        .where(temp_html_storage.c.id == existing_record.id)
                        .values(expires_at=expires_at)
                    )
                    db.commit()
                    return existing_record.id
                else:
                    # 存储到数据库
                    db.execute(
                        temp_html_storage.insert().values(
                            id=unique_id,
                            email_id=processed_email_id,  # 使用处理后的email_id
                            username=username,
                            content_hash=content_hash,
                            html_content=html_content,
                            created_at=current_time,
                            expires_at=expires_at,
                        )
                    )
                    db.commit()
                    return unique_id
        except Exception as e:
            logger.error(f"保存HTML内容到数据库时出错: {str(e)}")
            # 即使数据库操作失败，仍然返回一个ID，这样用户至少能看到HTML预览链接
            return unique_id
    except Exception as e:
        logger.error(f"保存HTML内容时出错: {str(e)}")
        # 出错时返回一个随机ID
        return str(uuid.uuid4())


class MicrosoftEmailExtension:
    """微软邮箱客户端扩展类，包含邮件获取功能"""

    def __init__(self, client):
        """
        初始化扩展

        Args:
            client: MicrosoftEmailClient实例
        """
        self.client = client

    def get_email_list(self, count: int = 25) -> Tuple[bool, List[Dict]]:
        """
        获取邮件列表

        Args:
            count: 获取邮件数量，默认25

        Returns:
            (成功标志, 邮件列表)
        """
        try:
            if not self.client.access_token or not self.client.puid:
                logger.error("access_token或PUID未初始化")
                return False, []

            headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9",
                "action": "StartupData",
                "authorization": f'MSAuth1.0 usertoken="{self.client.access_token}", type="MSACT"',
                "content-length": "0",
                "folderparams": '{"TimeZoneStr":"Greenwich Standard Time","FolderPaneBitFlags":0}',
                "messageparams": '{"TimeZoneStr":"Greenwich Standard Time","InboxReadingPanePosition":1,"IsFocusedInboxOn":true,"BootWithConversationView":true,"SortResults":[{"Path":{"__type":"PropertyUri:#Exchange","FieldURI":"conversation:LastDeliveryOrRenewTime"},"Order":"Descending"},{"Path":{"__type":"PropertyUri:#Exchange","FieldURI":"conversation:LastDeliveryTime"},"Order":"Descending"}],"IsSenderScreeningSettingEnabled":false}',
                "ms-cv": "",
                "origin": "https://outlook.live.com",
                "prefer": 'exchange.behavior="IncludeThirdPartyOnlineMeetingProviders"',
                "referer": "https://outlook.live.com/",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
                "x-anchormailbox": f"PUID:{self.client.puid}",
                "x-js-experiment": "5",
                "x-message-count": str(count),
                "x-owa-bootflights": "",
                "x-owa-correlationid": "accountPolicy_9",
                "x-owa-hosted-ux": "false",
                "x-owa-sessionid": "",
                "x-req-source": "Mail",
            }

            url = "https://outlook.live.com/owa/0/startupdata.ashx?app=Mail&n=0"

            # 添加超时和重试机制
            max_retries = 2
            timeout = 20  # 20秒超时

            for attempt in range(max_retries):
                try:
                    logger.info(f"获取邮件列表，尝试第 {attempt + 1} 次...")
                    response = self.client.session.post(
                        url, headers=headers, data="", timeout=timeout
                    )
                    break
                except Exception as e:
                    logger.warning(f"第 {attempt + 1} 次尝试失败: {str(e)}")
                    if attempt == max_retries - 1:
                        raise e
                    import time

                    time.sleep(2**attempt)  # 指数退避：2秒、4秒、8秒

            if response.status_code != 200:
                logger.error(f"获取邮件列表失败，状态码: {response.status_code}")
                return False, []

            # 解析响应JSON
            try:
                response_json = response.json()

                # 提取邮件对话列表
                conversations = (
                    response_json.get("findConversation", {})
                    .get("Body", {})
                    .get("Conversations", [])
                )

                if not conversations:
                    logger.info("未找到邮件对话")
                    return True, []

                # 解析邮件信息
                emails = []
                for conv in conversations:
                    # 安全地获取嵌套字典值
                    last_sender = conv.get("LastSender") or {}
                    mailbox = last_sender.get("Mailbox") or {}
                    family_id_obj = conv.get("FamilyId") or {}

                    email_info = {
                        "Subject": conv.get("ConversationTopic", ""),
                        "Preview": conv.get("Preview", ""),
                        "From": mailbox.get("EmailAddress", ""),
                        "_FamilyId": family_id_obj.get(
                            "Id", ""
                        ),  # 内部使用，不返回给用户
                        "Time": self._parse_email_time(
                            conv.get("LastDeliveryTime", "")
                        ),
                        # 移除IsRead和HasAttachments字段
                    }
                    emails.append(email_info)

                # 获取默认文件夹ID（用于后续获取邮件内容）
                owa_user_config = response_json.get("owaUserConfig") or {}
                session_settings = owa_user_config.get("SessionSettings") or {}
                default_folders = session_settings.get("DefaultFolderIds") or []

                if default_folders:
                    for folder in default_folders:
                        if folder:  # 确保folder不为None
                            folder_id = folder.get("Id", "")
                            if folder_id and folder_id != "null":
                                self.client.property_set_id = folder_id
                                break

                return True, emails

            except ValueError as e:
                logger.error(f"解析邮件列表响应JSON失败: {str(e)}")
                return False, []

        except Exception as e:
            logger.error(f"获取邮件列表时发生错误: {str(e)}")
            return False, []

    def get_email_content(
        self,
        family_id: str,
        conversation_id: str = None,
        request_host: str = None,
        username: str = None,
    ) -> Tuple[bool, Dict]:
        """
        获取邮件详细内容

        Args:
            family_id: 邮件家族ID
            conversation_id: 对话ID，如果不提供则使用默认值
            request_host: 请求的host信息，用于生成HTML预览链接
            username: 用户名，用于保存HTML内容

        Returns:
            (成功标志, 邮件内容字典)
        """
        try:
            if not self.client.access_token or not self.client.puid:
                logger.error("access_token或PUID未初始化")
                return False, {}

            if not conversation_id:
                conversation_id = self.client.property_set_id or ""

            headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9",
                "action": "GetConversationItems",
                "authorization": f'MSAuth1.0 usertoken="{self.client.access_token}", type="MSACT"',
                "content-type": "application/json; charset=utf-8",
                "ms-cv": "",
                "origin": "https://outlook.live.com",
                "prefer": 'exchange.behavior="IncludeThirdPartyOnlineMeetingProviders"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
                "x-anchormailbox": f"PUID:{self.client.puid}",
                "x-owa-correlationid": "",
                "x-owa-hosted-ux": "false",
                "x-owa-sessionid": "",
                "x-req-source": "Mail",
            }

            # 构建请求体
            request_body = {
                "__type": "GetConversationItemsJsonRequest:#Exchange",
                "Header": {
                    "__type": "JsonRequestHeaders:#Exchange",
                    "RequestServerVersion": "V2017_08_18",
                    "TimeZoneContext": {
                        "__type": "TimeZoneContext:#Exchange",
                        "TimeZoneDefinition": {
                            "__type": "TimeZoneDefinitionType:#Exchange",
                            "Id": "Greenwich Standard Time",
                        },
                    },
                },
                "Body": {
                    "__type": "GetConversationItemsRequest:#Exchange",
                    "Conversations": [
                        {
                            "__type": "ConversationRequestType:#Exchange",
                            "ConversationId": {
                                "__type": "ItemId:#Exchange",
                                "Id": family_id,
                            },
                            "SyncState": "",
                        }
                    ],
                    "ItemShape": {
                        "__type": "ItemResponseShape:#Exchange",
                        "BaseShape": "IdOnly",
                        "AddBlankTargetToLinks": True,
                        "BlockContentFromUnknownSenders": False,
                        "BlockExternalImagesIfSenderUntrusted": True,
                        "ClientSupportsIrm": True,
                        "CssScopeClassName": "rps_5b95",
                        "FilterHtmlContent": True,
                        "FilterInlineSafetyTips": True,
                        "InlineImageCustomDataTemplate": "{id}",
                        "InlineImageUrlTemplate": "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAEALAAAAAABAAEAAAIBTAA7",
                        "MaximumBodySize": 2097152,
                        "MaximumRecipientsToReturn": 20,
                        "ImageProxyCapability": "OwaAndConnectorsProxy",
                        "AdditionalProperties": [
                            {
                                "__type": "PropertyUri:#Exchange",
                                "FieldURI": "CanDelete",
                            },
                            {
                                "__type": "ExtendedPropertyUri:#Exchange",
                                "PropertySetId": "00062008-0000-0000-C000-000000000046",
                                "PropertyName": "ExplicitMessageCard",
                                "PropertyType": "String",
                            },
                        ],
                        "InlineImageUrlOnLoadTemplate": "",
                        "ExcludeBindForInlineAttachments": True,
                        "CalculateOnlyFirstBody": True,
                        "BodyShape": "UniqueFragment",
                    },
                    "ShapeName": "ItemPart",
                    "SortOrder": "DateOrderAscending",
                    "MaxItemsToReturn": 20,
                    "Action": "ReturnRootNode",
                    "FoldersToIgnore": [
                        {"__type": "FolderId:#Exchange", "Id": conversation_id},
                        {"__type": "FolderId:#Exchange", "Id": conversation_id},
                    ],
                    "ReturnSubmittedItems": True,
                    "ReturnDeletedItems": True,
                },
            }

            url = "https://outlook.live.com/owa/0/service.svc?action=GetConversationItems&app=Mail&n=8"

            # 添加超时和重试机制
            max_retries = 2
            timeout = 20  # 20秒超时

            for attempt in range(max_retries):
                try:
                    logger.info(f"获取邮件内容，尝试第 {attempt + 1} 次...")
                    response = self.client.session.post(
                        url, headers=headers, json=request_body, timeout=timeout
                    )
                    break
                except Exception as e:
                    logger.warning(f"第 {attempt + 1} 次尝试失败: {str(e)}")
                    if attempt == max_retries - 1:
                        raise e
                    import time

                    time.sleep(2**attempt)  # 指数退避

            if response.status_code != 200:
                logger.error(f"获取邮件内容失败，状态码: {response.status_code}")
                return False, {}

            # 解析响应JSON
            try:
                response_json = response.json()

                # 提取邮件内容 - 安全地获取嵌套字典值
                body = response_json.get("Body") or {}
                response_messages = body.get("ResponseMessages") or {}
                items = response_messages.get("Items") or []

                if not items:
                    logger.warning("响应中未找到邮件项目")
                    return False, {}

                if not items[0]:  # 确保第一个项目不为None
                    logger.warning("邮件项目为空")
                    return False, {}

                conversation = items[0].get("Conversation") or {}
                nodes = conversation.get("ConversationNodes") or []

                if not nodes:
                    logger.warning("响应中未找到对话节点")
                    return False, {}

                # 获取第一个节点的第一个项目
                first_node = nodes[0]
                if not first_node:  # 确保第一个节点不为None
                    logger.warning("第一个对话节点为空")
                    return False, {}

                node_items = first_node.get("Items") or []

                if not node_items:
                    logger.warning("对话节点中未找到项目")
                    return False, {}

                email_item = node_items[0]
                if not email_item:  # 确保邮件项目不为None
                    logger.warning("邮件项目为空")
                    return False, {}

                # 提取邮件内容
                # 安全地获取嵌套字典值
                unique_body = email_item.get("UniqueBody") or {}
                from_obj = email_item.get("From") or {}
                from_mailbox = from_obj.get("Mailbox") or {}

                # 获取原始HTML内容
                html_content = unique_body.get("Value", "")

                # 处理HTML内容，生成预览链接和纯文本
                processed_content = html_content
                html_preview_link = None

                if html_content and "<" in html_content and ">" in html_content:
                    # 这是HTML内容
                    try:
                        # 保存HTML内容并生成预览链接
                        html_id = save_html_content(html_content, family_id, username)
                        if html_id:
                            # 动态生成HTML预览链接
                            if request_host:
                                # 确保host包含协议
                                if not request_host.startswith(("http://", "https://")):
                                    base_url = f"https://{request_host}"
                                else:
                                    base_url = request_host
                            else:
                                # 如果没有提供request_host，尝试获取动态域名
                                try:
                                    domain = get_dynamic_domains()
                                    base_url = f"https://{domain}"
                                except Exception as e:
                                    # 如果获取失败，使用默认域名
                                    logger.warning(f"获取动态域名失败: {str(e)}")
                                    base_url = "https://api.kedaya.xyz"

                            html_preview_link = (
                                f"{base_url}/api/email/preview/{html_id}"
                            )

                        # 使用BeautifulSoup提取纯文本
                        soup = BeautifulSoup(html_content, "html.parser")

                        # 清理无用标签
                        for tag in ["style", "script"]:
                            for element in soup.find_all(tag):
                                element.decompose()

                        # 处理链接 - 为所有链接添加URL
                        for link in soup.find_all("a"):
                            href = link.get("href")
                            if href and "http" in href:
                                link_text = link.text.strip()
                                if link_text:
                                    link.string = f"{link_text} [{href}]"
                                else:
                                    link.string = f"[{href}]"

                        # 提取文本内容
                        text = soup.get_text(separator="\n", strip=True)

                        # 清理重复的空行
                        text = re.sub(r"\n{3,}", "\n\n", text)

                        # 将所有换行符替换为空格
                        text = text.replace("\n", " ")

                        processed_content = text.strip()

                    except Exception as e:
                        logger.error(f"处理HTML内容时出错: {str(e)}")
                        # 如果HTML解析失败，直接返回原始内容
                        processed_content = html_content.strip().replace("\n", " ")
                else:
                    # 纯文本内容简单处理
                    processed_content = html_content.strip().replace("\n", " ")

                email_content = {
                    "Subject": email_item.get("Subject", ""),
                    "Content": processed_content,
                    "From": from_mailbox.get("EmailAddress", ""),
                }

                # 如果有HTML预览链接，添加到结果中
                if html_preview_link:
                    email_content["HtmlPreviewLink"] = html_preview_link

                return True, email_content

            except ValueError as e:
                logger.error(f"解析邮件内容响应JSON失败: {str(e)}")
                return False, {}

        except Exception as e:
            logger.error(f"获取邮件内容时发生错误: {str(e)}")
            return False, {}

    def _parse_email_time(self, time_str: str) -> int:
        """
        解析邮件时间字符串为时间戳

        Args:
            time_str: 时间字符串

        Returns:
            时间戳
        """
        try:
            if not time_str:
                return 0

            # 尝试解析ISO格式的时间字符串
            if "T" in time_str:
                dt = datetime.fromisoformat(time_str.replace("Z", "+00:00"))
                return int(dt.timestamp())

            return 0
        except Exception:
            return 0
