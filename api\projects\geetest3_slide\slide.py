# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import random
from concurrent.futures import ThreadPoolExecutor
from pydantic import BaseModel
from typing import Optional, Dict, Any, List, Tuple
from .gap import restore_picture, get_gap
from .trace import get_track


# 定义数据模型
class CaptchaRequest(BaseModel):
    gt: Optional[str] = None
    challenge: Optional[str] = None
    proxy: Optional[str] = None


class ApiClient:
    """API客户端，负责与外部服务通信"""

    def __init__(self, base_url: str = "https://www.qali.cn/api"):
        self.base_url = base_url

    def execute_js(
        self,
        js_path: str,
        function_name: str,
        function_params: Optional[List] = None,
        timeout: int = 10,
    ) -> Dict[str, Any]:
        """调用JS执行API"""
        try:
            payload = {"jsPath": js_path, "functionName": function_name}
            if function_params:
                payload["functionParams"] = function_params
            print(payload)
            response = requests.post(
                url=f"{self.base_url}/execute-js", json=payload, timeout=timeout
            )

            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            raise Exception(f"API调用失败: {str(e)}")
        except ValueError as e:
            raise Exception(f"JSON解析失败: {str(e)}")


class GeetestSlideHelper:
    """极验滑动验证码辅助工具类"""

    def __init__(self, api_client: ApiClient):
        self.api_client = api_client
        self.js_path = "geetest3_slide.js"

    def get_encode_trace(self, track: List, s: str) -> str:
        """获取加密的轨迹数据"""
        try:
            response = self.api_client.execute_js(
                self.js_path, "get_encode_trace", [track, s]
            )
            return response["result"]
        except Exception as e:
            raise Exception(f"获取加密轨迹失败: {str(e)}")

    def get_w(
        self,
        distance: int,
        track: str,
        challenge: str,
        challenge_prefix: str,
        passtime: int,
        time_str: str,
        gt: str,
    ) -> str:
        """获取w参数"""
        try:
            response = self.api_client.execute_js(
                self.js_path,
                "get_w",
                [distance, track, challenge, challenge_prefix, passtime, time_str, gt],
            )
            return response["result"]
        except Exception as e:
            raise Exception(f"获取w参数失败: {str(e)}")


class GeetestSlideService:
    """极验滑动验证码服务"""

    def __init__(self):
        self.api_client = ApiClient()
        self.geetest_helper = GeetestSlideHelper(self.api_client)
        self.geetest_api_url = "https://api.geevisit.com"

    def generate_callback_suffix(self) -> str:
        """生成回调函数后缀"""
        return str(round(time.time() * 1000))

    def parse_json_response(self, response_text: str) -> Dict[str, Any]:
        """从响应文本中解析JSON数据"""
        try:
            return json.loads(
                response_text[response_text.index("(") + 1 : response_text.rindex(")")]
            )
        except Exception as e:
            raise Exception(f"解析响应数据失败: {str(e)}")

    def format_proxy(self, proxy: Optional[str] = None) -> Optional[Dict[str, str]]:
        """格式化代理设置"""
        if not proxy:
            return None

        # 如果代理字符串不包含协议前缀，添加http://前缀
        if proxy and not proxy.startswith(("http://", "https://")):
            proxy = f"http://{proxy}"

        return {"http": proxy, "https": proxy}

    def make_geetest_request(
        self, url: str, proxies: Optional[Dict[str, str]] = None, timeout: int = 10
    ) -> Dict[str, Any]:
        """发送请求到极验API并解析响应"""
        try:
            response = requests.get(url, proxies=proxies, timeout=timeout)
            response.raise_for_status()
            return self.parse_json_response(response.text)
        except requests.RequestException as e:
            raise Exception(f"请求极验API失败: {str(e)}")
        except Exception as e:
            raise Exception(f"处理极验响应失败: {str(e)}")

    def get_image_data(
        self, proxy: Optional[str] = None
    ) -> Tuple[str, str, str, int, List]:
        """获取验证码图片数据并处理，返回相关参数"""
        try:
            # 格式化代理设置
            proxies = self.format_proxy(proxy)

            # 获取初始gt和challenge参数
            url = f"https://www.geetest.com/demo/gt/register-slide-official?t={self.generate_callback_suffix()}"
            res = requests.get(url, proxies=proxies, timeout=10).json()
            gt = res["gt"]
            challenge = res["challenge"]

            # 第一次请求
            url = (
                f"{self.geetest_api_url}/ajax.php?gt={gt}"
                f"&challenge={challenge}&lang=zh-cn&pt=0&w=&callback=geetest_{self.generate_callback_suffix()}"
            )
            res = requests.get(url, proxies=proxies, timeout=10).text

            # 获取验证信息
            url = (
                f"{self.geetest_api_url}/get.php?is_next=true&type=slide3&gt={gt}"
                f"&challenge={challenge}&lang=zh-cn&https=true&protocol=https%3A%2F%2F"
                f"&offline=false&product=embed&api_server=api.geevisit.com&isPC=true"
                f"&autoReset=true&width=100%25&callback=geetest_{self.generate_callback_suffix()}"
            )
            print("url:", url)
            res = self.parse_json_response(
                requests.get(url, proxies=proxies, timeout=10).text
            )
            challenge = res["challenge"]
            s = res["s"]

            # 获取图片URL
            fullbg_url = f"https://static.geetest.com/{res['fullbg']}"
            bg_url = f"https://static.geetest.com/{res['bg']}"
            slice_url = f"https://static.geetest.com/{res['slice']}"

            # 并行获取图片数据
            with ThreadPoolExecutor(max_workers=3) as executor:
                fullbg_future = executor.submit(
                    requests.get, fullbg_url, proxies=proxies, timeout=10
                )
                bg_future = executor.submit(
                    requests.get, bg_url, proxies=proxies, timeout=10
                )
                slice_future = executor.submit(
                    requests.get, slice_url, proxies=proxies, timeout=10
                )

                fullbg_bytes = fullbg_future.result().content
                bg_bytes = bg_future.result().content
                slice_bytes = slice_future.result().content

            # 处理图片数据
            processed_bg_bytes, processed_fullbg_bytes = restore_picture(
                bg_bytes, fullbg_bytes
            )
            distance = get_gap(slice_bytes, processed_bg_bytes)

            # 确保距离为正数并调整偏移量
            adjusted_distance = max(distance - 5, 0)
            track = get_track(adjusted_distance)

            return gt, challenge, s, distance, track
        except Exception as e:
            raise Exception(f"获取图片数据失败: {str(e)}")

    def get_image_data_with_params(
        self, gt: str, challenge: str, proxy: Optional[str] = None
    ) -> Tuple[str, str, int, List]:
        """使用已有的gt和challenge获取图片数据"""
        try:
            # 格式化代理设置
            proxies = self.format_proxy(proxy)

            # 获取验证信息
            url = (
                f"{self.geetest_api_url}/get.php?is_next=true&type=slide3&gt={gt}"
                f"&challenge={challenge}&lang=zh-cn&https=true&protocol=https%3A%2F%2F"
                f"&offline=false&product=embed&api_server=api.geevisit.com&isPC=true"
                f"&autoReset=true&width=100%25&callback=geetest_{self.generate_callback_suffix()}"
            )
            res = self.parse_json_response(
                requests.get(url, proxies=proxies, timeout=10).text
            )

            s = res["s"]
            challenge = res["challenge"]  # 更新challenge

            # 获取图片URL
            fullbg_url = f"https://static.geetest.com/{res['fullbg']}"
            bg_url = f"https://static.geetest.com/{res['bg']}"
            slice_url = f"https://static.geetest.com/{res['slice']}"

            # 并行获取图片数据
            with ThreadPoolExecutor(max_workers=3) as executor:
                fullbg_future = executor.submit(
                    requests.get, fullbg_url, proxies=proxies, timeout=10
                )
                bg_future = executor.submit(
                    requests.get, bg_url, proxies=proxies, timeout=10
                )
                slice_future = executor.submit(
                    requests.get, slice_url, proxies=proxies, timeout=10
                )

                fullbg_bytes = fullbg_future.result().content
                bg_bytes = bg_future.result().content
                slice_bytes = slice_future.result().content

            # 处理图片数据
            processed_bg_bytes, processed_fullbg_bytes = restore_picture(
                bg_bytes, fullbg_bytes
            )
            distance = get_gap(slice_bytes, processed_bg_bytes)

            # 确保距离为正数并调整偏移量
            adjusted_distance = max(distance - 5, 0)
            track = get_track(adjusted_distance)

            return challenge, s, distance, track
        except Exception as e:
            raise Exception(f"获取图片数据失败: {str(e)}")

    def process_captcha(
        self,
        gt: Optional[str] = None,
        challenge: Optional[str] = None,
        proxy: Optional[str] = None,
    ) -> Dict[str, Any]:
        """处理极验滑动验证码流程"""
        try:
            # 如果未提供gt和challenge，自动获取
            if gt is None or challenge is None:
                gt, challenge, s, distance, track = self.get_image_data(proxy)
            else:
                # 使用提供的gt和challenge获取其他信息
                challenge, s, distance, track = self.get_image_data_with_params(
                    gt, challenge, proxy
                )

            # 处理轨迹数据
            passtime = track[-1][-1]
            encoded_track = self.geetest_helper.get_encode_trace(track, s)

            # 确保传入的距离为非负数
            adjusted_distance = max(distance - 5, 0)

            # 获取加密参数w
            w = self.geetest_helper.get_w(
                adjusted_distance,
                encoded_track,
                challenge,
                challenge[:32],
                passtime,
                str(random.randint(100, 200)),
                gt,
            )

            # 发送验证请求
            url = (
                f"{self.geetest_api_url}/ajax.php?gt={gt}"
                f"&challenge={challenge}&lang=zh-cn&pt=0&w={w}"
                f"&callback=geetest_{self.generate_callback_suffix()}"
            )

            return self.make_geetest_request(url, proxy)
        except Exception as e:
            return {"status": "error", "message": str(e)}


def geetest3_slide(
    gt: Optional[str] = None,
    challenge: Optional[str] = None,
    proxy: Optional[str] = None,
) -> Dict[str, Any]:
    """
    处理极验滑动验证码，保留与旧版兼容的接口

    :param gt: 极验验证的gt参数，如果为None则自动获取
    :param challenge: 极验验证的challenge参数，如果为None则自动获取
    :param proxy: 代理服务器地址，格式为"http://ip:port"或"https://ip:port"
    :return: 验证结果字典
    """
    service = GeetestSlideService()
    return service.process_captcha(gt, challenge, proxy)


def test_captcha() -> Dict[str, Any]:
    """测试函数，用于本地测试验证码功能"""
    try:
        result = geetest3_slide()
        print(result)
        return result
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return {"status": "error", "message": str(e)}


def parallel_test(num_threads: int = 3) -> List[Dict[str, Any]]:
    """并行测试多个验证码处理"""
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(test_captcha) for _ in range(num_threads)]
        results = [future.result() for future in futures]

    success_count = len([r for r in results if r.get("success") == 1])
    success_rate = success_count / len(results) if results else 0
    print(f"并行处理完成，成功率: {success_rate}")
    return results


if __name__ == "__main__":
    # 使用并行处理方式
    parallel_test(1)
