import json
import threading
import time
from fastapi import APIRouter, HTTPException, Depends, Request
import sqlalchemy
import asyncio

try:
    from api.models import users, success_conversations, timeout_refunds, AIRequest
    from api.db import get_db, with_db_retry, rate_limit
    from api.auth import get_current_user, get_client_ip
    from api.utils.data import get_candles, get_coinglass_data, chat, get_list
    from api.utils.billing import record_consumption
except:
    from models import users, success_conversations, timeout_refunds, AIRequest
    from db import get_db, with_db_retry, rate_limit
    from auth import get_current_user, get_client_ip
    from utils.data import get_candles, get_coinglass_data, chat, get_list
    from utils.billing import record_consumption

router = APIRouter(prefix="/api/v1", tags=["ai"])


@router.post("/ai")
def ai_endpoint(
    request: AIRequest,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    """接收AI对话请求"""
    client_ip = get_client_ip(req) if req else "unknown"

    price = 200  # 发起一次对话价格

    # 验证用户余额
    if current_user["balance"] < price and request.type == "chat":
        raise HTTPException(status_code=400, detail=f"余额不足，需要{price}点余额")

    with rate_limit(f"ai:{client_ip}", max_requests=3, expire=2):
        if request.type == "chat":

            @with_db_retry(max_retries=3)
            def do_chat():
                with get_db() as db:
                    with db.begin():
                        # 检查余额
                        price = int(request.num) * 10  # AI对话费用
                        if current_user["balance"] < price:
                            raise HTTPException(status_code=400, detail="余额不足")

                        candles = get_candles(request.symbol, request.time, request.num)
                        if candles == "失败":
                            raise HTTPException(
                                status_code=400, detail="K线数据获取失败"
                            )
                        symbol = request.symbol.upper()
                        if "USDT" in symbol:
                            symbol = symbol.replace("USDT", "")
                        coinglass_data = get_coinglass_data(
                            f"https://capi.coinglass.com/api/v2/kline?diff=false&minLimit=false&limit={request.num}&interval={request.time[::-1].lower()}&symbol=ALL%23{symbol}%23aggregated_spot_buy_sell_usd"
                        )
                        if coinglass_data == "失败":
                            raise HTTPException(
                                status_code=400, detail="现货流入/流出数据获取失败"
                            )
                        coinglass_data1 = get_coinglass_data(
                            f"https://capi.coinglass.com/api/openInterest/ex/info?symbol={symbol}"
                        )
                        if coinglass_data1 == "失败":
                            raise HTTPException(
                                status_code=400, detail="各交易所的持仓量数据获取失败"
                            )
                        candles = json.dumps(candles)
                        coinglass_data = json.dumps(coinglass_data)
                        coinglass_data1 = json.dumps(coinglass_data1)
                        # 调用AI对话
                        if request.conversation:
                            result = chat(
                                request.symbol,
                                current_user["username"],
                                candles,
                                coinglass_data,
                                coinglass_data1,
                                request.conversation,
                            )
                        else:
                            result = chat(
                                request.symbol,
                                current_user["username"],
                                candles,
                                coinglass_data,
                                coinglass_data1,
                            )
                        print(result)
                        if result == "失败":
                            return {
                                "code": 200,
                                "status": False,
                                "message": "AI对话失败",
                                "balance": current_user["balance"],
                            }

                        # 扣费
                        new_balance = current_user["balance"] - price
                        stmt = (
                            users.update()
                            .where(users.c.username == current_user["username"])
                            .values(balance=new_balance)
                        )
                        update_result = db.execute(stmt)

                        if update_result.rowcount == 0:
                            raise sqlalchemy.exc.OperationalError("更新失败，请重试")

                        # 记录消费
                        try:
                            # 使用异步函数但同步调用，避免阻塞主流程

                            asyncio.create_task(
                                record_consumption(
                                    username=current_user["username"],
                                    details=f"AI分析 {request.symbol}",
                                    amount=price,
                                )
                            )
                        except Exception as e:
                            print(f"记录消费失败: {str(e)}")
                            # 继续执行，不因记录失败而中断流程

                        # 启动后台轮询任务
                        def background_poll():
                            max_retries = 10
                            retry_interval = 10  # 秒
                            for _ in range(max_retries):
                                time.sleep(retry_interval)
                                message_result = get_list(result)
                                if message_result != "失败" and message_result != "":
                                    print(f"AI对话成功，conversation_id: {result}")
                                    # 记录成功对话
                                    try:
                                        with get_db() as success_db:
                                            with success_db.begin():
                                                success_db.execute(
                                                    success_conversations.insert().values(
                                                        conversation_id=result,
                                                        username=current_user[
                                                            "username"
                                                        ],
                                                        symbol=request.symbol,
                                                    )
                                                )
                                    except Exception as e:
                                        print(f"记录成功对话失败: {str(e)}")
                                    return
                            print(f"AI对话超时，conversation_id: {result}")
                            # AI对话超时，退还用户余额
                            try:
                                with get_db() as refund_db:
                                    with refund_db.begin():
                                        # 退还余额
                                        refund_stmt = (
                                            users.update()
                                            .where(
                                                users.c.username
                                                == current_user["username"]
                                            )
                                            .values(balance=users.c.balance + price)
                                        )
                                        refund_db.execute(refund_stmt)

                                        # 记录退款状态
                                        refund_db.execute(
                                            timeout_refunds.insert().values(
                                                conversation_id=result,
                                                refunded=True,
                                                username=current_user["username"],
                                                amount=price,
                                            )
                                        )
                                print(
                                    f"已退还用户 {current_user['username']} {price} 余额"
                                )
                            except Exception as e:
                                print(f"退款失败: {str(e)}")

                        # 启动后台线程
                        thread = threading.Thread(target=background_poll)
                        thread.daemon = True  # 设置为守护线程
                        thread.start()

                        # 立即返回 conversation_id
                        return {
                            "code": 200,
                            "status": True,
                            "conversation_id": result,
                            "balance": new_balance,
                            "message": "AI对话请求已提交，请稍后使用 conversation_id 查询结果",
                            "message_type": "pending",
                        }

            return do_chat()

        elif request.type == "list":
            if not request.conversation:
                raise HTTPException(status_code=400, detail="conversation参数不能为空")

            result = get_list(request.conversation)
            if result == "失败":
                return {"code": 200, "status": False, "message": "AI对话查询失败"}
            if result == "":
                # 检查是否已经退款
                with get_db() as db:
                    refund_record = db.execute(
                        timeout_refunds.select().where(
                            timeout_refunds.c.conversation_id == request.conversation
                        )
                    ).fetchone()

                    if refund_record and refund_record["refunded"]:
                        return {
                            "code": 200,
                            "status": False,
                            "message": "AI对话已超时，余额已退回",
                            "message_type": "timeout",
                        }
                    else:
                        return {
                            "code": 200,
                            "status": False,
                            "message_type": "pending",
                        }
            return {
                "code": 200,
                "status": True,
                "message": result,
                "message_type": "success",
            }
        return {
            "code": 200,
            "status": False,
            "message": "type参数错误，支持 chat 或 list",
        }


# 修改获取最后成功对话ID的接口
@router.get("/ai/last-success")
def get_last_success_conversation():
    """获取所有用户最后一次成功的对话ID"""
    try:
        with get_db() as db:
            # 使用子查询获取每个用户最新的对话记录
            subquery = (
                sqlalchemy.select(
                    success_conversations.c.username,
                    sqlalchemy.func.max(success_conversations.c.created_at).label(
                        "max_created_at"
                    ),
                )
                .group_by(success_conversations.c.username)
                .alias("latest_convs")
            )

            # 获取每个用户最新的完整记录
            query = (
                sqlalchemy.select(success_conversations)
                .join(
                    subquery,
                    sqlalchemy.and_(
                        success_conversations.c.username == subquery.c.username,
                        success_conversations.c.created_at == subquery.c.max_created_at,
                    ),
                )
                .order_by(success_conversations.c.created_at.desc())
            )

            results = db.execute(query).fetchall()

            if results:
                return {
                    "code": 200,
                    "status": True,
                    "data": [
                        {
                            "username": row[success_conversations.c.username],
                            "conversation_id": row[
                                success_conversations.c.conversation_id
                            ],
                            "symbol": row[success_conversations.c.symbol],
                            "created_at": row[
                                success_conversations.c.created_at
                            ].isoformat(),
                        }
                        for row in results
                    ],
                }
            else:
                return {"code": 200, "status": False, "message": "暂无成功对话记录"}
    except Exception as e:
        print(f"获取最后成功对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
