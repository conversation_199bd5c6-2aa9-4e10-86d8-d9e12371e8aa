import requests
import json
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.utils.crypto import yt, decrypt_aes
except:
    from utils.crypto import yt, decrypt_aes


def get_coinglass_data(url):
    """
    获取CoinGlass数据并解密
    """
    headers = {
        "language": "zh",
        "sec-ch-ua-platform": '"Windows"',
        "sec-ch-ua": '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        "sec-ch-ua-mobile": "?0",
        "encryption": "true",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
        "accept": "application/json",
        "cache-ts": str(int(time.time() * 1000)),
        "origin": "https://www.coinglass.com",
        "sec-fetch-site": "same-site",
        "sec-fetch-mode": "cors",
        "sec-fetch-dest": "empty",
        "referer": "https://www.coinglass.com/",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "priority": "u=1, i",
    }

    try:
        response = requests.get(url, headers=headers, verify=False)

        response_json = response.json()
        if not response_json.get("success", False):
            logger.error(f"API请求：API返回错误: {response_json}")
            return None

        user_header = response.headers.get("user")
        if user_header is None:
            logger.warning("API请求：响应头中没有找到'user'字段")
            return response_json.get("data")

        data = yt(
            response_json.get("data"),
            yt(user_header, "Y29pbmdsYXNzL2Fw"),
        )
        logger.debug(f"数据解密：解密后数据: {data}")
        return data
    except Exception as e:
        logger.error(f"API请求：请求或解析过程中出错: {e}")
        return "失败"


def get_realtime_data():
    """
    获取实盘动态数据
    """
    url = "https://i.bicoin.com.cn/settingFirmOffer/getUserLeaderListBySecret"
    params = {"typeStr": "", "pageSize": 20, "showType": 5, "pageNum": 1}

    headers = {
        "Accept": "application/json,application/xml,application/xhtml+xml,text/html;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh",
        "Appversion": "4.0.4",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "From": "Android",
        "Fromandroid": "bicoin",
        "Mobilid": "dervice_id",
        "Mobilkey": "C9FF5A57CF0DCC68901C9BF69246E87E",
        "Token": "ad317b124988d50dd01d25dbfa69ac22",
        "Redrisegreendown": "2",
        "User-Agent": "Mozilla/5.0 (Linux; U; Android 13; zh-cn; 2211133C Build/TKQ1.220905.001) AppleWebKit/533.1 (KHTML, like Gecko) Version/5.0 Mobile Safari/533.1",
        "Usertempid": "",
        "Host": "i.bicoin.com.cn",
    }

    try:
        response = requests.get(url, params=params, headers=headers)
        response_data = response.json()
        if "data" in response_data:
            # 解密数据
            decrypted_data = decrypt_aes(response_data["data"])
            if decrypted_data:
                data = json.loads(decrypted_data)
                # 格式化输出
                result = []
                for item in data.get("firmOfferHisList", []):
                    result.append(
                        f"时间：{item.get('informTime', '')} | "
                        f"交易员：{item.get('leaderName', '')} | "
                        f"币对：{item.get('sym', '')} | "
                        f"操作：{item.get('labelSub', '')} | "
                        f"价格：{item.get('unit', '')} | "
                        f"数量：{item.get('amount', '')}"
                    )
                return result
        return None
    except Exception as e:
        logger.error(f"实盘数据：获取实盘动态失败: {str(e)}")
        return None


def get_candles(symbol: str = "BTCUSDT", time_frame: str = "15m", num: str = "100"):
    """
    获取K线数据
    """
    symbol = symbol.upper()
    if "USDT" not in symbol:
        symbol = symbol + "USDT"
    url = f"https://api.bitget.com/api/v2/mix/market/candles?symbol={symbol}&granularity={time_frame}&limit={num}&productType=usdt-futures"
    response = requests.get(url)
    response = response.json()
    if response["msg"] == "success":
        return response["data"]
    else:
        return "失败"


def chat(
    symbol: str,
    name: str,
    candles: str,
    coinglass_data: str,
    coinglass_data1: str,
    conversation: str = "",
):
    """
    调用AI对话接口
    """
    url = "https://api.coze.cn/v3/chat?conversation_id=" + conversation
    headers = {
        "Authorization": "Bearer pat_BD3u5RCRw4PntCJ1QxnVrZyZ3VDpVbhxlS11fdGHLxnBQQivmdMch0CXG6QEqrrF",
        "Content-Type": "application/json",
    }
    data = {
        "additional_messages": [
            {
                "role": "user",
                "type": "question",
                "content_type": "text",
                "content": f"分析币对：{symbol},数据都是倒序:现货K线数据{candles},现货流入/流出数据{coinglass_data},各交易所的持仓量数据{coinglass_data1}",
            }
        ],
        "stream": False,
        "user_id": name,
        "bot_id": "7477162238172987411",
        "auto_save_history": True,
    }
    response = requests.post(url, headers=headers, json=data)
    response = response.json()
    logger.debug(f"AI对话：响应数据 {response}")
    if response["code"] == 0:
        return response["data"]["conversation_id"]
    else:
        return "失败"


def get_list(conversation: str = ""):
    """
    获取对话消息列表
    """
    url = (
        "https://api.coze.cn/v1/conversation/message/list?conversation_id="
        + conversation
    )
    headers = {
        "Authorization": "Bearer pat_BD3u5RCRw4PntCJ1QxnVrZyZ3VDpVbhxlS11fdGHLxnBQQivmdMch0CXG6QEqrrF",
        "Content-Type": "application/json",
    }
    response = requests.post(url, headers=headers)
    response = response.json()
    if response["data"][0]["type"] == "answer":
        return response["data"][0]["content"]
    else:
        return "失败"
