# -*- coding: utf-8 -*-

import numpy as np
from PIL import Image, ImageFilter, ImageDraw
import io
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("geetest_gap")


class SlideCrack(object):
    """滑块验证码识别处理类"""

    def __init__(self, gap_bytes, bg_bytes, out=None):
        """
        初始化

        :param gap_bytes: 滑块图片的字节数据
        :param bg_bytes: 背景图片的字节数据
        :param out: 输出图片路径(可选)
        """
        self.gap_bytes = gap_bytes
        self.bg_bytes = bg_bytes
        self.out = out

    @staticmethod
    def bytes_to_pil_img(img_bytes):
        """
        将字节流转换为PIL图片对象

        :param img_bytes: 图片字节数据
        :return: PIL图片对象
        """
        try:
            return Image.open(io.BytesIO(img_bytes)).convert("RGB")
        except Exception as e:
            logger.error(f"转换图片数据失败: {str(e)}")
            raise

    def extract_subimage(self, img):
        """
        清除图片的空白区域，主要清除滑块的空白

        :param img: PIL图片对象
        :return: 处理后的PIL图片
        """
        try:
            img_np = np.array(img)
            rows, cols, channel = img_np.shape
            min_x = rows
            min_y = cols
            max_x = 0
            max_y = 0

            # 遍历图片像素，找出非空白区域的边界
            for x in range(1, rows):
                for y in range(1, cols):
                    t = set(img_np[x, y])
                    if len(t) >= 2:
                        if x < min_x:
                            min_x = x
                        if x > max_x:
                            max_x = x
                        if y < min_y:
                            min_y = y
                        if y > max_y:
                            max_y = y

            # 确保找到了有效区域
            if min_x >= max_x or min_y >= max_y:
                raise Exception("无法找到滑块的有效区域")

            img1 = img.crop((min_y, min_x, max_y, max_x))
            return img1
        except Exception as e:
            logger.error(f"清除滑块空白区域失败: {str(e)}")
            raise

    @staticmethod
    def image_edge_detection(img):
        """
        图片边缘检测，使用PIL的FIND_EDGES

        :param img: 输入PIL图片
        :return: 边缘检测后的灰度PIL图片
        """
        try:
            gray_img = img.convert("L")
            edges = gray_img.filter(ImageFilter.FIND_EDGES)
            return edges
        except Exception as e:
            logger.error(f"图片边缘检测失败: {str(e)}")
            raise

    @staticmethod
    def pil_to_gray_np(img):
        """
        PIL图片转灰度numpy数组
        """
        return np.array(img.convert("L"))

    def template_match(self, tpl, target):
        """
        用numpy实现模板匹配，返回最佳x坐标

        :param tpl: PIL图片（滑块）
        :param target: PIL图片（背景）
        :return: 匹配位置的x坐标
        """
        try:
            tpl_np = self.pil_to_gray_np(tpl)
            target_np = self.pil_to_gray_np(target)

            th, tw = tpl_np.shape
            h, w = target_np.shape
            max_corr = -1
            max_loc = 0

            for x in range(w - tw + 1):
                for y in range(h - th + 1):
                    window = target_np[y : y + th, x : x + tw]
                    if window.shape != tpl_np.shape:
                        continue
                    corr = np.corrcoef(tpl_np.flatten(), window.flatten())[0, 1]
                    if corr > max_corr:
                        max_corr = corr
                        max_loc = x

            if max_corr < 0.3:
                logger.warning(f"模板匹配可信度较低: {max_corr}")

            # 如果需要输出结果图片
            if self.out:
                # 在原图上标记匹配位置
                output_img = self.bytes_to_pil_img(self.bg_bytes)
                draw = ImageDraw.Draw(output_img)
                draw.rectangle(
                    [(max_loc, 0), (max_loc + tw, th)], outline=(255, 0, 0), width=2
                )
                output_img.save(self.out)

            return max_loc
        except Exception as e:
            logger.error(f"模板匹配失败: {str(e)}")
            raise

    def discern(self):
        """
        识别滑块位置

        :return: 缺口位置的x坐标
        """
        try:
            # 转换滑块图片为PIL对象并提取有效区域
            gap_img = self.bytes_to_pil_img(self.gap_bytes)
            gap_img = self.extract_subimage(gap_img)

            # 边缘检测
            gap_edge = self.image_edge_detection(gap_img)

            # 转换背景图片为PIL对象
            bg_img = self.bytes_to_pil_img(self.bg_bytes)

            # 背景图片边缘检测
            bg_edge = self.image_edge_detection(bg_img)

            # 模板匹配
            x = self.template_match(gap_edge, bg_edge)

            return x
        except Exception as e:
            logger.error(f"识别滑块位置失败: {str(e)}")
            raise


def get_gap(gap_bytes, bg_bytes, out_path=None):
    """
    获取滑块位置

    :param gap_bytes: 滑块图片字节
    :param bg_bytes: 背景图片字节
    :param out_path: 输出图片路径(可选)
    :return: 滑块位置的x坐标
    """
    try:
        sc = SlideCrack(gap_bytes, bg_bytes, out_path)
        return sc.discern()
    except Exception as e:
        logger.error(f"获取缺口位置失败: {str(e)}")
        raise
