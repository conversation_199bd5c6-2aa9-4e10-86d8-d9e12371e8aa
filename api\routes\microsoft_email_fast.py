"""
Microsoft邮箱API - 超级快速模式路由
专为极致性能优化，适用于只需要基本邮件信息的场景
"""

from fastapi import APIRouter, HTTPException, Depends, Request
import logging
from datetime import datetime, timezone, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.models import MicrosoftEmailRequest
    from api.auth import get_current_user, get_client_ip
    from api.db import rate_limit
    from api.utils.common import get_request_host
    from api.utils.microsoft_email_api import get_microsoft_emails_optimized
    from api.utils.startup_tasks import ensure_startup_tasks_executed
except:
    from models import MicrosoftEmailRequest
    from auth import get_current_user, get_client_ip
    from db import rate_limit
    from utils.common import get_request_host
    from utils.microsoft_email_api import get_microsoft_emails_optimized
    from utils.startup_tasks import ensure_startup_tasks_executed

router = APIRouter(prefix="/api/v1", tags=["microsoft_email_fast"])


@router.post("/microsoft-email/fast")
async def get_microsoft_email_fast(
    request: MicrosoftEmailRequest,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    """
    获取微软邮箱邮件 - 超级快速模式 ⚡

    特点:
    - 只返回邮件列表和预览信息
    - 跳过详细内容获取
    - 响应时间 < 2秒
    - 适用于邮件列表展示场景

    参数:
    - account: 微软邮箱账号
    - password: 微软邮箱密码
    - num: 获取邮件数量，默认"10"
    - sender_email: 发件人邮箱地址筛选（可选）
    - proxy: 代理地址（可选）
    """
    # 确保启动任务已执行
    ensure_startup_tasks_executed()

    client_ip = get_client_ip(req) if req else "unknown"
    request_host = get_request_host(req) if req else None

    with rate_limit(f"microsoft_email_fast:{client_ip}", max_requests=15, expire=60):
        try:
            # 处理num参数
            try:
                num_count = int(request.num) if request.num else 10
            except (ValueError, TypeError):
                num_count = 10

            # 强制启用快速模式
            result = get_microsoft_emails_optimized(
                account=request.account,
                password=request.password,
                count=num_count,
                sender_email=request.sender_email,
                proxy=request.proxy,
                request_host=request_host,
                username=current_user["username"],
                fast_mode=True,  # 强制快速模式
            )

            if not result["status"]:
                return {
                    "code": 400,
                    "status": False,
                    "data": {"message": result["msg"]},
                }

            emails = result.get("emails", [])

            # 为每封邮件添加格式化时间
            for email in emails:
                if "Time" in email and email["Time"]:
                    try:
                        timestamp = int(email["Time"])
                        utc8 = timezone(
                            timezone.utc.utcoffset(None) + timedelta(hours=8)
                        )
                        email_time = datetime.fromtimestamp(timestamp, tz=utc8)
                        email["FormatTime"] = email_time.strftime("%Y-%m-%d %H:%M:%S")
                    except Exception as e:
                        logger.error(f"时间转换错误: {str(e)}")
                        email["FormatTime"] = "时间格式错误"

            # 检查缓存状态
            used_cache = result.get("cached", False)
            performance = result.get("performance", {})

            cache_message = " (使用缓存登录)" if used_cache else " (完整登录)"

            return {
                "code": 200,
                "status": True,
                "data": {
                    "account": request.account,
                    "emails": emails,
                    "count": len(emails),
                    "cached": used_cache,
                    "fast_mode": True,
                    "performance": performance,
                    "message": f"快速获取到{len(emails)}封邮件{cache_message}",
                    "note": "快速模式：只包含邮件列表和预览信息，如需详细内容请使用标准模式",
                },
            }

        except Exception as e:
            logger.error(f"快速获取微软邮箱邮件时发生错误: {str(e)}")
            return {
                "code": 500,
                "status": False,
                "data": {"message": f"获取邮件失败: {str(e)}"},
            }


@router.post("/microsoft-email/preview")
async def get_microsoft_email_preview(
    request: MicrosoftEmailRequest,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    """
    获取微软邮箱邮件预览 - 极速模式 🚀

    特点:
    - 只返回最基本的邮件信息
    - 最快响应时间
    - 适用于快速检查是否有新邮件

    参数:
    - account: 微软邮箱账号
    - password: 微软邮箱密码
    - num: 获取邮件数量，默认"5"（建议不超过5）
    """
    # 确保启动任务已执行
    ensure_startup_tasks_executed()

    client_ip = get_client_ip(req) if req else "unknown"

    with rate_limit(f"microsoft_email_preview:{client_ip}", max_requests=20, expire=60):
        try:
            # 限制预览模式的邮件数量
            num_count = min(int(request.num) if request.num else 5, 5)

            # 使用快速模式获取邮件
            result = get_microsoft_emails_optimized(
                account=request.account,
                password=request.password,
                count=num_count,
                sender_email=request.sender_email,
                proxy=request.proxy,
                username=current_user["username"],
                fast_mode=True,
            )

            if not result["status"]:
                return {
                    "code": 400,
                    "status": False,
                    "data": {"message": result["msg"]},
                }

            emails = result.get("emails", [])

            # 只返回最基本的信息
            preview_emails = []
            for email in emails:
                preview_emails.append(
                    {
                        "Subject": email.get("Subject", "")[:50] + "..."
                        if len(email.get("Subject", "")) > 50
                        else email.get("Subject", ""),
                        "From": email.get("From", ""),
                        "Preview": email.get("Preview", "")[:100] + "..."
                        if len(email.get("Preview", "")) > 100
                        else email.get("Preview", ""),
                        "Time": email.get("Time", ""),
                    }
                )

            used_cache = result.get("cached", False)
            performance = result.get("performance", {})

            return {
                "code": 200,
                "status": True,
                "data": {
                    "account": request.account,
                    "emails": preview_emails,
                    "count": len(preview_emails),
                    "cached": used_cache,
                    "preview_mode": True,
                    "performance": performance,
                    "message": f"预览模式：获取到{len(preview_emails)}封邮件摘要",
                },
            }

        except Exception as e:
            logger.error(f"预览微软邮箱邮件时发生错误: {str(e)}")
            return {
                "code": 500,
                "status": False,
                "data": {"message": f"获取邮件预览失败: {str(e)}"},
            }
