[["move", 170, 514, 1721639139496, "pointermove"], ["move", 171, 514, 1721639139518, "pointermove"], ["move", 172, 514, 1721639139564, "pointermove"], ["move", 174, 514, 1721639139578, "pointermove"], ["move", 174, 514, 1721639139586, "pointermove"], ["move", 175, 514, 1721639139646, "pointermove"], ["move", 178, 514, 1721639139651, "pointermove"], ["move", 177, 513, 1721639139652, "mousemove"], ["move", 179, 514, 1721639139660, "pointermove"], ["move", 182, 514, 1721639139676, "pointermove"], ["move", 186, 514, 1721639139682, "pointermove"], ["move", 190, 514, 1721639139690, "pointermove"], ["move", 195, 514, 1721639139697, "pointermove"], ["move", 195, 513, 1721639139698, "mousemove"], ["move", 202, 514, 1721639139705, "pointermove"], ["move", 210, 514, 1721639139712, "pointermove"], ["move", 218, 514, 1721639139719, "pointermove"], ["move", 230, 513, 1721639139727, "pointermove"], ["move", 242, 512, 1721639139735, "pointermove"], ["move", 254, 510, 1721639139741, "pointermove"], ["move", 263, 507, 1721639139749, "pointermove"], ["move", 263, 507, 1721639139750, "mousemove"], ["move", 274, 504, 1721639139757, "pointermove"], ["move", 283, 503, 1721639139765, "pointermove"], ["move", 283, 503, 1721639139766, "mousemove"], ["move", 292, 501, 1721639139772, "pointermove"], ["move", 302, 500, 1721639139779, "pointermove"], ["move", 310, 500, 1721639139787, "pointermove"], ["move", 316, 499, 1721639139795, "pointermove"], ["move", 316, 499, 1721639139796, "mousemove"], ["move", 322, 498, 1721639139801, "pointermove"], ["move", 326, 498, 1721639139810, "pointermove"], ["move", 328, 497, 1721639139817, "pointermove"], ["move", 330, 497, 1721639139825, "pointermove"], ["move", 331, 497, 1721639139831, "pointermove"], ["move", 332, 497, 1721639139839, "pointermove"], ["move", 334, 496, 1721639139856, "pointermove"], ["move", 335, 496, 1721639139861, "pointermove"], ["move", 336, 496, 1721639139869, "pointermove"], ["move", 336, 496, 1721639139870, "mousemove"], ["move", 338, 496, 1721639139886, "pointermove"], ["move", 339, 496, 1721639139891, "pointermove"], ["move", 342, 495, 1721639139900, "pointermove"], ["move", 346, 495, 1721639139907, "pointermove"], ["move", 350, 494, 1721639139916, "pointermove"], ["move", 354, 493, 1721639139921, "pointermove"], ["move", 359, 493, 1721639139929, "pointermove"], ["move", 359, 493, 1721639139930, "mousemove"], ["move", 364, 492, 1721639139937, "pointermove"], ["move", 370, 491, 1721639139946, "pointermove"], ["move", 375, 490, 1721639139951, "pointermove"], ["move", 382, 489, 1721639139959, "pointermove"], ["move", 387, 488, 1721639139967, "pointermove"], ["move", 394, 487, 1721639139975, "pointermove"], ["move", 400, 487, 1721639139981, "pointermove"], ["move", 406, 486, 1721639139989, "pointermove"], ["move", 411, 485, 1721639139997, "pointermove"], ["move", 415, 483, 1721639140006, "pointermove"], ["move", 419, 482, 1721639140011, "pointermove"], ["move", 422, 481, 1721639140019, "pointermove"], ["move", 426, 480, 1721639140028, "pointermove"], ["move", 427, 479, 1721639140041, "pointermove"], ["move", 430, 478, 1721639140050, "pointermove"], ["move", 430, 477, 1721639140057, "pointermove"], ["move", 431, 476, 1721639140066, "pointermove"], ["move", 431, 475, 1721639140072, "pointermove"], ["move", 431, 474, 1721639140088, "pointermove"], ["move", 431, 472, 1721639140102, "pointermove"], ["move", 431, 471, 1721639140110, "pointermove"], ["move", 431, 469, 1721639140118, "pointermove"], ["move", 431, 468, 1721639140126, "pointermove"], ["move", 431, 466, 1721639140132, "pointermove"], ["move", 431, 464, 1721639140148, "pointermove"], ["move", 431, 462, 1721639140156, "pointermove"], ["move", 431, 460, 1721639140186, "pointermove"], ["move", 431, 459, 1721639140261, "pointermove"], ["move", 432, 460, 1721639140530, "pointermove"], ["move", 432, 462, 1721639140538, "pointermove"], ["move", 434, 462, 1721639140546, "pointermove"], ["move", 434, 463, 1721639140552, "pointermove"], ["move", 432, 464, 1721639140912, "pointermove"], ["move", 429, 466, 1721639140920, "pointermove"], ["move", 426, 467, 1721639140928, "pointermove"], ["move", 422, 470, 1721639140934, "pointermove"], ["move", 417, 471, 1721639140942, "pointermove"], ["move", 413, 472, 1721639140950, "pointermove"], ["move", 408, 474, 1721639140964, "pointermove"], ["move", 404, 475, 1721639140972, "pointermove"], ["move", 399, 475, 1721639140980, "pointermove"], ["move", 395, 476, 1721639140988, "pointermove"], ["move", 390, 478, 1721639140994, "pointermove"], ["move", 386, 478, 1721639141002, "pointermove"], ["move", 381, 479, 1721639141010, "pointermove"], ["move", 376, 479, 1721639141018, "pointermove"], ["move", 370, 479, 1721639141024, "pointermove"], ["move", 364, 479, 1721639141032, "pointermove"], ["move", 357, 479, 1721639141040, "pointermove"], ["move", 352, 479, 1721639141048, "pointermove"], ["move", 347, 479, 1721639141054, "pointermove"], ["move", 342, 479, 1721639141062, "pointermove"], ["move", 339, 478, 1721639141070, "pointermove"], ["move", 336, 478, 1721639141078, "pointermove"], ["move", 334, 478, 1721639141084, "pointermove"], ["move", 333, 478, 1721639141100, "pointermove"], ["move", 332, 479, 1721639141138, "pointermove"], ["move", 331, 479, 1721639141152, "pointermove"], ["move", 330, 479, 1721639141160, "pointermove"], ["move", 329, 480, 1721639141168, "pointermove"], ["move", 328, 482, 1721639141182, "pointermove"], ["move", 327, 483, 1721639141198, "pointermove"], ["move", 326, 483, 1721639141204, "pointermove"], ["move", 324, 484, 1721639141212, "pointermove"], ["move", 322, 486, 1721639141220, "pointermove"], ["move", 319, 487, 1721639141228, "pointermove"], ["move", 316, 488, 1721639141234, "pointermove"], ["move", 313, 491, 1721639141242, "pointermove"], ["move", 310, 494, 1721639141250, "pointermove"], ["move", 306, 495, 1721639141258, "pointermove"], ["move", 304, 495, 1721639141264, "pointermove"], ["move", 301, 498, 1721639141272, "pointermove"], ["move", 299, 499, 1721639141280, "pointermove"], ["move", 297, 499, 1721639141288, "pointermove"], ["move", 295, 499, 1721639141294, "pointermove"], ["move", 293, 499, 1721639141302, "pointermove"], ["move", 292, 500, 1721639141310, "pointermove"], ["move", 291, 500, 1721639141324, "pointermove"], ["down", 291, 500, 1721639141543, "pointerdown"], ["up", 291, 500, 1721639141580, "pointerup"], ["move", 294, 500, 1721639141654, "pointermove"], ["move", 299, 500, 1721639141662, "pointermove"], ["move", 306, 500, 1721639141670, "pointermove"], ["move", 314, 499, 1721639141678, "pointermove"], ["move", 323, 498, 1721639141684, "pointermove"], ["move", 334, 497, 1721639141692, "pointermove"], ["move", 344, 495, 1721639141708, "pointermove"], ["move", 356, 493, 1721639141714, "pointermove"], ["move", 367, 491, 1721639141722, "pointermove"], ["move", 378, 489, 1721639141730, "pointermove"], ["move", 387, 487, 1721639141738, "pointermove"], ["move", 395, 485, 1721639141744, "pointermove"], ["move", 402, 483, 1721639141752, "pointermove"], ["move", 407, 482, 1721639141760, "pointermove"], ["move", 410, 481, 1721639141768, "pointermove"], ["move", 412, 480, 1721639141774, "pointermove"], ["move", 414, 479, 1721639141924, "pointermove"], ["move", 415, 479, 1721639141932, "pointermove"], ["move", 416, 478, 1721639141940, "pointermove"], ["move", 419, 477, 1721639141949, "pointermove"], ["move", 422, 475, 1721639141954, "pointermove"], ["move", 426, 475, 1721639141962, "pointermove"], ["move", 430, 473, 1721639141970, "pointermove"], ["move", 434, 472, 1721639141978, "pointermove"], ["move", 436, 470, 1721639141984, "pointermove"], ["move", 440, 469, 1721639141992, "pointermove"], ["move", 443, 468, 1721639142000, "pointermove"], ["move", 446, 467, 1721639142008, "pointermove"], ["move", 450, 466, 1721639142014, "pointermove"], ["move", 450, 466, 1721639142022, "pointermove"], ["move", 451, 466, 1721639142030, "pointermove"], ["move", 452, 466, 1721639142038, "pointermove"], ["move", 454, 466, 1721639142044, "pointermove"], ["down", 454, 466, 1721639142082, "pointerdown"], ["up", 454, 466, 1721639142134, "pointerup"], ["move", 454, 467, 1721639142194, "pointermove"], ["move", 454, 471, 1721639142202, "pointermove"], ["move", 451, 475, 1721639142210, "pointermove"], ["move", 448, 479, 1721639142216, "pointermove"], ["move", 444, 483, 1721639142224, "pointermove"], ["move", 439, 488, 1721639142232, "pointermove"], ["move", 434, 494, 1721639142240, "pointermove"], ["move", 429, 499, 1721639142246, "pointermove"], ["move", 424, 503, 1721639142262, "pointermove"], ["move", 419, 508, 1721639142270, "pointermove"], ["move", 415, 512, 1721639142276, "pointermove"], ["move", 411, 516, 1721639142284, "pointermove"], ["move", 407, 520, 1721639142292, "pointermove"], ["move", 404, 524, 1721639142300, "pointermove"], ["move", 402, 527, 1721639142306, "pointermove"], ["move", 399, 530, 1721639142314, "pointermove"], ["move", 398, 531, 1721639142322, "pointermove"], ["move", 397, 531, 1721639142330, "pointermove"], ["move", 396, 532, 1721639142336, "pointermove"], ["move", 395, 534, 1721639142344, "pointermove"], ["move", 394, 535, 1721639142352, "pointermove"], ["move", 393, 535, 1721639142360, "pointermove"], ["move", 392, 536, 1721639142366, "pointermove"], ["move", 391, 538, 1721639142374, "pointermove"], ["move", 390, 539, 1721639142382, "pointermove"], ["move", 389, 539, 1721639142390, "pointermove"], ["move", 387, 540, 1721639142396, "pointermove"], ["move", 386, 542, 1721639142404, "pointermove"], ["move", 384, 543, 1721639142412, "pointermove"], ["move", 383, 546, 1721639142420, "pointermove"], ["move", 381, 547, 1721639142426, "pointermove"], ["move", 379, 550, 1721639142434, "pointermove"], ["move", 378, 551, 1721639142450, "pointermove"], ["move", 377, 551, 1721639142456, "pointermove"], ["move", 376, 554, 1721639142464, "pointermove"], ["move", 376, 555, 1721639142472, "pointermove"], ["move", 375, 556, 1721639142480, "pointermove"], ["move", 373, 558, 1721639142486, "pointermove"], ["move", 372, 559, 1721639142495, "pointermove"], ["move", 371, 562, 1721639142502, "pointermove"], ["move", 370, 563, 1721639142511, "pointermove"], ["move", 370, 564, 1721639142516, "pointermove"], ["move", 369, 566, 1721639142524, "pointermove"], ["down", 369, 566, 1721639142554, "pointerdown"], ["up", 369, 566, 1721639142606, "pointerup"], ["move", 369, 564, 1721639142652, "pointermove"], ["move", 369, 562, 1721639142661, "pointermove"], ["move", 368, 558, 1721639142666, "pointermove"], ["move", 366, 554, 1721639142674, "pointermove"], ["move", 363, 548, 1721639142682, "pointermove"], ["move", 359, 541, 1721639142690, "pointermove"], ["move", 353, 534, 1721639142696, "pointermove"], ["move", 346, 524, 1721639142704, "pointermove"], ["move", 337, 514, 1721639142712, "pointermove"], ["move", 328, 505, 1721639142720, "pointermove"], ["move", 318, 496, 1721639142726, "pointermove"], ["move", 308, 487, 1721639142734, "pointermove"], ["move", 299, 480, 1721639142742, "pointermove"], ["move", 291, 474, 1721639142750, "pointermove"], ["move", 283, 468, 1721639142756, "pointermove"], ["move", 277, 464, 1721639142764, "pointermove"], ["move", 273, 460, 1721639142772, "pointermove"], ["move", 270, 457, 1721639142780, "pointermove"], ["move", 268, 455, 1721639142786, "pointermove"], ["move", 266, 453, 1721639142794, "pointermove"], ["move", 265, 451, 1721639142802, "pointermove"], ["move", 264, 448, 1721639142816, "pointermove"], ["move", 263, 446, 1721639142824, "pointermove"], ["move", 262, 444, 1721639142832, "pointermove"], ["move", 262, 442, 1721639142840, "pointermove"], ["move", 261, 440, 1721639142846, "pointermove"], ["move", 261, 438, 1721639142854, "pointermove"], ["move", 261, 436, 1721639142862, "pointermove"], ["move", 261, 434, 1721639142870, "pointermove"], ["move", 261, 432, 1721639142876, "pointermove"], ["move", 261, 430, 1721639142884, "pointermove"], ["move", 261, 428, 1721639142892, "pointermove"], ["move", 261, 426, 1721639142900, "pointermove"], ["move", 261, 424, 1721639142906, "pointermove"], ["move", 261, 423, 1721639142914, "pointermove"], ["move", 261, 421, 1721639142922, "pointermove"], ["move", 261, 420, 1721639142930, "pointermove"], ["move", 261, 418, 1721639142936, "pointermove"], ["move", 261, 415, 1721639142952, "pointermove"], ["move", 260, 414, 1721639142960, "pointermove"], ["move", 259, 412, 1721639142974, "pointermove"], ["move", 258, 411, 1721639142982, "pointermove"], ["move", 258, 410, 1721639142990, "pointermove"], ["move", 258, 407, 1721639143012, "pointermove"], ["move", 257, 406, 1721639143020, "pointermove"], ["move", 256, 404, 1721639143034, "pointermove"], ["move", 256, 403, 1721639143042, "pointermove"], ["move", 255, 402, 1721639143050, "pointermove"], ["down", 255, 401, 1721639143124, "pointerdown"], ["up", 255, 401, 1721639143192, "pointerup"], ["move", 256, 404, 1721639143252, "pointermove"], ["move", 258, 410, 1721639143260, "pointermove"], ["move", 262, 415, 1721639143266, "pointermove"], ["move", 266, 423, 1721639143274, "pointermove"], ["move", 271, 431, 1721639143282, "pointermove"], ["move", 276, 439, 1721639143290, "pointermove"], ["move", 282, 448, 1721639143296, "pointermove"], ["move", 286, 459, 1721639143304, "pointermove"], ["move", 294, 470, 1721639143312, "pointermove"], ["move", 300, 482, 1721639143320, "pointermove"], ["move", 307, 492, 1721639143326, "pointermove"], ["move", 314, 503, 1721639143334, "pointermove"], ["move", 322, 515, 1721639143342, "pointermove"], ["move", 330, 523, 1721639143348, "pointermove"], ["move", 336, 535, 1721639143356, "pointermove"], ["move", 344, 544, 1721639143372, "pointermove"], ["move", 354, 555, 1721639143378, "pointermove"], ["move", 362, 563, 1721639143386, "pointermove"], ["move", 370, 572, 1721639143394, "pointermove"], ["move", 375, 580, 1721639143402, "pointermove"], ["move", 380, 587, 1721639143408, "pointermove"], ["move", 386, 595, 1721639143416, "pointermove"], ["move", 391, 600, 1721639143424, "pointermove"], ["move", 396, 607, 1721639143433, "pointermove"], ["move", 402, 612, 1721639143438, "pointermove"], ["move", 406, 619, 1721639143446, "pointermove"], ["move", 410, 624, 1721639143454, "pointermove"], ["move", 415, 630, 1721639143462, "pointermove"], ["move", 419, 635, 1721639143468, "pointermove"], ["move", 422, 640, 1721639143476, "pointermove"], ["move", 426, 644, 1721639143484, "pointermove"], ["move", 430, 647, 1721639143492, "pointermove"], ["move", 431, 650, 1721639143498, "pointermove"], ["move", 434, 651, 1721639143506, "pointermove"], ["move", 434, 652, 1721639143514, "pointermove"], ["move", 435, 655, 1721639143522, "pointermove"], ["move", 436, 656, 1721639143528, "pointermove"], ["move", 436, 658, 1721639143536, "pointermove"], ["move", 438, 659, 1721639143544, "pointermove"], ["move", 438, 659, 1721639143552, "pointermove"], ["move", 438, 660, 1721639143574, "pointermove"], ["move", 439, 660, 1721639143656, "pointermove"], ["move", 440, 662, 1721639143672, "pointermove"], ["move", 442, 663, 1721639143678, "pointermove"], ["move", 443, 663, 1721639143686, "pointermove"], ["move", 444, 664, 1721639143695, "pointermove"], ["move", 446, 666, 1721639143702, "pointermove"], ["move", 446, 666, 1721639143708, "pointermove"], ["move", 447, 667, 1721639143716, "pointermove"], ["move", 447, 666, 1721639143717, "mousemove"], ["move", 448, 667, 1721639143724, "pointermove"], ["move", 450, 667, 1721639143732, "pointermove"], ["move", 450, 670, 1721639143746, "pointermove"], ["move", 452, 671, 1721639143754, "pointermove"], ["move", 454, 672, 1721639143762, "pointermove"], ["move", 456, 675, 1721639143768, "pointermove"], ["move", 458, 676, 1721639143776, "pointermove"], ["move", 460, 679, 1721639143784, "pointermove"], ["move", 462, 680, 1721639143792, "pointermove"], ["move", 466, 683, 1721639143798, "pointermove"], ["move", 467, 684, 1721639143806, "pointermove"], ["move", 470, 687, 1721639143814, "pointermove"], ["move", 470, 688, 1721639143822, "pointermove"], ["move", 472, 691, 1721639143828, "pointermove"], ["move", 474, 692, 1721639143836, "pointermove"], ["move", 475, 695, 1721639143844, "pointermove"], ["move", 478, 696, 1721639143852, "pointermove"], ["move", 479, 699, 1721639143858, "pointermove"], ["move", 482, 700, 1721639143866, "pointermove"], ["move", 482, 703, 1721639143874, "pointermove"], ["move", 483, 703, 1721639143882, "pointermove"], ["move", 483, 704, 1721639143888, "pointermove"], ["move", 483, 706, 1721639143912, "pointermove"], ["down", 483, 706, 1721639143965, "pointerdown"], ["focus", 1721639143965], ["up", 483, 706, 1721639144024, "pointerup"]]