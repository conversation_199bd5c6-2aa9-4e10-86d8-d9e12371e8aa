import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 3000,
    proxy: {
      '/api': {
        // Docker环境中使用容器名作为主机名
        // target: process.env.NODE_ENV === 'development' && process.env.DOCKER_ENV
        //   ? 'http://backend:8000'
        //   : 'http://127.0.0.1:8000',
        target: 'https://www.kedaya.xyz',
        changeOrigin: true,
        secure: false
      }
    }
  }
})