"""
卡密管理模块

提供卡密的生成、导出和使用功能：
- 管理员生成卡密
- 管理员导出卡密列表
- 用户使用卡密充值

安全特性：
- 管理员密钥验证
- 输入参数验证
- 限流保护
- 详细的日志记录

作者: API系统
版本: 2.0
"""

import random
import string
import os
import logging
from fastapi import APIRouter, HTTPException, Depends, Request
from datetime import datetime, timezone
from fastapi.responses import PlainTextResponse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from api.db import engine, rate_limit
    from api.models import users, card_keys, CardKeyGenRequest, CardKeyUseRequest
    from api.auth import get_current_user
    from api.utils.common import get_client_ip
    from api.utils.billing import record_recharge
except ImportError as e:
    logger.warning(f"使用相对导入: {e}")
    try:
        from db import engine, rate_limit
        from models import users, card_keys, CardKeyGenRequest, CardKeyUseRequest
        from auth import get_current_user
        from utils.common import get_client_ip
        from utils.billing import record_recharge
    except ImportError as e:
        logger.error(f"导入失败: {e}")
        raise

router = APIRouter(prefix="/api", tags=["card"])

# 常量定义
CARD_KEY_LENGTH = 16
CARD_KEY_SEGMENT_LENGTH = 4
BALANCE_MULTIPLIER = 10000  # 1元 = 10000点
MAX_CARD_GENERATION_COUNT = 1000
MIN_CARD_AMOUNT = 0.01
MAX_CARD_AMOUNT = 10000.0


# 辅助函数
def validate_admin_key(admin_key: str) -> bool:
    """验证管理员密钥"""
    if not admin_key:
        return False

    admin_secret = os.getenv("ADMIN_KEY", "admin-secret-key")
    if not admin_secret or admin_secret == "admin-secret-key":
        logger.warning("使用默认管理员密钥，请在生产环境中更改")

    return admin_key == admin_secret


def generate_card_key() -> str:
    """生成卡密"""
    # 生成随机卡密
    card_key = "".join(
        random.choices(string.ascii_uppercase + string.digits, k=CARD_KEY_LENGTH)
    )
    # 用连字符分隔，提高可读性
    return "-".join(
        [
            card_key[i : i + CARD_KEY_SEGMENT_LENGTH]
            for i in range(0, len(card_key), CARD_KEY_SEGMENT_LENGTH)
        ]
    )


def validate_card_generation_request(request: CardKeyGenRequest) -> None:
    """验证卡密生成请求"""
    if request.count <= 0 or request.count > MAX_CARD_GENERATION_COUNT:
        raise HTTPException(
            status_code=400, detail=f"卡密数量必须在1-{MAX_CARD_GENERATION_COUNT}之间"
        )

    if request.amount < MIN_CARD_AMOUNT or request.amount > MAX_CARD_AMOUNT:
        raise HTTPException(
            status_code=400,
            detail=f"卡密金额必须在{MIN_CARD_AMOUNT}-{MAX_CARD_AMOUNT}之间",
        )


def validate_card_key_format(card_key: str) -> None:
    """验证卡密格式"""
    if not card_key:
        raise HTTPException(status_code=400, detail="卡密不能为空")

    # 移除连字符检查格式
    clean_key = card_key.replace("-", "")
    if len(clean_key) != CARD_KEY_LENGTH:
        raise HTTPException(status_code=400, detail="卡密格式不正确")

    if not clean_key.isalnum():
        raise HTTPException(status_code=400, detail="卡密只能包含字母和数字")


@router.post("/admin/generate_cards")
def generate_card_keys(
    request: CardKeyGenRequest,
    admin_key: str = None,
):
    """
    管理员生成卡密
    """
    try:
        # 验证管理员密钥
        if not validate_admin_key(admin_key):
            logger.warning("无效的管理员密钥尝试")
            raise HTTPException(status_code=403, detail="无效的管理员密钥")

        # 验证请求参数
        validate_card_generation_request(request)

        # 生成指定数量的卡密
        cards = []
        failed_cards = []

        with engine.connect() as db:
            with db.begin():
                for i in range(request.count):
                    try:
                        # 生成唯一卡密（最多重试10次）
                        max_retries = 10
                        card_key = None

                        for retry in range(max_retries):
                            candidate_key = generate_card_key()

                            # 检查卡密是否已存在
                            existing = db.execute(
                                card_keys.select().where(
                                    card_keys.c.card_key == candidate_key
                                )
                            ).fetchone()

                            if not existing:
                                card_key = candidate_key
                                break

                        if not card_key:
                            failed_cards.append(
                                f"第{i + 1}张卡密生成失败：无法生成唯一卡密"
                            )
                            continue

                        # 插入数据库
                        db.execute(
                            card_keys.insert().values(
                                card_key=card_key,
                                amount=request.amount,
                                used=False,
                                created_at=datetime.now(timezone.utc),
                            )
                        )
                        cards.append(card_key)

                    except Exception as e:
                        logger.error(f"生成第{i + 1}张卡密时出错: {str(e)}")
                        failed_cards.append(f"第{i + 1}张卡密生成失败：{str(e)}")

        logger.info(f"管理员生成了{len(cards)}张卡密，金额：{request.amount}元")

        result = {
            "card_keys": cards,
            "amount": request.amount,
            "count": len(cards),
            "requested_count": request.count,
        }

        if failed_cards:
            result["failed"] = failed_cards
            result["message"] = f"成功生成{len(cards)}张卡密，{len(failed_cards)}张失败"

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成卡密时发生未预期错误: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误，请稍后重试")


@router.get("/admin/export_cards")
def export_card_keys(admin_key: str = None, status: str = "unused"):
    """
    导出卡密列表
    """
    try:
        # 验证管理员密钥
        if not validate_admin_key(admin_key):
            logger.warning("无效的管理员密钥尝试导出卡密")
            raise HTTPException(status_code=403, detail="无效的管理员密钥")

        # 验证状态参数
        valid_statuses = ["unused", "used", "all"]
        if status not in valid_statuses:
            raise HTTPException(
                status_code=400,
                detail=f"无效的状态参数，必须是: {', '.join(valid_statuses)}",
            )

        # 导出卡密
        cards_text = ""
        card_count = 0

        with engine.connect() as db:
            try:
                # 根据状态过滤
                query = card_keys.select()
                if status == "unused":
                    query = query.where(~card_keys.c.used)  # 使用 ~ 代替 == False
                elif status == "used":
                    query = query.where(card_keys.c.used)  # 使用直接布尔值代替 == True

                # 按创建时间排序
                query = query.order_by(card_keys.c.created_at.desc())

                result = db.execute(query)
                for row in result:
                    card_count += 1
                    # 添加每张卡密的详细信息
                    if status != "all":
                        cards_text += f"{row.card_key} - ¥{row.amount}\n"
                    else:
                        used_status = "已使用" if row.used else "未使用"
                        used_by = (
                            f" 被 {row.used_by}" if row.used and row.used_by else ""
                        )
                        used_time = (
                            f" 于 {row.used_at.strftime('%Y-%m-%d %H:%M:%S')}"
                            if row.used_at
                            else ""
                        )
                        cards_text += f"{row.card_key} - ¥{row.amount} - {used_status}{used_by}{used_time}\n"

            except Exception as e:
                logger.error(f"查询卡密数据时出错: {str(e)}")
                raise HTTPException(status_code=500, detail="查询数据失败")

        # 添加统计信息
        if cards_text:
            header = f"# 卡密导出报告 - {status.upper()}\n"
            header += f"# 导出时间: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}\n"
            header += f"# 总计: {card_count} 张卡密\n\n"
            cards_text = header + cards_text
        else:
            cards_text = f"# 没有找到状态为 '{status}' 的卡密\n"

        logger.info(f"管理员导出了{card_count}张{status}状态的卡密")

        # 返回纯文本格式的卡密列表
        return PlainTextResponse(content=cards_text)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出卡密时发生未预期错误: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误，请稍后重试")


@router.post("/recharge_card")
async def recharge_card(
    request: CardKeyUseRequest,
    current_user: dict = Depends(get_current_user),
    req: Request = None,
):
    """
    用户输入卡密充值余额
    """
    try:
        # 验证卡密格式
        validate_card_key_format(request.card_key)

        # 获取客户端IP和用户信息
        client_ip = get_client_ip(req) if req else "unknown"
        username = current_user["username"]

        with rate_limit(f"recharge_card:{client_ip}", max_requests=5, expire=300):
            with engine.connect() as db:
                with db.begin():
                    try:
                        # 查找卡密
                        result = db.execute(
                            card_keys.select().where(
                                card_keys.c.card_key == request.card_key
                            )
                        )
                        card = result.fetchone()

                        # 验证卡密
                        if not card:
                            logger.warning(
                                f"用户 {username} 尝试使用不存在的卡密: {request.card_key}"
                            )
                            raise HTTPException(status_code=400, detail="卡密不存在")

                        if card.used:
                            used_info = (
                                f"已被 {card.used_by} 于 {card.used_at} 使用"
                                if card.used_by and card.used_at
                                else "已被使用"
                            )
                            logger.warning(
                                f"用户 {username} 尝试使用已使用的卡密: {request.card_key} ({used_info})"
                            )
                            raise HTTPException(
                                status_code=400, detail=f"卡密{used_info}"
                            )

                        # 计算新余额
                        current_balance = current_user["balance"]
                        recharge_points = card.amount * BALANCE_MULTIPLIER
                        new_balance = current_balance + recharge_points

                        # 更新卡密状态
                        db.execute(
                            card_keys.update()
                            .where(card_keys.c.card_key == request.card_key)
                            .values(
                                used=True,
                                used_by=username,
                                used_at=datetime.now(timezone.utc),
                            )
                        )

                        # 更新用户余额
                        db.execute(
                            users.update()
                            .where(users.c.username == username)
                            .values(balance=new_balance)
                        )

                        logger.info(
                            f"用户 {username} 成功使用卡密 {request.card_key} 充值 {card.amount}元"
                        )

                    except HTTPException:
                        raise
                    except Exception as e:
                        logger.error(f"数据库操作失败: {str(e)}")
                        raise HTTPException(
                            status_code=500, detail="充值处理失败，请稍后重试"
                        )

                # 在事务外记录充值记录（避免影响主要业务流程）
                try:
                    await record_recharge(
                        username=username,
                        details=f"卡密充值 {request.card_key}",
                        amount=card.amount,
                    )
                except Exception as e:
                    logger.error(f"记录充值失败: {str(e)}")
                    # 不影响主流程，仅记录错误

                return {
                    "message": f"充值成功，已添加 ¥{card.amount} ({recharge_points}点) 到账户余额",
                    "amount": card.amount,
                    "points": recharge_points,
                    "balance": new_balance,
                    "card_key": request.card_key,
                }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"充值卡密时发生未预期错误: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误，请稍后重试")
