"""
性能监控工具
用于监控Microsoft邮箱API的性能指标
"""

import time
import logging
from typing import Dict, Optional
from datetime import datetime, timezone
from contextlib import contextmanager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""

    @staticmethod
    @contextmanager
    def monitor_operation(operation_name: str, account: str = None):
        """
        监控操作性能的上下文管理器

        Args:
            operation_name: 操作名称
            account: 邮箱账号（可选）
        """
        start_time = time.time()
        start_datetime = datetime.now(timezone.utc)

        try:
            logger.info(
                f"开始执行操作: {operation_name}"
                + (f" (账号: {account})" if account else "")
            )
            yield

        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(
                f"操作失败: {operation_name}, 耗时: {duration:.2f}秒, 错误: {str(e)}"
            )
            raise

        else:
            end_time = time.time()
            duration = end_time - start_time

            # 根据耗时给出不同级别的日志
            if duration < 2.0:
                logger.info(f"操作完成: {operation_name}, 耗时: {duration:.2f}秒 ✓")
            elif duration < 5.0:
                logger.warning(
                    f"操作完成: {operation_name}, 耗时: {duration:.2f}秒 (较慢)"
                )
            else:
                logger.warning(
                    f"操作完成: {operation_name}, 耗时: {duration:.2f}秒 (很慢)"
                )

    @staticmethod
    def log_cache_hit(account: str, operation: str = "login"):
        """
        记录缓存命中

        Args:
            account: 邮箱账号
            operation: 操作类型
        """
        logger.info(f"缓存命中 ⚡ - {operation}: {account}")

    @staticmethod
    def log_cache_miss(account: str, operation: str = "login"):
        """
        记录缓存未命中

        Args:
            account: 邮箱账号
            operation: 操作类型
        """
        logger.info(f"缓存未命中 🔄 - {operation}: {account}")

    @staticmethod
    def log_performance_summary(
        total_emails: int, new_emails: int, cached: bool, duration: float, account: str
    ):
        """
        记录性能摘要

        Args:
            total_emails: 总邮件数
            new_emails: 新邮件数
            cached: 是否使用缓存
            duration: 总耗时
            account: 邮箱账号
        """
        cache_status = "缓存" if cached else "完整登录"
        efficiency = "高效" if duration < 3.0 else "一般" if duration < 8.0 else "较慢"

        logger.info(
            f"性能摘要 📊 - 账号: {account}, "
            f"邮件: {total_emails}封(新增{new_emails}封), "
            f"方式: {cache_status}, "
            f"耗时: {duration:.2f}秒, "
            f"效率: {efficiency}"
        )


# 装饰器版本的性能监控
def monitor_performance(operation_name: str):
    """
    性能监控装饰器

    Args:
        operation_name: 操作名称
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceMonitor.monitor_operation(operation_name):
                return func(*args, **kwargs)

        return wrapper

    return decorator
