import json
import random
import string
from fastapi import APIRouter, HTTPException, Request, Depends
import sqlalchemy
from sqlalchemy.exc import IntegrityError
from datetime import timedelta, datetime, timezone
from jose import JWTError, jwt
from fastapi.security import HTTPAuthorizationCredentials
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.models import users, UserCreate
    from api.db import get_db, with_db_retry, rate_limit
    from api.utils.email import get_emails, send_email_async
    from api.auth import (
        create_access_token,
        pwd_context,
        get_current_user,
        get_client_ip,
        verify_turnstile,
        security,
        SECRET_KEY,
        ALGORITHM,
    )
except:
    from models import users, UserCreate
    from db import get_db, with_db_retry, rate_limit
    from utils.email import get_emails, send_email_async
    from auth import (
        create_access_token,
        pwd_context,
        get_current_user,
        get_client_ip,
        verify_turnstile,
        security,
        SECRET_KEY,
        ALGORITHM,
    )
router = APIRouter(prefix="/api", tags=["auth"])


@router.post("/register")
async def register(request: Request):
    try:
        body_raw = await request.body()
        body = json.loads(body_raw)
        email = body.get("email")  # 保留email参数名
        password = body.get("password")
        invite_code = body.get("inviteCode")
        turnstile_token = body.get("turnstileToken")
        verification_code = body.get("verificationCode")

        if not email or not password or not verification_code:
            raise HTTPException(status_code=400, detail="邮箱、密码和验证码不能为空")

        # 验证 Turnstile token
        if not turnstile_token:
            raise HTTPException(status_code=400, detail="请完成人机验证")

        if not await verify_turnstile(turnstile_token):
            raise HTTPException(status_code=400, detail="人机验证失败")

        if "qq.com" not in email.lower():
            raise HTTPException(status_code=400, detail="非QQ邮箱无法注册")

        client_ip = get_client_ip(request)

        # 我们先在rate_limit外部检查用户名是否已存在和验证邀请码
        with get_db() as check_conn:
            # 检查用户名是否已存在
            result = check_conn.execute(users.select().where(users.c.username == email))
            existing_user = result.fetchone()
            if existing_user:
                raise HTTPException(status_code=400, detail="邮箱已被注册")

            # 验证邀请码(邀请人邮箱)
            inviter = None
            if invite_code:
                result = check_conn.execute(
                    users.select().where(users.c.username == invite_code)
                )
                inviter = result.fetchone()
                if not inviter:
                    raise HTTPException(status_code=400, detail="邀请码无效")

            # 验证验证码
            if not check_verification_code(email, verification_code):
                raise HTTPException(status_code=400, detail="验证码错误或已过期")

        # 生成密码哈希
        hashed_password = pwd_context.hash(password)

        # 在rate_limit中执行插入操作
        with rate_limit(f"register:{client_ip}", max_requests=5, expire=60):
            with get_db() as conn:
                transaction = conn.begin()
                try:
                    # 尝试重置序列以避免主键冲突
                    try:
                        # 获取users表当前最大id
                        max_id_result = conn.execute(
                            sqlalchemy.select(sqlalchemy.func.max(users.c.id))
                        ).fetchone()
                        max_id = max_id_result[0] if max_id_result[0] is not None else 0

                        # 将序列重置为比最大id大的值
                        conn.execute(
                            sqlalchemy.text(
                                f"SELECT setval('users_id_seq', {max_id + 1}, false)"
                            )
                        )
                    except Exception as e:
                        logger.error(f"重置序列时出错: {str(e)}")
                        # 继续执行，不要因为序列重置失败而中断注册流程

                    # 生成永久有效token
                    token = create_access_token(
                        data={"sub": email},
                        expires_delta=None,  # None表示永久有效
                    )

                    # 插入用户记录，不要显式指定id，让数据库自动生成
                    user_values = {
                        "username": email,  # 使用邮箱作为用户名
                        "password_hash": hashed_password,
                        "balance": 500.0,
                        "token": token,
                        "inviter": invite_code if invite_code else None,
                        "invite_reward": 0.0,
                    }
                    conn.execute(users.insert().values(**user_values))

                    # 清理验证码
                    clear_verification_code(email)

                    transaction.commit()
                except Exception as e:
                    transaction.rollback()
                    logger.error(f"用户注册失败: {str(e)}")
                    raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")

                return {
                    "email": email,  # 保留email字段返回
                    "balance": 500.0,
                    "token": token,
                    "inviter": invite_code if invite_code else None,
                    "invite_reward": 0.0,
                    "message": "注册成功",
                }
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="无效的 JSON 数据")


# 存储验证码的字典和过期时间
verification_codes_memory = {}  # 结构: {'email': {'code': '123456', 'expires_at': datetime}}


# 检查验证码是否有效
def check_verification_code(email, code):
    if email not in verification_codes_memory:
        return False

    code_info = verification_codes_memory[email]
    if code_info["code"] != code:
        return False

    # 检查是否过期（5分钟有效期）
    if datetime.now(timezone.utc) > code_info["expires_at"]:
        # 清理过期验证码
        del verification_codes_memory[email]
        return False

    return True


# 清理验证码
def clear_verification_code(email):
    if email in verification_codes_memory:
        del verification_codes_memory[email]


@router.post("/login")
async def login(request: Request):
    try:
        body_raw = await request.body()
        body = json.loads(body_raw)
        email = body.get("email")  # 保留email参数名
        password = body.get("password")
        turnstile_token = body.get("turnstileToken")

        if not email or not password:
            raise HTTPException(status_code=400, detail="邮箱和密码不能为空")

        # 验证 Turnstile token
        if not turnstile_token:
            raise HTTPException(status_code=400, detail="请完成人机验证")

        if not await verify_turnstile(turnstile_token):
            raise HTTPException(status_code=400, detail="人机验证失败")

        client_ip = get_client_ip(request)
        with rate_limit(f"login:{client_ip}", max_requests=5, expire=300):
            with get_db() as conn:
                with conn.begin():
                    result = conn.execute(
                        users.select().where(users.c.username == email)
                    )
                    user = result.fetchone()

                    if not user:
                        raise HTTPException(status_code=400, detail="邮箱或密码错误")

                    # 创建映射字典
                    user_dict = {}
                    for column in users.columns.keys():
                        user_dict[column] = user._mapping[column]

                    if not user_dict or not pwd_context.verify(
                        password, user_dict["password_hash"]
                    ):
                        raise HTTPException(status_code=400, detail="邮箱或密码错误")

                    # 检查token是否有效
                    token_valid = True
                    if user_dict["token"]:
                        try:
                            # 尝试解码token检查是否有效
                            jwt.decode(
                                user_dict["token"], SECRET_KEY, algorithms=[ALGORITHM]
                            )
                        except JWTError:
                            # Token无效或已过期
                            token_valid = False
                    else:
                        token_valid = False

                    # 如果token不存在或已过期，生成新的永久token
                    if not token_valid:
                        new_token = create_access_token(
                            data={"sub": email},
                            expires_delta=None,  # None表示永久有效
                        )

                        # 更新token
                        conn.execute(
                            users.update()
                            .where(users.c.username == email)
                            .values(token=new_token)
                        )

                        # 更新用户字典中的token
                        user_dict["token"] = new_token

                    return {
                        "email": email,  # 保留email字段返回
                        "balance": user_dict["balance"],
                        "token": user_dict["token"],
                        "inviter": user_dict["inviter"],
                        "invite_reward": user_dict["invite_reward"],
                        "message": "登录成功",
                    }
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="无效的 JSON 数据")


@router.post("/reset-token")
def reset_token(
    current_user: dict = Depends(get_current_user), request: Request = None
):
    client_ip = get_client_ip(request) if request else "unknown"

    with rate_limit(f"reset_token:{client_ip}", max_requests=5, expire=300):

        @with_db_retry(max_retries=3)
        def do_reset_token():
            with get_db() as conn:
                with conn.begin():
                    # 先将旧 token 设置为 null
                    conn.execute(
                        users.update()
                        .where(users.c.username == current_user["username"])
                        .values(token=None)
                    )

                    # 生成新的 token
                    new_token = create_access_token(
                        data={"sub": current_user["username"]},
                        expires_delta=timedelta(days=365),
                    )

                    # 更新数据库中的 token
                    try:
                        conn.execute(
                            users.update()
                            .where(users.c.username == current_user["username"])
                            .values(token=new_token)
                        )
                    except IntegrityError:
                        # 如果出现唯一性冲突，重新生成 token
                        raise sqlalchemy.exc.OperationalError("token 生成冲突，请重试")

                    # 验证更新是否成功
                    result = conn.execute(
                        users.select().where(
                            users.c.username == current_user["username"]
                        )
                    )
                    updated_user = result.fetchone()

                    # 创建映射字典
                    if updated_user:
                        updated_dict = {}
                        for column in users.columns.keys():
                            updated_dict[column] = updated_user._mapping[column]

                    if not updated_user or updated_dict["token"] != new_token:
                        raise sqlalchemy.exc.OperationalError("token 更新失败")

                    return {"token": new_token, "username": current_user["username"]}

        try:
            return do_reset_token()
        except Exception as e:
            logger.error(f"Token重置：重置序列时出错: {str(e)}")
            raise HTTPException(status_code=500, detail="Token重置失败，请重试")


# 添加发送验证码的路由
@router.post("/send-verification-code")
async def send_verification_code(request: Request):
    try:
        body_raw = await request.body()
        body = json.loads(body_raw)
        email = body.get("email")  # 保留email参数名
        turnstile_token = body.get("turnstileToken")

        if not email:
            raise HTTPException(status_code=400, detail="邮箱不能为空")

        # 验证 Turnstile token
        if not turnstile_token:
            raise HTTPException(status_code=400, detail="请完成人机验证")

        if not await verify_turnstile(turnstile_token):
            raise HTTPException(status_code=400, detail="人机验证失败")

        if "qq.com" not in email.lower():
            raise HTTPException(status_code=400, detail="非QQ邮箱无法注册")

        client_ip = get_client_ip(request)

        # 检查邮箱是否已注册
        with get_db() as conn:
            result = conn.execute(users.select().where(users.c.username == email))
            if result.fetchone():
                raise HTTPException(status_code=400, detail="该邮箱已注册")

        # 生成验证码
        verification_code = "".join(random.choices(string.digits, k=6))

        # 在rate_limit中执行验证码发送和存储操作
        with rate_limit(f"send_code:{client_ip}", max_requests=3, expire=60):
            try:
                # 尝试发送验证码邮件
                try:
                    # 发送邮件
                    subject = "可达鸭API系统注册验证码"
                    content = f"您的注册验证码是：{verification_code} 有效期5分钟，请勿泄露给他人。"

                    await send_email_async(
                        "system", email, subject, content, "<EMAIL>"
                    )
                except Exception as e:
                    logger.error(f"发送验证码邮件失败: {str(e)}")
                    raise HTTPException(
                        status_code=500, detail="发送验证码邮件失败，请稍后重试"
                    )

                # 存储验证码到内存
                verification_codes_memory[email] = {
                    "code": verification_code,
                    "expires_at": datetime.now(timezone.utc) + timedelta(minutes=5),
                }

                return {"message": "验证码已发送到您的邮箱，请查收"}
            except Exception as e:
                logger.error(f"验证码发送/保存失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"验证码发送失败: {str(e)}")
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="无效的 JSON 数据")


# 获取用户信息
@router.get("/user")
def get_user_info(current_user: dict = Depends(get_current_user)):
    return {
        "username": current_user["username"],
        "balance": current_user["balance"],
        "inviter": current_user["inviter"],
        "invite_reward": current_user["invite_reward"],
    }


# 获取用户消费记录
@router.get("/consumption-records")
def get_consumption_records(current_user: dict = Depends(get_current_user)):
    try:
        with get_db() as conn:
            # 查询最近30天的消费记录，按时间倒序排列
            query = sqlalchemy.text(
                """
                SELECT details, amount, created_at 
                FROM consumption_records 
                WHERE username = :username 
                ORDER BY created_at DESC
                """
            )
            result = conn.execute(query, {"username": current_user["username"]})
            records = []
            for row in result:
                # 将datetime对象转换为ISO格式字符串
                created_at = row[2].replace(tzinfo=timezone.utc).isoformat()
                records.append(
                    {"details": row[0], "amount": row[1], "created_at": created_at}
                )
            return {"records": records}
    except Exception as e:
        logger.error(f"获取消费记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取消费记录失败")


# 忘记密码 - 发送重置验证码
@router.post("/forgot-password")
async def forgot_password(request: Request):
    try:
        body_raw = await request.body()
        body = json.loads(body_raw)
        email = body.get("email")
        turnstile_token = body.get("turnstileToken")

        if not email:
            raise HTTPException(status_code=400, detail="邮箱不能为空")

        # 验证 Turnstile token
        if not turnstile_token:
            raise HTTPException(status_code=400, detail="请完成人机验证")

        if not await verify_turnstile(turnstile_token):
            raise HTTPException(status_code=400, detail="人机验证失败")

        client_ip = get_client_ip(request)

        # 检查邮箱是否已注册
        with get_db() as conn:
            result = conn.execute(users.select().where(users.c.username == email))
            user = result.fetchone()
            if not user:
                raise HTTPException(status_code=400, detail="该邮箱未注册")

        # 生成重置验证码
        reset_code = "".join(random.choices(string.digits, k=6))

        # 在rate_limit中执行验证码发送和存储操作
        with rate_limit(f"forgot_password:{client_ip}", max_requests=3, expire=60):
            try:
                # 发送重置密码邮件
                subject = "可达鸭API系统密码重置验证码"
                content = f"您的密码重置验证码是：{reset_code} 有效期5分钟，请勿泄露给他人。如非本人操作，请忽略此邮件。"

                await send_email_async(
                    "system", email, subject, content, "<EMAIL>"
                )

                # 存储重置验证码到内存（使用特殊前缀区分）
                verification_codes_memory[f"reset_{email}"] = {
                    "code": reset_code,
                    "expires_at": datetime.now(timezone.utc) + timedelta(minutes=5),
                }

                return {"message": "密码重置验证码已发送到您的邮箱，请查收"}
            except Exception as e:
                logger.error(f"发送重置验证码失败: {str(e)}")
                raise HTTPException(
                    status_code=500, detail="发送验证码失败，请稍后重试"
                )
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="无效的 JSON 数据")


# 重置密码
@router.post("/reset-password")
async def reset_password(request: Request):
    try:
        body_raw = await request.body()
        body = json.loads(body_raw)
        email = body.get("email")
        verification_code = body.get("verificationCode")
        new_password = body.get("newPassword")
        turnstile_token = body.get("turnstileToken")

        if not email or not verification_code or not new_password:
            raise HTTPException(status_code=400, detail="邮箱、验证码和新密码不能为空")

        # 验证 Turnstile token
        if not turnstile_token:
            raise HTTPException(status_code=400, detail="请完成人机验证")

        if not await verify_turnstile(turnstile_token):
            raise HTTPException(status_code=400, detail="人机验证失败")

        client_ip = get_client_ip(request)

        # 验证重置验证码
        reset_key = f"reset_{email}"
        if not check_verification_code_by_key(reset_key, verification_code):
            raise HTTPException(status_code=400, detail="验证码错误或已过期")

        with rate_limit(f"reset_password:{client_ip}", max_requests=5, expire=300):
            with get_db() as conn:
                with conn.begin():
                    # 检查用户是否存在
                    result = conn.execute(
                        users.select().where(users.c.username == email)
                    )
                    user = result.fetchone()
                    if not user:
                        raise HTTPException(status_code=400, detail="用户不存在")

                    # 生成新密码哈希
                    new_password_hash = pwd_context.hash(new_password)

                    # 生成新的token（重置密码后需要重新登录）
                    new_token = create_access_token(
                        data={"sub": email},
                        expires_delta=None,  # None表示永久有效
                    )

                    # 更新密码和token
                    conn.execute(
                        users.update()
                        .where(users.c.username == email)
                        .values(password_hash=new_password_hash, token=new_token)
                    )

                    # 清理重置验证码
                    clear_verification_code_by_key(reset_key)

                    return {"message": "密码重置成功，请使用新密码登录"}
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="无效的 JSON 数据")


# 辅助函数：通过key检查验证码
def check_verification_code_by_key(key, code):
    if key not in verification_codes_memory:
        return False

    code_info = verification_codes_memory[key]
    if code_info["code"] != code:
        return False

    # 检查是否过期（5分钟有效期）
    if datetime.now(timezone.utc) > code_info["expires_at"]:
        # 清理过期验证码
        del verification_codes_memory[key]
        return False

    return True


# 辅助函数：通过key清理验证码
def clear_verification_code_by_key(key):
    if key in verification_codes_memory:
        del verification_codes_memory[key]


# 修改密码（已登录用户）
@router.post("/change-password")
async def change_password(
    request: Request, current_user: dict = Depends(get_current_user)
):
    try:
        body_raw = await request.body()
        body = json.loads(body_raw)
        current_password = body.get("currentPassword")
        new_password = body.get("newPassword")
        turnstile_token = body.get("turnstileToken")

        if not current_password or not new_password:
            raise HTTPException(status_code=400, detail="当前密码和新密码不能为空")

        # 验证 Turnstile token
        if not turnstile_token:
            raise HTTPException(status_code=400, detail="请完成人机验证")

        if not await verify_turnstile(turnstile_token):
            raise HTTPException(status_code=400, detail="人机验证失败")

        if len(new_password) < 6:
            raise HTTPException(status_code=400, detail="新密码长度不能少于6位")

        client_ip = get_client_ip(request)

        with rate_limit(f"change_password:{client_ip}", max_requests=5, expire=300):
            with get_db() as conn:
                with conn.begin():
                    # 获取用户当前信息
                    result = conn.execute(
                        users.select().where(
                            users.c.username == current_user["username"]
                        )
                    )
                    user = result.fetchone()

                    if not user:
                        raise HTTPException(status_code=400, detail="用户不存在")

                    # 创建映射字典
                    user_dict = {}
                    for column in users.columns.keys():
                        user_dict[column] = user._mapping[column]

                    # 验证当前密码
                    if not pwd_context.verify(
                        current_password, user_dict["password_hash"]
                    ):
                        raise HTTPException(status_code=400, detail="当前密码错误")

                    # 检查新密码是否与当前密码相同
                    if pwd_context.verify(new_password, user_dict["password_hash"]):
                        raise HTTPException(
                            status_code=400, detail="新密码不能与当前密码相同"
                        )

                    # 生成新密码哈希
                    new_password_hash = pwd_context.hash(new_password)

                    # 更新密码
                    conn.execute(
                        users.update()
                        .where(users.c.username == current_user["username"])
                        .values(password_hash=new_password_hash)
                    )

                    return {"message": "密码修改成功"}
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="无效的 JSON 数据")
