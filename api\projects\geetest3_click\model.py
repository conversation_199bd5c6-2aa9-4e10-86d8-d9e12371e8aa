# -*- coding: utf-8 -*-
import os
import numpy as np
from typing import Dict, List, Tuple, Any
from io import BytesIO

try:
    import onnxruntime
except ImportError:
    print("警告: onnxruntime未安装，模型功能将不可用")
    onnxruntime = None

try:
    from PIL import Image
except ImportError:
    print("警告: PIL未安装，图像处理功能将不可用")
    Image = None


class Model:
    """YOLO和Siamese模型处理类"""

    def __init__(self):
        self.img = None
        self.yolo = None
        self.Siamese = None
        self.classes = ["big", "small"]
        self.color_palette = np.random.uniform(0, 255, size=(len(self.classes), 3))

        # 初始化模型
        self._init_models()

    @staticmethod
    def _calculate_iou(box1, box2):
        """计算两个边界框的IoU"""
        x1, y1, w1, h1 = box1
        x2, y2, w2, h2 = box2

        # 计算交集
        x_left = max(x1, x2)
        y_top = max(y1, y2)
        x_right = min(x1 + w1, x2 + w2)
        y_bottom = min(y1 + h1, y2 + h2)

        if x_right < x_left or y_bottom < y_top:
            return 0.0

        intersection = (x_right - x_left) * (y_bottom - y_top)

        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    @staticmethod
    def _nms_boxes(boxes, scores, score_threshold, iou_threshold):
        """简单的非极大值抑制实现"""
        if not boxes:
            return []

        # 按分数排序
        indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)

        keep = []
        while indices:
            current = indices.pop(0)
            keep.append(current)

            # 移除与当前框IoU过高的框
            indices = [
                i
                for i in indices
                if Model._calculate_iou(boxes[current], boxes[i]) < iou_threshold
            ]

        return keep

    def _init_models(self):
        """初始化ONNX模型"""
        if onnxruntime is None:
            print("警告: onnxruntime不可用，无法加载模型")
            return

        try:
            # 获取当前文件目录
            current_dir = os.path.dirname(os.path.abspath(__file__))

            # YOLO模型路径
            yolo_path = os.path.join(current_dir, "yolov8s.onnx")
            if os.path.exists(yolo_path):
                self.yolo = onnxruntime.InferenceSession(yolo_path)
                print("YOLO模型加载成功")
            else:
                print(f"警告: YOLO模型文件不存在: {yolo_path}")
                self.yolo = None

            # Siamese模型路径
            siamese_path = os.path.join(current_dir, "siamese.onnx")
            if os.path.exists(siamese_path):
                self.Siamese = onnxruntime.InferenceSession(siamese_path)
                print("Siamese模型加载成功")
            else:
                print(f"警告: Siamese模型文件不存在: {siamese_path}")
                self.Siamese = None

        except Exception as e:
            print(f"模型初始化失败: {e}")
            self.yolo = None
            self.Siamese = None

    def detect(self, img: bytes) -> Tuple[Dict[int, Any], List[List[int]]]:
        """
        使用YOLO模型检测图片中的目标

        :param img: 图片字节数据
        :return: (小图字典, 大图边界框列表)
        """
        if self.yolo is None or Image is None:
            print("YOLO模型或PIL未加载，返回空结果")
            return {}, []

        try:
            confidence_thres = 0.8
            iou_thres = 0.8

            # 获取模型输入信息
            model_inputs = self.yolo.get_inputs()
            input_shape = model_inputs[0].shape
            input_width = input_shape[2]
            input_height = input_shape[3]

            # 使用PIL解码图片
            img_pil = Image.open(BytesIO(img)).convert("RGB")
            self.img = np.array(img_pil)
            img_height, img_width = self.img.shape[:2]

            # 预处理图片
            img_processed = img_pil.resize((input_width, input_height))
            image_data = np.array(img_processed) / 255.0
            image_data = np.transpose(image_data, (2, 0, 1))
            image_data = np.expand_dims(image_data, axis=0).astype(np.float32)

            # 模型推理
            input_dict = {model_inputs[0].name: image_data}
            output = self.yolo.run(None, input_dict)
            outputs = np.transpose(np.squeeze(output[0]))
            rows = outputs.shape[0]

            # 解析检测结果
            boxes, scores, class_ids = [], [], []
            x_factor = img_width / input_width
            y_factor = img_height / input_height

            for i in range(rows):
                classes_scores = outputs[i][4:]
                max_score = np.amax(classes_scores)
                if max_score >= confidence_thres:
                    class_id = np.argmax(classes_scores)
                    x, y, w, h = (
                        outputs[i][0],
                        outputs[i][1],
                        outputs[i][2],
                        outputs[i][3],
                    )
                    left = int((x - w / 2) * x_factor)
                    top = int((y - h / 2) * y_factor)
                    width = int(w * x_factor)
                    height = int(h * y_factor)
                    class_ids.append(class_id)
                    scores.append(max_score)
                    boxes.append([left, top, width, height])

            # 非极大值抑制
            indices = self._nms_boxes(boxes, scores, confidence_thres, iou_thres)
            new_boxes = [boxes[i] for i in indices]

            # 分类小图和大图
            small_imgs, big_img_boxes = {}, []
            for i in new_boxes:
                cropped = self.img[i[1] : i[1] + i[3], i[0] : i[0] + i[2]]
                if cropped.shape[0] < 35 and cropped.shape[1] < 35:
                    small_imgs[i[0]] = cropped
                else:
                    big_img_boxes.append(i)

            return small_imgs, big_img_boxes

        except Exception as e:
            print(f"图片检测失败: {e}")
            return {}, []

    @staticmethod
    def preprocess_image(img, size=(105, 105)):
        """
        预处理图片用于Siamese网络

        :param img: 输入图片 (numpy数组)
        :param size: 目标尺寸
        :return: 预处理后的图片数据
        """
        try:
            if Image is None:
                print("PIL未安装，无法预处理图片")
                return None

            # 将numpy数组转换为PIL图像
            if isinstance(img, np.ndarray):
                img_pil = Image.fromarray(img.astype("uint8"))
            else:
                img_pil = img

            # 调整大小
            img_resized = img_pil.resize(size)
            img_normalized = np.array(img_resized) / 255.0
            img_transposed = np.transpose(img_normalized, (2, 0, 1))
            img_expanded = np.expand_dims(img_transposed, axis=0).astype(np.float32)
            return img_expanded
        except Exception as e:
            print(f"图片预处理失败: {e}")
            return None

    def siamese(
        self, small_imgs: Dict[int, Any], big_img_boxes: List[List[int]]
    ) -> List[List[int]]:
        """
        使用Siamese网络进行图片匹配

        :param small_imgs: 小图字典
        :param big_img_boxes: 大图边界框列表
        :return: 匹配结果坐标列表
        """
        if self.Siamese is None:
            print("Siamese模型未加载，返回空结果")
            return []

        try:
            # 预处理所有小图
            preprocessed_small_imgs = {}
            for i in sorted(small_imgs):
                processed = self.preprocess_image(small_imgs[i])
                if processed is not None:
                    preprocessed_small_imgs[i] = processed

            result_list = []

            # 对每个小图与大图进行匹配
            for i in sorted(preprocessed_small_imgs):
                image_data_1 = preprocessed_small_imgs[i]

                for box in big_img_boxes:
                    # 避免重复匹配
                    if [box[0], box[1]] in result_list:
                        continue

                    # 裁剪大图区域
                    cropped = self.img[
                        box[1] : box[1] + box[3], box[0] : box[0] + box[2]
                    ]
                    image_data_2 = self.preprocess_image(cropped)

                    if image_data_2 is None:
                        continue

                    # Siamese网络推理
                    inputs = {"input": image_data_1, "input.53": image_data_2}
                    output = self.Siamese.run(None, inputs)

                    # 计算相似度
                    output_sigmoid = 1 / (1 + np.exp(-output[0]))
                    res = output_sigmoid[0][0]

                    # 如果相似度超过阈值，认为匹配成功
                    if res >= 0.1:
                        result_list.append([box[0], box[1]])
                        break

            # 注意：已移除图片保存功能以消除IO开销
            # 所有处理都在内存中完成

            return result_list

        except Exception as e:
            print(f"Siamese匹配失败: {e}")
            return []


def test_model():
    """测试模型功能"""
    try:
        model = Model()
        print("模型初始化完成")

        # 这里可以添加更多测试代码
        return True
    except Exception as e:
        print(f"模型测试失败: {e}")
        return False


if __name__ == "__main__":
    test_model()
