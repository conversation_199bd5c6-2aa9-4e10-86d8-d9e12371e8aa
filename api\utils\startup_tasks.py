"""
应用启动任务模块
针对Vercel无服务器环境优化的启动任务
"""

import logging
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.db import init_db, cleanup_expired_records
    from api.utils.microsoft_email_cache import MicrosoftEmailCacheManager
except:
    from db import init_db, cleanup_expired_records
    from utils.microsoft_email_cache import MicrosoftEmailCacheManager


class StartupTaskManager:
    """启动任务管理器"""

    @staticmethod
    def run_startup_tasks():
        """
        运行所有启动任务
        适用于Vercel无服务器环境，快速执行必要的初始化任务
        """
        logger.info("开始执行启动任务...")

        try:
            # 1. 初始化数据库表
            logger.info("初始化数据库表...")
            init_db()
            logger.info("数据库表初始化完成")

            # 2. 清理过期记录（异步执行，不阻塞启动）
            try:
                logger.info("清理过期记录...")
                cleanup_expired_records()

                # 清理过期的登录缓存
                cleaned_count = MicrosoftEmailCacheManager.cleanup_expired_cache()
                if cleaned_count > 0:
                    logger.info(f"清理了 {cleaned_count} 条过期的登录缓存")

                logger.info("过期记录清理完成")
            except Exception as e:
                logger.warning(f"清理过期记录时出错（不影响启动）: {str(e)}")

            logger.info("所有启动任务执行完成")

        except Exception as e:
            logger.error(f"启动任务执行失败: {str(e)}")
            # 在Vercel环境中，即使启动任务失败也不应该阻止应用启动
            # 因为数据库可能已经初始化过了

    @staticmethod
    def run_periodic_cleanup():
        """
        运行定期清理任务
        可以在API调用时偶尔执行，保持数据库清洁
        """
        try:
            logger.info("执行定期清理任务...")

            # 清理过期记录
            cleanup_expired_records()

            # 清理过期的登录缓存
            cleaned_count = MicrosoftEmailCacheManager.cleanup_expired_cache()
            if cleaned_count > 0:
                logger.info(f"定期清理了 {cleaned_count} 条过期的登录缓存")

            logger.info("定期清理任务完成")

        except Exception as e:
            logger.warning(f"定期清理任务执行失败: {str(e)}")


# 全局变量，用于跟踪是否已执行过启动任务
_startup_tasks_executed = False


def ensure_startup_tasks_executed():
    """
    确保启动任务已执行
    在Vercel环境中，每个函数调用都可能是"冷启动"，需要确保必要的初始化已完成
    """
    global _startup_tasks_executed

    if not _startup_tasks_executed:
        try:
            StartupTaskManager.run_startup_tasks()
            _startup_tasks_executed = True
        except Exception as e:
            logger.error(f"启动任务执行失败: {str(e)}")
            # 即使失败也标记为已执行，避免重复尝试
            _startup_tasks_executed = True


def run_periodic_cleanup_if_needed():
    """
    根据需要运行定期清理任务
    在API调用时偶尔执行，避免频繁清理影响性能
    """
    import random

    # 10% 的概率执行清理任务
    if random.random() < 0.1:
        try:
            StartupTaskManager.run_periodic_cleanup()
        except Exception as e:
            logger.warning(f"定期清理任务失败: {str(e)}")
