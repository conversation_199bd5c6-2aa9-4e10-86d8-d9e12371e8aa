import { createVNode, render } from 'vue'
import Message from '../components/Message.vue'

const messageTypes = ['success', 'warning', 'info', 'error']
let messageContainer = null

const createContainer = () => {
  const container = document.createElement('div')
  container.className = 'message-container'
  document.body.appendChild(container)
  return container
}

const message = (options) => {
  if (!messageContainer) {
    messageContainer = createContainer()
  }

  const props = typeof options === 'string' ? { content: options } : options
  const div = document.createElement('div')
  messageContainer.appendChild(div)

  const vnode = createVNode(Message, {
    ...props,
    onDestroy: () => {
      messageContainer.removeChild(div)
      render(null, div)
    }
  })

  render(vnode, div)
}

// 添加便捷方法
messageTypes.forEach(type => {
  message[type] = (content, options = {}) => {
    return message({
      type,
      content,
      ...options
    })
  }
})

export default message 