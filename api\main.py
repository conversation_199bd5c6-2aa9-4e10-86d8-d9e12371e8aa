import os
import traceback
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import asyncio
from datetime import datetime, timedelta

try:
    # 导入各个模块
    from .db import init_db, engine
    from .models import metadata
    from .routes import (
        auth,
        email,
        ai,
        captcha,
        qrcode,
        subscription,
        card,
        admin,
        microsoft_email,
    )
    from .routes.email import get_html_preview_router
    from .utils.email import cleanup_expired_html_records
except:
    # 导入各个模块
    from models import metadata
    from routes import (
        auth,
        email,
        ai,
        captcha,
        qrcode,
        subscription,
        card,
        admin,
        microsoft_email,
    )
    from routes.email import get_html_preview_router
    from utils.email import cleanup_expired_html_records
    from db import init_db, engine

# 检查是否在Vercel环境中运行
IS_VERCEL = os.environ.get("VERCEL", "0") == "1"


# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动
    try:
        # 在应用启动时创建数据库表
        with engine.begin() as conn:
            # 创建所有表
            metadata.create_all(conn, checkfirst=True)
            print("所有表已创建或已存在")

        # 设置定期清理过期HTML记录的任务 (如果不是在Vercel环境中)
        if not IS_VERCEL:
            cleanup_task = asyncio.create_task(periodic_cleanup())

        yield
    except Exception as e:
        print(f"数据库迁移错误: {str(e)}")
        raise
    finally:
        # 资源清理
        print("应用关闭，清理资源...")
        if not IS_VERCEL and "cleanup_task" in locals():
            cleanup_task.cancel()


# 定期清理过期HTML记录
async def periodic_cleanup():
    """每小时清理一次过期的HTML记录"""
    try:
        while True:
            # 执行清理
            try:
                cleanup_expired_html_records()
                print(
                    f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 执行清理过期HTML记录"
                )
            except Exception as e:
                print(f"清理过期HTML记录时发生错误: {str(e)}")

            # 等待一小时
            await asyncio.sleep(3600)  # 1小时 = 3600秒
    except asyncio.CancelledError:
        # 任务被取消时正常退出
        print("清理任务已取消")


# 创建FastAPI应用
app = FastAPI(
    lifespan=lifespan,
    title="API Backend",
    description="KedayaAPI后端服务",
    version="1.0.0",
)


# 全局异常处理中间件
@app.middleware("http")
async def global_exception_middleware(request: Request, call_next):
    try:
        return await call_next(request)
    except Exception as exc:
        # 记录异常但不暴露详细信息给客户端
        error_msg = f"请求处理异常: {str(exc)}"
        if not IS_VERCEL:  # 在非Vercel环境中打印更多信息
            print(f"路径: {request.url.path}")
            print(f"详细错误: {traceback.format_exc()}")
        else:
            print(error_msg)  # Vercel环境下只打印简要信息

        # 返回JSON格式错误响应
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "服务器内部错误，请稍后重试"},
        )


# CORS 配置 - 对于生产环境，应该限制origins
origins = ["*"]  # 在生产环境中应替换为实际域名
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含各个路由模块
app.include_router(auth.router)
app.include_router(email.router)
app.include_router(ai.router)
app.include_router(captcha.router)
app.include_router(qrcode.router)
app.include_router(subscription.router)
app.include_router(card.router)
app.include_router(admin.router)
app.include_router(microsoft_email.router)
app.include_router(get_html_preview_router())


# 添加管理员端点用于初始化数据库
@app.get("/api/admin/init-db")
async def admin_init_db(admin_key: str = None):
    """管理员手动初始化数据库"""
    # 简单的管理员密钥验证
    if not admin_key or admin_key != os.getenv("ADMIN_KEY", "admin-secret-key"):
        return {"status": False, "message": "无效的管理员密钥"}

    try:
        # 手动初始化数据库
        init_db()
        return {"status": True, "message": "数据库初始化成功，所有表已创建"}
    except Exception as e:
        print(f"数据库初始化失败: {str(e)}")
        return {"status": False, "message": f"数据库初始化失败: {str(e)}"}


dist_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "dist")
print(f"静态文件目录: {dist_path}")
if os.path.exists(dist_path):
    app.mount("/", StaticFiles(directory=dist_path, html=True), name="static")
    print("✅ 静态文件服务已启用")
else:
    print(f"⚠️ 警告: 静态文件目录不存在: {dist_path}")

# 为开发环境添加直接运行支持
if __name__ == "__main__":
    import uvicorn

    # 获取端口 - 处理 Zeabur 环境变量问题
    port_env = os.getenv("PORT", "8000")
    try:
        # 如果端口是变量形式（如 ${WEB_PORT}），使用默认端口
        if port_env.startswith("${") and port_env.endswith("}"):
            PORT = 8000
        else:
            PORT = int(port_env)
    except (ValueError, AttributeError):
        PORT = 8000

    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=PORT,
        )
    except Exception as e:
        print(f"启动服务器失败: {str(e)}")
        traceback.print_exc()
