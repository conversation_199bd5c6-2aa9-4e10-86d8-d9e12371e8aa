<template>
    <div class="header">
        <h1 class="site-title">API 功能列表</h1>
        <div class="user-section">
            <ThemeToggle />
            <template v-if="!userInfo">
                <LoadingButton class="auth-btn" @click="openAuthModal('login')">登录</LoadingButton>
                <LoadingButton class="auth-btn" @click="openAuthModal('register')">注册</LoadingButton>
            </template>
            <template v-else>
                <div class="user-info" @click="showUserModal">
                    <User style="height: 1.5em;" :color="isDarkTheme ? '#818cf8' : '#4f46e5'" />
                    <span class="balance">余额: {{ userInfo.balance }}</span>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import LoadingButton from './LoadingButton.vue'
import ThemeToggle from './ThemeToggle.vue'
import User from './icons/User.vue'

const props = defineProps({
    userInfo: {
        type: Object,
        default: null
    }
})

const emit = defineEmits(['openAuthModal', 'showUserModal'])

const store = useStore()

// 获取当前主题
const isDarkTheme = computed(() => {
    return store.getters.currentTheme === 'dark'
})

const openAuthModal = (type) => {
    emit('openAuthModal', type)
}

const showUserModal = () => {
    emit('showUserModal')
}
</script> 