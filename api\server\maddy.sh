#!/bin/bash

# 定义容器名称和邮件域名
CONTAINER_NAME="1Panel-maddy-mail-VBhJ"
MAIL_DOMAIN="kedaya.xyz"  # 邮件的域名可以在这里修改
MAX_THREADS=5  # 最大并行线程数

# 显示操作菜单
show_menu() {
    echo "请选择操作:"
    echo "1. 查看邮件列表"
    echo "2. 添加邮件账户"
    echo "3. 移除邮件账户"
    echo "4. 更改密码"
    echo "5. 删除所有账户"
    echo "6. 退出"
}

# 查看邮件列表并合并输出
check_mail_list() {
    # 获取并去重两个邮件列表
    echo "执行命令: maddy creds list"
    creds_list=$(docker exec -i $CONTAINER_NAME /bin/sh -c "maddy creds list" | sort | uniq)

    echo "执行命令: maddy imap-acct list"
    imap_list=$(docker exec -i $CONTAINER_NAME /bin/sh -c "maddy imap-acct list" | sort | uniq)

    # 合并并去重两个列表
    merged_list=$(echo -e "$creds_list\n$imap_list" | sort | uniq)

    # 输出合并后的列表
    echo "合并后的邮件账户列表:"
    echo "$merged_list"

    # 计算账户总数
    total_accounts=$(echo "$merged_list" | grep -v '^$' | wc -l)
    echo "总共有 $total_accounts 个邮件账户"

    # 比较并找出缺失的账户
    missing_in_creds=$(comm -23 <(echo "$imap_list" | sort) <(echo "$creds_list" | sort))
    missing_in_imap=$(comm -13 <(echo "$imap_list" | sort) <(echo "$creds_list" | sort))

    # 输出缺失的账户
    if [ -n "$missing_in_creds" ]; then
        echo "以下账户在 creds list 中缺失:"
        echo "$missing_in_creds"
    fi
    if [ -n "$missing_in_imap" ]; then
        echo "以下账户在 imap-acct list 中缺失:"
        echo "$missing_in_imap"
    fi
}

# 添加邮件账户
add_mail_account() {
    # 提示并输入账户名
    read -p "请输入邮件账户（例如 test）： " email

    echo "正在添加邮件账户..."
    # 进入容器并手动输入密码
    docker exec -it $CONTAINER_NAME /bin/sh -c "maddy creds create $email@$MAIL_DOMAIN"
    docker exec -it $CONTAINER_NAME /bin/sh -c "maddy imap-acct create $email@$MAIL_DOMAIN"

    # 完成账户添加后查看邮件列表
    echo "账户添加完成，查看邮件列表..."
    check_mail_list
}

# 移除邮件账户
remove_mail_account() {
    # 提示并输入账户名
    read -p "请输入要移除的邮件账户（例如 test）： " email

    # 提示确认移除
    read -p "你确定要移除账户 $email@$MAIL_DOMAIN 吗？(y/n): " confirm
    if [[ "$confirm" == "y" || "$confirm" == "Y" ]]; then
        echo "正在移除邮件账户..."
        # 执行移除操作
        docker exec -it $CONTAINER_NAME /bin/sh -c "echo y | maddy creds remove $email@$MAIL_DOMAIN"
        docker exec -it $CONTAINER_NAME /bin/sh -c "echo y | maddy imap-acct remove $email@$MAIL_DOMAIN"

        # 完成账户移除后查看邮件列表
        echo "账户移除完成，查看邮件列表..."
        check_mail_list
    else
        echo "账户移除操作已取消。"
    fi
}

# 更改邮件账户密码
change_password() {
    # 提示并输入账户名
    read -p "请输入要更改密码的邮件账户（例如 test）： " email

    echo "正在更改密码..."
    # 执行更改密码命令，不再提示输入密码
    docker exec -it $CONTAINER_NAME /bin/sh -c "maddy creds password $email@$MAIL_DOMAIN"

    # 完成密码更改后查看邮件列表
    echo "密码更改完成!"
    echo "查看邮件列表..."
    check_mail_list
}

# 显示进度条
show_progress() {
    local completed=$1
    local total=$2
    local percent=$((completed * 100 / total))
    local progress=$((completed * 50 / total))
    
    # 进度条字符
    local bar="["
    for ((i=0; i<progress; i++)); do
        bar+="#"
    done
    for ((i=progress; i<50; i++)); do
        bar+="."
    done
    bar+="]"
    
    # 输出进度信息
    echo -ne "\r已处理: $completed/$total $bar $percent%"
    if [ $completed -eq $total ]; then
        echo -e "\n删除操作完成!"
    fi
}

# 删除所有账户
remove_all_accounts() {
    # 获取所有账户列表
    echo "获取所有邮件账户列表..."
    creds_list=$(docker exec -i $CONTAINER_NAME /bin/sh -c "maddy creds list" | sort | uniq)
    imap_list=$(docker exec -i $CONTAINER_NAME /bin/sh -c "maddy imap-acct list" | sort | uniq)
    
    # 合并并去重两个列表
    all_accounts=$(echo -e "$creds_list\n$imap_list" | sort | uniq | grep -v '^$')
    
    # 检查是否有账户可删除
    total_accounts=$(echo "$all_accounts" | wc -l)
    if [ $total_accounts -eq 0 ]; then
        echo "没有找到任何邮件账户。"
        return
    fi
    
    # 显示所有将被删除的账户
    echo "以下账户将被删除:"
    echo "$all_accounts"
    echo "总共有 $total_accounts 个邮件账户"
    
    # 提示确认删除所有账户
    read -p "警告: 你确定要删除所有账户吗？此操作不可撤销! (yes/no): " confirm
    if [[ "$confirm" != "yes" ]]; then
        echo "删除所有账户操作已取消。"
        return
    fi
    
    # 二次确认
    read -p "请再次确认，输入'DELETE ALL'(全大写)继续: " confirm2
    if [[ "$confirm2" != "DELETE ALL" ]]; then
        echo "删除所有账户操作已取消。"
        return
    fi
    
    echo "开始删除所有账户，总计: $total_accounts 个..."
    
    # 创建临时脚本文件
    creds_script=$(mktemp)
    imap_script=$(mktemp)
    
    # 为每种类型准备批处理脚本
    echo "#!/bin/sh" > $creds_script
    echo "#!/bin/sh" > $imap_script
    
    # 添加删除命令到批处理脚本
    while read -r account; do
        echo "echo y | maddy creds remove $account" >> $creds_script
        echo "echo y | maddy imap-acct remove $account" >> $imap_script
    done <<< "$all_accounts"
    
    # 设置执行权限
    chmod +x $creds_script $imap_script
    
    # 并行执行creds和imap删除操作
    echo "开始并行删除账户..."
    docker cp $creds_script $CONTAINER_NAME:/tmp/creds_remove.sh
    docker cp $imap_script $CONTAINER_NAME:/tmp/imap_remove.sh
    
    # 创建后台任务执行批量删除
    docker exec -i $CONTAINER_NAME /bin/sh -c "/tmp/creds_remove.sh" &
    creds_pid=$!
    docker exec -i $CONTAINER_NAME /bin/sh -c "/tmp/imap_remove.sh" &
    imap_pid=$!
    
    # 显示处理进度
    completed=0
    while kill -0 $creds_pid 2>/dev/null || kill -0 $imap_pid 2>/dev/null; do
        # 每秒更新进度，进度值为估算值
        completed=$((completed + 1))
        if [ $completed -gt $total_accounts ]; then
            completed=$total_accounts
        fi
        show_progress $completed $((total_accounts * 2))
        sleep 0.5
    done
    
    # 确保显示100%完成
    show_progress $((total_accounts * 2)) $((total_accounts * 2))
    
    # 清理临时文件
    docker exec -i $CONTAINER_NAME /bin/sh -c "rm -f /tmp/creds_remove.sh /tmp/imap_remove.sh"
    rm -f $creds_script $imap_script
    
    echo "所有账户删除操作完成!"
    # 检查是否还有账户存在
    check_mail_list
}

# 主程序：展示菜单并执行选定操作
while true; do
    show_menu
    # 输入选项并处理
    read -p "请输入选项: " option
    case $option in
        1) check_mail_list ;;  # 查看合并后的邮件列表
        2) add_mail_account ;;  # 添加邮件账户
        3) remove_mail_account ;;  # 移除邮件账户
        4) change_password ;;  # 更改密码
        5) remove_all_accounts ;;  # 删除所有账户
        6) echo "退出程序."; break ;;  # 退出脚本
        *) echo "无效的选项，请重新选择。" ;;  # 无效输入
    esac
done