{"version": 2, "builds": [{"src": "api/main.py", "use": "@vercel/python"}, {"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist", "buildCommand": "npm run build"}}], "routes": [{"src": "/api/(.*)", "dest": "api/main.py"}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"PYTHONPATH": "."}, "crons": [{"path": "/api/admin/generate-subscriptions?admin_key=admin-secret-key&count=5&threads=5", "schedule": "0 0 * * *"}]}