import sqlalchemy
from fastapi import APIRouter, HTTPException, Request, Depends
import requests
import asyncio

try:
    from api.db import engine, with_db_retry, rate_limit
    from api.models import users, QRCodeRequest, QRCodeDecodeRequest
    from api.auth import get_current_user
    from api.utils.common import get_client_ip
    from api.utils.billing import record_consumption
except:
    from db import engine, with_db_retry, rate_limit
    from models import users, QRCodeRequest, QRCodeDecodeRequest
    from auth import get_current_user
    from utils.common import get_client_ip
    from utils.billing import record_consumption

router = APIRouter(prefix="/api/v1/scraping", tags=["scraping"])


def qrcode_generate(**params):
    """调用二维码生成API"""
    qali_api_url = "https://qali.cn/api/v1/scraping/qrcode"

    response = requests.post(qali_api_url, json=params)

    if response.status_code != 200:
        error_msg = f"调用二维码API失败: HTTP {response.status_code}"
        try:
            error_detail = response.json()
            error_msg += f" - {error_detail.get('message', '')}"
        except:
            pass
        raise HTTPException(status_code=500, detail=error_msg)

    return response.text


def qrcode_decode(**params):
    """调用二维码解析API"""
    qali_api_url = "https://qali.cn/api/v1/scraping/qrcode-decode"

    response = requests.post(qali_api_url, json=params)

    if response.status_code != 200:
        error_msg = f"调用二维码解析API失败: HTTP {response.status_code}"
        try:
            error_detail = response.json()
            error_msg += f" - {error_detail.get('message', '')}"
        except:
            pass
        raise HTTPException(status_code=500, detail=error_msg)

    try:
        response_data = response.json()
        if "text" not in response_data:
            raise HTTPException(status_code=400, detail="API返回数据格式错误")
        return response_data["text"]
    except ValueError:
        raise HTTPException(status_code=500, detail="API返回数据不是有效的JSON")


@router.post("/scraping/qrcode", include_in_schema=True)
async def qrcode_generate_endpoint(
    request: QRCodeRequest, current_user: dict = Depends(get_current_user)
):
    """生成二维码"""
    try:
        # 收费：1点
        fee = 1.0

        # 检查余额
        if current_user["balance"] < fee:
            raise HTTPException(status_code=400, detail=f"余额不足，需要{fee}点余额")

        with engine.begin() as conn:
            # 扣除余额
            new_balance = current_user["balance"] - fee
            conn.execute(
                users.update()
                .where(users.c.username == current_user["username"])
                .values(balance=new_balance)
            )

            # 记录消费
            try:
                # 使用异步函数但同步调用，避免阻塞主流程
                asyncio.create_task(
                    record_consumption(
                        username=current_user["username"],
                        details=f"生成二维码",
                        amount=fee,
                    )
                )
            except Exception as e:
                print(f"记录消费失败: {str(e)}")
                # 继续执行，不因记录失败而中断流程

        # 调用QR码生成
        resp = qrcode_generate(**request.dict())

        return {
            "code": 200,
            "status": True,
            "data": resp,
            "balance": new_balance,
            "message": "二维码生成成功",
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"二维码生成失败: {str(e)}")


@router.post("/scraping/qrcode/decode", include_in_schema=True)
async def qrcode_decode_endpoint(
    request: QRCodeDecodeRequest, current_user: dict = Depends(get_current_user)
):
    """解析二维码"""
    try:
        # 收费：100点
        fee = 100.0

        # 检查余额
        if current_user["balance"] < fee:
            raise HTTPException(status_code=400, detail=f"余额不足，需要{fee}点余额")

        with engine.begin() as conn:
            # 扣除余额
            new_balance = current_user["balance"] - fee
            conn.execute(
                users.update()
                .where(users.c.username == current_user["username"])
                .values(balance=new_balance)
            )

            # 记录消费
            try:
                # 使用异步函数但同步调用，避免阻塞主流程
                asyncio.create_task(
                    record_consumption(
                        username=current_user["username"],
                        details=f"解析二维码",
                        amount=fee,
                    )
                )
            except Exception as e:
                print(f"记录消费失败: {str(e)}")
                # 继续执行，不因记录失败而中断流程

        # 解析二维码
        resp = qrcode_decode(**request.dict())

        return {
            "code": 200,
            "status": True,
            "data": resp,
            "balance": new_balance,
            "message": "二维码解析成功",
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"二维码解析失败: {str(e)}")
