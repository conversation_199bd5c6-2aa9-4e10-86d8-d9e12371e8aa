import random
import string
import time
import functools
import threading
import asyncio
from datetime import datetime, timezone, timedelta
import sqlalchemy
from sqlalchemy.pool import QueuePool
from curl_cffi import requests as curl_requests
from functools import lru_cache
import os
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

# 修复导入路径，添加灵活的导入方式
try:
    from api.projects import __third_step
    from api.projects.geetest3_fullpage import geetest3_fullpage
    from api.projects.geetest3_slide import geetest3_slide
    from api.projects.geetest3_click import geetest3_click
    from api.models import subscriptions
    from api.utils.email import extract_verification_code, get_emails
    from api.routes.email import get_emails_core
    from api.db import engine
except ImportError:
    try:
        from models import subscriptions
        from utils.email import extract_verification_code, get_emails
        from routes.email import get_emails_core
        from db import engine
        from projects import __third_step
        from projects.geetest3_fullpage import geetest3_fullpage
        from projects.geetest3_slide import geetest3_slide
        from projects.geetest3_click import geetest3_click
    except ImportError:
        # 尝试其他可能的导入路径，用于部署环境
        import sys
        import os

        # 添加当前目录到路径
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        try:
            from models import subscriptions
            from utils.email import extract_verification_code, get_emails
            from routes.email import get_emails_core
            from db import engine
        except ImportError as e:
            logger.error(f"模块导入：无法导入必要模块: {e}")
            raise

# 检查是否在Vercel环境中运行
IS_VERCEL = os.environ.get("VERCEL", "0") == "1"


# 将缓存装饰器移动到类外部，作为独立函数
def cache_result(ttl=300):
    """缓存装饰器，对查询结果进行缓存，ttl为缓存有效期（秒）"""

    def decorator(func):
        cache = {}
        cache_lock = threading.Lock()

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{str(args)}:{str(kwargs)}"

            # 检查缓存是否存在且未过期
            with cache_lock:
                if cache_key in cache:
                    result, timestamp = cache[cache_key]
                    if time.time() - timestamp < ttl:
                        logger.debug(f"缓存使用：使用缓存结果: {func.__name__}")
                        return result

            # 执行原始函数
            result = func(*args, **kwargs)

            # 存入缓存
            with cache_lock:
                cache[cache_key] = (result, time.time())

            return result

        # 添加清理缓存的方法
        def clear_cache():
            with cache_lock:
                cache.clear()

        wrapper.clear_cache = clear_cache
        return wrapper

    return decorator


def captcha3(gt, challenge, proxy=None):
    try:
        status, result = __third_step(gt, challenge, proxy)
        logger.info(f"验证码第三步状态: {status}, 结果: {result}")

        if status == "sense":
            if result:
                return result
            else:
                result = geetest3_fullpage(gt, challenge, proxy)
                if result and isinstance(result, dict) and result.get("validate"):
                    return result.get("validate")
                logger.warning(f"全页验证码处理失败: {result}")
                return "验证码识别出错"
        elif status == "error":
            logger.error(f"获取验证码类型错误: {result}")
            return "验证码识别出错"
        elif status == "success" or result == "slide":
            result = geetest3_slide(gt, challenge, proxy)
            logger.info(f"滑动验证码结果: {result}")

            if result and isinstance(result, dict) and result.get("success") == 1:
                return result.get("validate")
            else:
                logger.warning(f"滑动验证码识别失败: {result}")
                return "验证码识别出错"
        elif result == "click":
            result = geetest3_click(gt, challenge, proxy)
            logger.info(f"点击验证码结果: {result}")

            if (
                result
                and isinstance(result, dict)
                and result.get("status") == "success"
            ):
                data = result.get("data", {})
                if isinstance(data, dict) and data.get("validate"):
                    return data.get("validate")

            logger.warning(f"点击验证码识别失败: {result}")
            return "验证码识别出错"
        else:
            logger.error(f"未知验证码类型: status={status}, result={result}")
            return "验证码类型错误"
    except Exception as e:
        logger.error(f"验证码处理异常: {str(e)}")
        import traceback

        traceback.print_exc()
        return "验证码识别出错"


class CSKedayaAPI:
    # 创建数据库连接池
    db_engine = None
    # HTTP会话复用
    http_session = None
    # 线程锁，用于确保初始化的线程安全
    _init_lock = threading.RLock()
    # 缓存集合，用于统一清理
    _cache_functions = []

    # 支持的浏览器指纹列表
    BROWSER_FINGERPRINTS = [
        # Chrome系列
        "chrome99",
        "chrome100",
        "chrome101",
        "chrome104",
        "chrome107",
        "chrome110",
        "chrome116",
        "chrome119",
        "chrome120",
        "chrome123",
        "chrome124",
        "chrome131",
        "chrome133a",
        "chrome136",
        # Chrome Android
        "chrome99_android",
        "chrome131_android",
        # Safari系列
        "safari15_3",
        "safari15_5",
        "safari17_0",
        "safari18_0",
        "safari18_4",
        # Safari iOS
        "safari17_2_ios",
        "safari18_0_ios",
        "safari18_4_ios",
        # Firefox系列
        "firefox133",
        "firefox135",
        # Edge系列
        "edge99",
        "edge101",
        # Tor浏览器
        "tor145",
    ]

    @classmethod
    def initialize(cls, db_pool_size=None):
        """初始化类级别资源，如HTTP会话和数据库连接池"""
        with cls._init_lock:
            # 创建curl_cffi会话对象
            if cls.http_session is None:
                cls.http_session = curl_requests.Session(
                    timeout=60,
                    impersonate="chrome136",  # 默认使用最新的Chrome
                    headers={
                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                        "Accept-Encoding": "deflate, br",
                        "Accept": "*/*",
                    },
                )

            # 配置数据库连接池
            if cls.db_engine is None:
                try:
                    cls.db_engine = sqlalchemy.create_engine(
                        engine.url,
                        poolclass=QueuePool,
                        pool_size=300,
                        max_overflow=10,
                        pool_timeout=30,
                        pool_recycle=1800,
                        pool_pre_ping=True,
                    )
                except Exception as e:
                    logger.error(f"数据库：创建数据库连接池失败: {e}")
                    cls.db_engine = engine

            logger.info("API初始化：CSKedayaAPI 类资源初始化完成")

    def __init__(
        self,
        token=None,
        domain=None,
        debug=False,
        impersonate="chrome136",
        proxy=None,
        cookies=None,
        refresh_domain=False,  # 新增参数，用于控制是否刷新域名配置
    ):
        """初始化API客户端

        Args:
            token: API令牌（现在主要用于其他功能，邮件获取不再需要）
            domain: 域名
            debug: 是否开启调试模式
            impersonate: 要模拟的浏览器指纹，默认为最新Chrome
            proxy: 代理设置，如 "*********************:port" 或 "socks5://host:port"
            cookies: 初始化cookies
            refresh_domain: 是否强制刷新域名配置（忽略缓存）
        """
        # 确保类级别资源已初始化
        if CSKedayaAPI.http_session is None:
            CSKedayaAPI.initialize()

        self.subscribe = "e.bbydy.org"
        self.domain = "api4.345112.xyz"
        self.base_url = f"https://{self.domain}"
        self.token = token
        self.impersonate = impersonate
        self.proxy = proxy
        self.cookies = cookies or {}

        self.headers = {
            "accept": "*/*",
            "content-type": "application/json",
            "host": self.domain,
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        }

        # 创建会话
        self.session = curl_requests.Session(
            timeout=60,
            impersonate=self.impersonate,
            headers=self.headers,
            cookies=self.cookies,
        )

        # 设置代理
        if self.proxy:
            self.session.proxies = {"all": self.proxy}
            logger.info(f"代理设置：使用代理: {self.proxy}")

        # 加载订阅地址列表
        self.subscriptions = []
        self.load_subscriptions()

    def send_verification_code(self, email, ip=""):
        """发送验证码到指定邮箱"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.session.get(
                    f"{self.base_url}/api/v1/guest/geetest/register"
                )
                if response.status_code != 200:
                    logger.error(f"获取geetest注册信息失败: {response.text}")
                    return {
                        "status": "fail",
                        "message": f"HTTP错误: {response.status_code}",
                    }

                geetest_data = response.json()
                gt = geetest_data["gt"]
                challenge = geetest_data["challenge"]

                captcha_result = captcha3(gt, challenge)

                if not captcha_result or captcha_result in [
                    "验证码识别出错",
                    "验证码类型错误",
                ]:
                    logger.error(f"验证码识别最终失败: {captcha_result}")
                    if attempt < max_retries - 1:
                        logger.info(f"整体重试 {attempt + 1}/{max_retries}")
                        time.sleep(5)
                        continue
                    else:
                        return {
                            "status": "fail",
                            "message": "验证码识别失败，请稍后重试",
                        }

                url = f"{self.base_url}/api/v1/passport/comm/sendEmailVerify"
                # JSON数据
                data = {
                    "email": email,
                    "recaptcha_data[geetest_challenge]": challenge,
                    "recaptcha_data[geetest_validate]": captcha_result,
                    "recaptcha_data[geetest_seccode]": captcha_result + "|jordan",
                    "geetest_challenge": challenge,
                    "geetest_validate": captcha_result,
                    "geetest_seccode": captcha_result + "|jordan",
                    "isforget": "0",
                }

                # 使用curl_cffi会话发送请求
                response = self.session.post(
                    url,
                    json=data,
                    headers={
                        "host": self.domain,
                        "content-type": "application/json",
                    },
                )

                if response.status_code != 200:
                    logger.error(
                        f"发送验证码API返回非200状态码: {response.status_code}"
                    )
                    logger.error(f"响应内容: {response.text}")
                    if attempt < max_retries - 1:
                        logger.info(f"HTTP错误，重试 {attempt + 1}/{max_retries}")
                        time.sleep(3)
                        continue
                    else:
                        return {
                            "status": "fail",
                            "message": f"HTTP错误: {response.status_code}",
                        }

                result = response.json()
                logger.info(f"发送验证码API响应: {result}")

                # 检查响应是否包含成功标志
                if isinstance(result, dict) and result.get("data") is True:
                    logger.info("验证码发送成功")
                    return result
                else:
                    logger.error(f"验证码发送失败: {result}")
                    # 如果是验证码错误，重试整个流程
                    if isinstance(result, dict) and "验证码" in str(
                        result.get("message", "")
                    ):
                        if attempt < max_retries - 1:
                            logger.info(
                                f"验证码错误，重试整个流程 {attempt + 1}/{max_retries}"
                            )
                            time.sleep(5)
                            continue
                    return result

            except Exception as e:
                error_msg = str(e)
                logger.error(f"发送验证码失败: {error_msg}")
                if attempt < max_retries - 1:
                    logger.info(f"异常重试 {attempt + 1}/{max_retries}")
                    time.sleep(3)
                    continue
                else:
                    import traceback

                    traceback.print_exc()
                    return {"status": "fail", "message": error_msg}

        return {"status": "fail", "message": "多次重试后仍然失败"}

    def register(self, email, email_code, password, ip=""):
        """使用邮箱、验证码和密码进行注册"""
        url = f"{self.base_url}/api/v1/passport/auth/register"
        data = {
            "email": email,
            "password": password,
            "invite_code": "",
            "email_code": email_code,
        }

        try:
            # 使用curl_cffi会话发送请求
            response = self.session.post(
                url,
                data=data,
                headers={
                    "host": self.domain,
                    "content-type": "application/x-www-form-urlencoded",
                    "content-language": "zh-CN",
                    "sec-ch-ua-platform": "Windows",
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                    "sec-ch-ua": '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                    "sec-ch-ua-mobile": "?0",
                    "accept": "*/*",
                    "origin": "https://bby010.com",
                    "sec-fetch-site": "cross-site",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-storage-access": "active",
                    "referer": "https://bby010.com/",
                    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                    "dnt": "1",
                    "sec-gpc": "1",
                    "priority": "u=1, i",
                },
            )

            if response.status_code != 200:
                logger.error(f"注册API返回非200状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return {
                    "status": "fail",
                    "message": f"HTTP错误: {response.status_code}",
                }

            result = response.json()
            logger.info(f"注册API响应: {result}")

            # 检查响应是否包含token（新的返回格式）
            if isinstance(result, dict) and result.get("data", {}).get("token"):
                logger.info("注册成功")
                token = result.get("data", {}).get("token")
                # 为了保持兼容性，将响应格式化为之前的格式
                formatted_result = {
                    "status": "success",
                    "data": {
                        "token": token,
                        "is_admin": result.get("data", {}).get("is_admin"),
                        "auth_data": result.get("data", {}).get("auth_data"),
                    },
                }
                return formatted_result
            else:
                logger.error(f"注册失败: {result}")
                return {"status": "fail", "message": "注册失败", "data": result}
        except Exception as e:
            error_msg = str(e)
            logger.error(f"注册失败: {error_msg}")
            import traceback

            traceback.print_exc()
            return {"status": "fail", "message": error_msg}

    @staticmethod
    def random_email():
        """生成格式为q177665609+随机数字@gmail.com的随机邮箱"""
        random_number = "".join(random.choices(string.digits, k=7))
        return f"q177665609+{random_number}@gmail.com"

    async def fetch_email(
        self, email_prefix="kedaya", num=1, passwd="", request_host=None
    ):
        """获取邮件内容，使用新的核心邮件获取函数

        Args:
            email_prefix: 邮箱前缀，如 "kedaya" 对应 <EMAIL>
            num: 获取邮件数量
            passwd: 邮箱密码，如果为空则使用默认密码（已废弃，核心函数不需要密码）
            request_host: 请求的host信息，用于生成HTML预览链接

        Returns:
            dict: 包含邮件列表的字典，格式为 {"status": True, "data": {"result": [邮件列表]}}
            或者错误信息
        """
        try:
            # 构建完整的邮箱地址
            if "@" not in email_prefix:
                email_address = f"{email_prefix}@kedaya.xyz"
            else:
                email_address = email_prefix

            # 使用新的核心邮件获取函数
            result = await get_emails_core(
                email=email_address,
                num=num,
                username="api_client",  # 标识来源
                request_host=request_host,
            )

            # 检查返回结果并转换为原有格式
            if result["status"] and result["data"]:
                return {"status": True, "data": {"result": result["data"]}}
            else:
                # 返回错误信息
                logger.warning(f"获取邮件失败: {result['message']}")
                return {
                    "status": False,
                    "message": result["message"],
                    "data": {"result": []},
                }

        except Exception as e:
            logger.error(f"获取邮件异常: {str(e)}")
            import traceback

            traceback.print_exc()
            return {
                "status": False,
                "message": f"获取邮件失败: {str(e)}",
                "data": {"result": []},
            }

    # 使用外部缓存装饰器，并跟踪缓存函数
    @lru_cache(maxsize=1)
    def _get_subscription_query(self):
        """返回查询订阅的基本SQL查询对象，使用LRU缓存避免重复构建"""
        return (
            subscriptions.select()
            .where(
                sqlalchemy.or_(
                    subscriptions.c.username == None,
                    subscriptions.c.username == "system",
                )
            )
            .order_by(subscriptions.c.created_at)
        )

    def load_subscriptions(self):
        """从数据库加载订阅地址列表"""
        self.subscriptions = []
        try:
            # 使用连接池获取连接
            with CSKedayaAPI.db_engine.connect() as conn:
                # 获取所有未分配给用户的订阅地址（username为空或为'system'）
                result = conn.execute(self._get_subscription_query())

                # 批量处理结果
                batch = []
                for row in result:
                    row_mapping = row._mapping
                    created_at = row_mapping[subscriptions.c.created_at]
                    # 确保created_at有时区信息
                    if created_at and created_at.tzinfo is None:
                        # 如果数据库返回的时间没有时区信息，假定为UTC+8
                        utc_plus_8 = timezone(timedelta(hours=8))
                        created_at = created_at.replace(tzinfo=utc_plus_8)

                    batch.append(
                        {
                            "email": row_mapping[subscriptions.c.email],
                            "password": row_mapping[subscriptions.c.password],
                            "token": row_mapping[subscriptions.c.token],
                            "url": f"https://{self.subscribe}/api/v1/client/subscribe?token={row_mapping[subscriptions.c.token]}",
                            "created_at": created_at.isoformat()
                            if created_at
                            else datetime.now(timezone(timedelta(hours=8))).isoformat(),
                        }
                    )

                # 一次性添加所有结果，减少列表操作开销
                self.subscriptions.extend(batch)
                logger.info(f"从数据库加载了 {len(self.subscriptions)} 个订阅地址")
        except Exception as e:
            logger.error(f"从数据库加载订阅地址失败: {str(e)}")
            import traceback

            traceback.print_exc()
            self.subscriptions = []

    def save_subscriptions(self):
        """将订阅地址列表保存到数据库"""
        if not self.subscriptions:
            logger.info("没有订阅需要保存")
            return

        try:
            # 使用连接池获取连接
            with CSKedayaAPI.db_engine.connect() as conn:
                with conn.begin():
                    # 只删除超过4小时的system用户订阅
                    utc8_tz = timezone(timedelta(hours=8))
                    four_hours_ago = datetime.now(utc8_tz) - timedelta(hours=4)

                    delete_result = conn.execute(
                        subscriptions.delete().where(
                            sqlalchemy.and_(
                                subscriptions.c.username == "system",
                                subscriptions.c.created_at < four_hours_ago,
                            )
                        )
                    )
                    logger.info(
                        f"已删除 {delete_result.rowcount} 个超过4小时的旧订阅（创建时间早于 {four_hours_ago}）"
                    )

                    # 准备批量插入数据
                    batch_data = []
                    utc_plus_8 = timezone(timedelta(hours=8))

                    for sub in self.subscriptions:
                        # 处理created_at字段，确保有时区信息
                        if isinstance(sub["created_at"], str):
                            try:
                                created_at = datetime.fromisoformat(sub["created_at"])
                                # 确保有时区信息
                                if created_at.tzinfo is None:
                                    created_at = created_at.replace(tzinfo=utc_plus_8)
                            except (ValueError, TypeError):
                                # 如果解析失败，使用当前时间
                                created_at = datetime.now(utc_plus_8)
                        else:
                            # 如果已经是datetime对象
                            created_at = sub["created_at"]
                            # 确保有时区信息
                            if created_at.tzinfo is None:
                                created_at = created_at.replace(tzinfo=utc_plus_8)

                        batch_data.append(
                            {
                                "email": sub["email"],
                                "password": sub["password"],
                                "token": sub["token"],
                                "created_at": created_at,
                                "username": "system",  # 使用'system'作为系统管理的订阅标识
                            }
                        )

                    # 批量执行插入，提高效率
                    if batch_data:
                        conn.execute(subscriptions.insert().values(batch_data))

                    logger.info(
                        f"已将 {len(self.subscriptions)} 个订阅地址保存到数据库"
                    )
        except Exception as e:
            logger.error(f"保存订阅地址到数据库失败: {str(e)}")
            import traceback

            traceback.print_exc()

    def clean_expired_subscriptions(self):
        """清理过期的订阅地址（超过24小时）

        使用带时区的datetime对象处理过期时间比较
        - 使用UTC+8时区的当前时间减去24小时作为过期时间
        - PostgreSQL会正确处理带时区的datetime比较
        """

        # 使用UTC+8时区的当前时间作为基准
        utc_plus_8 = timezone(timedelta(hours=8))
        now_utc8 = datetime.now(utc_plus_8)
        # 计算过期时间（24小时前）
        expire_date_utc8 = now_utc8 - timedelta(hours=24)

        try:
            # 使用连接池获取连接
            with CSKedayaAPI.db_engine.connect() as conn:
                with conn.begin():
                    # 删除过期的订阅
                    # 使用带时区的datetime对象比较
                    result = conn.execute(
                        subscriptions.delete().where(
                            sqlalchemy.and_(
                                sqlalchemy.or_(
                                    subscriptions.c.username == None,
                                    subscriptions.c.username == "system",
                                ),
                                subscriptions.c.created_at < expire_date_utc8,
                            )
                        )
                    )

                    logger.info(
                        f"已清理过期订阅（创建时间早于 {expire_date_utc8}），影响行数: {result.rowcount}"
                    )

            # 重新加载订阅列表
            self.load_subscriptions()
            logger.info("已完成过期订阅清理")
        except Exception as e:
            logger.error(f"清理过期订阅失败: {str(e)}")
            import traceback

            traceback.print_exc()

    def add_subscription(self, email, password, token):
        """添加一个新的订阅地址"""

        # 创建新的订阅记录，使用UTC+8时区
        utc_plus_8 = timezone(timedelta(hours=8))
        created_at_with_tz = datetime.now(utc_plus_8)
        logger.info(
            f"添加订阅：{email}, 创建时间(UTC+8): {created_at_with_tz.isoformat()}"
        )
        new_subscription = {
            "email": email,
            "password": password,
            "token": token,
            "url": f"https://{self.subscribe}/api/v1/client/subscribe?token={token}",
            "created_at": created_at_with_tz.isoformat(),
        }

        # 添加到列表
        self.subscriptions.append(new_subscription)

        # 保存到数据库
        try:
            # 使用连接池获取连接
            with CSKedayaAPI.db_engine.connect() as conn:
                with conn.begin():
                    # 使用带时区的datetime对象，确保时区信息正确保存
                    conn.execute(
                        subscriptions.insert().values(
                            email=email,
                            password=password,
                            token=token,
                            created_at=created_at_with_tz,
                            username="system",  # 使用'system'作为标识，表示这是系统管理的公共订阅
                        )
                    )
            logger.info(
                f"已添加新订阅到数据库: {email}, 创建时间(UTC+8): {created_at_with_tz}"
            )
        except Exception as e:
            logger.error(f"添加订阅到数据库失败: {str(e)}")
            import traceback

            traceback.print_exc()

        return f"https://{self.subscribe}/api/v1/client/subscribe?token={token}"

    def get_oldest_subscription(self):
        """获取最旧的订阅地址并从列表中删除"""

        if not self.subscriptions:
            return None

        # 清理过期地址
        self.clean_expired_subscriptions()

        if not self.subscriptions:
            return None

        try:
            # 使用连接池获取连接
            with CSKedayaAPI.db_engine.connect() as conn:
                with conn.begin():
                    # 获取最旧的订阅
                    result = conn.execute(self._get_subscription_query().limit(1))
                    oldest = result.fetchone()

                    if oldest:
                        # 从数据库中删除
                        oldest_id = oldest._mapping[subscriptions.c.id]
                        conn.execute(
                            subscriptions.delete().where(
                                subscriptions.c.id == oldest_id
                            )
                        )

                        # 构建返回结果 - 使用映射访问
                        token = oldest._mapping[subscriptions.c.token]
                        subscription = {
                            "email": oldest._mapping[subscriptions.c.email],
                            "password": oldest._mapping[subscriptions.c.password],
                            "token": token,
                            "url": f"https://{self.subscribe}/api/v1/client/subscribe?token={token}",
                            "created_at": oldest._mapping[
                                subscriptions.c.created_at
                            ].isoformat()
                            if oldest._mapping[subscriptions.c.created_at]
                            else datetime.now().isoformat(),
                        }

                        # 重新加载订阅列表
                        self.load_subscriptions()

                        return subscription

            return None
        except Exception as e:
            logger.error(f"获取最旧订阅失败: {str(e)}")
            import traceback

            traceback.print_exc()
            return None

    # 使用外部缓存装饰器替换类内部装饰器
    @cache_result(ttl=300)
    def count_current_subscriptions(self):
        """获取当前有效订阅数量，使用缓存以减少数据库查询"""
        try:
            with CSKedayaAPI.db_engine.connect() as conn:
                count_result = conn.execute(
                    sqlalchemy.select(sqlalchemy.func.count())
                    .select_from(subscriptions)
                    .where(
                        sqlalchemy.or_(
                            subscriptions.c.username == None,
                            subscriptions.c.username == "system",
                        )
                    )
                ).scalar()
                logger.info(f"当前有效订阅数量: {count_result}")
                return count_result
        except Exception as e:
            logger.error(f"查询订阅数量失败: {str(e)}")
            import traceback

            traceback.print_exc()
            return 0

    def ensure_min_subscriptions(self, min_count=5):
        """确保至少有指定数量的订阅地址"""

        logger.info("=== 开始订阅生成过程 ===")
        # 清理过期地址
        self.clean_expired_subscriptions()

        # 计算当前有效订阅数量
        try:
            # 使用缓存查询减少数据库负担
            count_result = self.count_current_subscriptions()

            # 计算需要补充的数量
            needed = max(0, min_count - count_result)
            logger.info(f"当前有 {count_result} 个订阅，需要补充 {needed} 个")
        except Exception as e:
            logger.error(f"检查订阅数量失败: {str(e)}")
            import traceback

            traceback.print_exc()
            # 如果查询失败，假设需要生成指定的最小数量
            needed = min_count
            logger.info(f"查询失败，默认补充 {needed} 个订阅")

        # 如果不需要补充
        if needed == 0:
            logger.info("无需补充订阅")
            return []

        # 如果需要补充
        results = []
        attempts = 0
        max_attempts = needed * 3  # 每个需要的订阅尝试最多3次

        logger.info(f"=== 开始生成 {needed} 个订阅，最多尝试 {max_attempts} 次 ===")

        while len(results) < needed and attempts < max_attempts:
            attempts += 1
            logger.info(
                f"正在生成订阅 {len(results) + 1}/{needed}，尝试 {attempts}/{max_attempts}..."
            )

            # 生成单个订阅
            new_sub = self.generate_subscription()
            if new_sub:
                results.append(new_sub)

        logger.info(
            f"=== 订阅生成过程结束，成功生成 {len(results)}/{needed} 个订阅 ==="
        )

        if not results:
            logger.error("无法生成任何订阅，已达到最大尝试次数")

        return results

    def generate_subscription(self):
        """生成单个订阅地址"""

        email = self.random_email()
        password = "123456aa"
        logger.info(f"生成随机邮箱: {email}")

        try:
            # 发送验证码
            send_result = self.send_verification_code(email)

            if (
                not isinstance(send_result, dict)
                or "true" not in str(send_result).lower()
            ):
                logger.error(f"发送验证码失败: {send_result}")
                return None

            # 获取验证码
            email_code = None
            max_code_attempts = 5  # 增加尝试次数

            for code_attempt in range(max_code_attempts):
                # 先等待一段时间让邮件送达
                wait_time = 10 + code_attempt * 5  # 逐渐增加等待时间
                time.sleep(wait_time)

                # 使用 asyncio.run 调用异步方法
                try:
                    email_data = asyncio.run(
                        self.fetch_email("kedaya", 10, "<EMAIL>")
                    )  # 使用kedaya邮箱的密码
                except Exception as e:
                    logger.error(f"调用异步邮件获取方法失败: {str(e)}")
                    continue

                if not email_data:
                    logger.error("获取邮件失败，返回为空")
                    continue

                if not isinstance(email_data, dict):
                    logger.error(f"获取邮件返回格式错误: {type(email_data)}")
                    continue

                if not email_data.get("status"):
                    logger.error(f"获取邮件状态错误: {email_data}")
                    continue

                if not email_data.get("data", {}).get("result"):
                    logger.error("邮件结果为空")
                    continue

                emails = email_data["data"]["result"]
                if not emails:
                    logger.error("没有找到任何邮件")
                    continue

                for email_item in emails:
                    # 添加检查确保email_item是字典
                    if not isinstance(email_item, dict):
                        logger.error(
                            f"邮件项格式错误: {type(email_item)}, 值: {email_item}"
                        )
                        continue

                    email_content = email_item.get("Content", "")
                    to_field = email_item.get("To", "")
                    subject = email_item.get("Subject", "")

                    # 确保所有字段都是字符串格式
                    if isinstance(email_content, bytes):
                        email_content = email_content.decode("utf-8", errors="ignore")
                    if isinstance(to_field, bytes):
                        to_field = to_field.decode("utf-8", errors="ignore")
                    if isinstance(subject, bytes):
                        subject = subject.decode("utf-8", errors="ignore")

                    # 转换为字符串（防止None值）
                    email_content = str(email_content)
                    to_field = str(to_field)
                    subject = str(subject)

                    # 检查邮件是否与当前注册邮箱相关
                    if email in email_content or email in to_field or email in subject:
                        code = extract_verification_code(email_content)
                        if code:
                            email_code = code
                            break
                        else:
                            logger.error("未能从邮件内容中提取验证码")
                            logger.error(f"完整邮件内容: {email_content[:100]}...")

                if email_code:
                    break

                logger.info(
                    f"尝试 {code_attempt + 1}/{max_code_attempts}: 未找到验证码，将重试..."
                )

            if not email_code:
                logger.error("多次尝试后仍无法获取验证码，尝试下一个邮箱")
                return None

            # 注册
            register_result = self.register(email, email_code, password)

            if (
                isinstance(register_result, dict)
                and register_result.get("status") == "success"
                and register_result.get("data", {}).get("token")
            ):
                token = register_result["data"]["token"]
                subscription_url = self.add_subscription(email, password, token)
                return {
                    "email": email,
                    "password": password,
                    "subscription_url": subscription_url,
                }
            else:
                error_msg = (
                    register_result.get("message", "未知错误")
                    if isinstance(register_result, dict)
                    else str(register_result)
                )
                logger.error(f"注册失败，原因: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"生成订阅出错: {str(e)}")
            import traceback

            traceback.print_exc()
            return None

    def clear_cache(self):
        """清除所有缓存数据"""
        # 清除lru_cache装饰的方法缓存
        self._get_subscription_query.cache_clear()

        # 清除函数缓存
        if hasattr(self.count_current_subscriptions, "clear_cache"):
            self.count_current_subscriptions.clear_cache()

        # 清除cookies
        self.session.cookies.clear()

        logger.info("已清除API客户端缓存")

    def close(self):
        """关闭会话，释放资源"""
        if hasattr(self, "session") and self.session:
            try:
                self.session.close()
                logger.info("已关闭API会话")
            except:
                pass


# 应用启动时初始化类级别资源
try:
    CSKedayaAPI.initialize()
except Exception as e:
    logger.error(f"初始化CSKedayaAPI类级别资源失败: {e}")
    # 不让初始化失败阻止模块导入
