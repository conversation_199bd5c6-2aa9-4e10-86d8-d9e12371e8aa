import logging
from datetime import datetime, timezone, timedelta
import sqlalchemy
from sqlalchemy import text

try:
    from api.db import get_db
    from api.models import users, consumption_records
except ImportError:
    from db import get_db
    from models import users, consumption_records

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)


async def record_consumption(username: str, details: str, amount: float):
    """
    记录用户消费信息

    参数:
    username: 用户名
    details: 消费详情
    amount: 消费金额
    """
    try:
        with get_db() as conn:
            with conn.begin():
                # 记录消费
                conn.execute(
                    consumption_records.insert().values(
                        username=username,
                        details=details,
                        amount=amount,
                        created_at=datetime.now(timezone.utc),
                        expires_at=datetime.now(timezone.utc) + timedelta(days=30),
                    )
                )

                # 更新用户余额
                conn.execute(
                    users.update()
                    .where(users.c.username == username)
                    .values(balance=users.c.balance - amount)
                )

                logger.info(
                    f"用户 {username} 消费记录已添加: {details}, 金额: {amount}"
                )
                return True
    except Exception as e:
        logger.error(f"记录消费失败: {str(e)}")
        return False


async def get_user_balance(username: str):
    """获取用户余额"""
    try:
        with get_db() as conn:
            result = conn.execute(
                sqlalchemy.select(users.c.balance).where(users.c.username == username)
            )
            user = result.fetchone()
            if user:
                return user[0]
            return 0
    except Exception as e:
        logger.error(f"获取用户余额失败: {str(e)}")
        return 0


async def record_recharge(username: str, details: str, amount: float):
    """
    记录用户充值信息

    参数:
    username: 用户名
    details: 充值详情
    amount: 充值金额
    """
    try:
        with get_db() as conn:
            with conn.begin():
                # 记录充值（使用负数金额表示充值而非消费）
                conn.execute(
                    consumption_records.insert().values(
                        username=username,
                        details=details,
                        amount=-amount,  # 使用负数表示充值
                        created_at=datetime.now(timezone.utc),
                        expires_at=datetime.now(timezone.utc) + timedelta(days=30),
                    )
                )

                logger.info(
                    f"用户 {username} 充值记录已添加: {details}, 金额: {amount}"
                )
                return True
    except Exception as e:
        logger.error(f"记录充值失败: {str(e)}")
        return False
