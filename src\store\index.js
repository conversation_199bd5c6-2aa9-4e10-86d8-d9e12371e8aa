import { createStore } from 'vuex'

// 安全地访问localStorage
const getItem = (key, defaultValue) => {
  try {
    if (typeof window !== 'undefined' && window.localStorage) {
      const value = window.localStorage.getItem(key)
      return value !== null ? value : defaultValue
    }
    return defaultValue
  } catch (e) {
    return defaultValue
  }
}

// 安全地设置localStorage
const setItem = (key, value) => {
  try {
    if (typeof window !== 'undefined' && window.localStorage) {
      window.localStorage.setItem(key, value)
    }
  } catch (e) {
    console.error('Error setting localStorage', e)
  }
}

// 更新HTML元素的主题类
const applyThemeToDOM = (theme) => {
  try {
    if (typeof window !== 'undefined' && window.document) {
      if (theme === 'dark' || (theme === 'system' && window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark-theme')
      } else {
        document.documentElement.classList.remove('dark-theme')
      }
    }
  } catch (e) {
    console.error('Error applying theme to DOM', e)
  }
}

export default createStore({
  state: {
    theme: getItem('theme', 'system') // 'light', 'dark', 'system'
  },
  mutations: {
    SET_THEME(state, theme) {
      state.theme = theme
      setItem('theme', theme)
      applyThemeToDOM(theme)
    }
  },
  actions: {
    setTheme({ commit }, theme) {
      commit('SET_THEME', theme)
    },
    
    initTheme({ commit, state }) {
      // 检测是否支持系统主题
      const supportsDarkMode = typeof window !== 'undefined' && window.matchMedia ? 
        window.matchMedia('(prefers-color-scheme: dark)').matches : false
      const supportsLightMode = typeof window !== 'undefined' && window.matchMedia ? 
        window.matchMedia('(prefers-color-scheme: light)').matches : false
      const supportsColorScheme = supportsDarkMode || supportsLightMode
      
      // 如果是系统主题，但不支持系统主题检测，则默认使用亮色主题
      if (state.theme === 'system' && !supportsColorScheme) {
        commit('SET_THEME', 'light')
      } else {
        // 确保当前主题正确应用到DOM
        applyThemeToDOM(state.theme)
      }
    }
  },
  getters: {
    currentTheme: (state) => {
      if (state.theme === 'system') {
        return typeof window !== 'undefined' && window.matchMedia ? 
          window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light' : 'light'
      }
      return state.theme
    }
  }
}) 