# -*- coding: utf-8 -*-
import random
from curl_cffi import requests
import time
import json
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from concurrent.futures import ThreadPoolExecutor


# 定义数据模型
class CaptchaRequest(BaseModel):
    gt: str
    challenge: str
    proxy: Optional[str] = None


class ApiClient:
    """API客户端，负责与外部服务通信"""

    def __init__(self, base_url: str = "https://www.qali.cn/api"):
        self.base_url = base_url

    def execute_js(
        self,
        js_path: str,
        function_name: str,
        function_params: Optional[List] = None,
        timeout: int = 10,
    ) -> Dict[str, Any]:
        """调用JS执行API"""
        try:
            payload = {"jsPath": js_path, "functionName": function_name}

            if function_params:
                payload["functionParams"] = function_params

            response = requests.post(
                url=f"{self.base_url}/execute-js", json=payload, timeout=timeout
            )

            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            raise Exception(f"API调用失败: {str(e)}")
        except ValueError as e:
            raise Exception(f"JSON解析失败: {str(e)}")


class GeetestHelper:
    """极验验证码辅助工具类"""

    def __init__(self, api_client: ApiClient):
        self.api_client = api_client
        self.js_path = "geetest3_fullpage.js"

    def get_key(self) -> str:
        """获取密钥"""
        try:
            response = self.api_client.execute_js(self.js_path, "get_key")
            return response["result"]
        except Exception as e:
            raise Exception(f"获取key失败: {str(e)}")

    def get_w1(self, gt: str, challenge: str, key: str) -> str:
        """获取第一个w参数"""
        try:
            response = self.api_client.execute_js(
                self.js_path, "get_w1", [gt, challenge, key]
            )
            return response["result"]
        except Exception as e:
            raise Exception(f"获取w1失败: {str(e)}")

    def get_w2(
        self,
        s: str,
        random_time: str,
        gt: str,
        challenge: str,
        first_time: str,
        key: str,
    ) -> str:
        """获取第二个w参数"""
        try:
            response = self.api_client.execute_js(
                self.js_path, "get_w2", [s, random_time, gt, challenge, first_time, key]
            )
            return response["result"]
        except Exception as e:
            raise Exception(f"获取w2失败: {str(e)}")


class GeetestService:
    """极验验证码服务"""

    def __init__(self):
        self.api_client = ApiClient()
        self.geetest_helper = GeetestHelper(self.api_client)
        self.geetest_api_url = "https://api.geevisit.com"

    def generate_callback_suffix(self) -> str:
        """生成回调函数后缀"""
        return str(round(time.time() * 1000))

    def parse_json_response(self, response_text: str) -> Dict[str, Any]:
        """从响应文本中解析JSON数据"""
        try:
            return json.loads(
                response_text[response_text.index("(") + 1 : response_text.rindex(")")]
            )
        except Exception as e:
            raise Exception(f"解析响应数据失败: {str(e)}")

    def format_proxy(self, proxy: Optional[str] = None) -> Optional[Dict[str, str]]:
        """格式化代理设置"""
        if not proxy:
            return None

        # 如果代理字符串不包含协议前缀，添加http://前缀
        if proxy and not proxy.startswith(("http://", "https://")):
            proxy = f"http://{proxy}"

        return {"http": proxy, "https": proxy}

    def process_captcha(
        self, gt: str, challenge: str, proxy: Optional[str] = None
    ) -> Dict[str, Any]:
        """处理极验验证码流程"""
        try:
            first_time = str(round(time.time() * 1000))

            # 第一步：获取key和w1
            key = self.geetest_helper.get_key()
            w1 = self.geetest_helper.get_w1(gt, challenge, key)

            # 格式化代理设置
            proxies = self.format_proxy(proxy)

            # 第一次请求
            url = (
                f"{self.geetest_api_url}/get.php?gt={gt}"
                f"&challenge={challenge}&lang=zh-cn&pt=0&client_type=web&w={w1}"
                f"&callback=geetest_{self.generate_callback_suffix()}"
            )

            first_response = self.make_geetest_request(url, proxies)

            # 获取s值
            s = first_response["data"]["s"]

            # 第二步：生成w2并发送第二次请求
            random_time = str(random.randint(500, 3000))
            w2 = self.geetest_helper.get_w2(
                s, random_time, gt, challenge, first_time, key
            )

            url = (
                f"{self.geetest_api_url}/ajax.php?gt={gt}"
                f"&challenge={challenge}&lang=zh-cn&pt=0&client_type=web&w={w2}"
                f"&callback=geetest_{self.generate_callback_suffix()}"
            )

            return self.make_geetest_request(url, proxies)

        except Exception as e:
            return {"status": "error", "message": str(e)}

    def make_geetest_request(
        self, url: str, proxies: Optional[Dict[str, str]] = None, timeout: int = 10
    ) -> Dict[str, Any]:
        """发送请求到极验API并解析响应"""
        try:
            response = requests.get(url, proxies=proxies, timeout=timeout)
            response.raise_for_status()
            return self.parse_json_response(response.text)
        except requests.RequestException as e:
            raise Exception(f"请求极验API失败: {str(e)}")
        except Exception as e:
            raise Exception(f"处理极验响应失败: {str(e)}")


def geetest3_fullpage(
    gt: str, challenge: str, proxy: Optional[str] = None
) -> Dict[str, Any]:
    """处理极验验证码逻辑，保留与旧版兼容的接口"""
    service = GeetestService()
    return service.process_captcha(gt, challenge, proxy)


def test_captcha() -> Dict[str, Any]:
    """测试函数，用于本地测试验证码功能"""
    try:
        service = GeetestService()
        url = f"https://www.geetest.com/demo/gt/register-fullpage?t={service.generate_callback_suffix()}"
        res = requests.get(url).json()
        gt = res["gt"]
        challenge = res["challenge"]
        result = geetest3_fullpage(gt, challenge)
        print(result)
        return result
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return {"status": "error", "message": str(e)}


def parallel_test(num_threads: int = 3) -> List[Dict[str, Any]]:
    """并行测试多个验证码处理"""
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(test_captcha) for _ in range(num_threads)]
        results = [future.result() for future in futures]

    success_count = len([r for r in results if r.get("status") == "success"])
    success_rate = success_count / len(results) if results else 0
    print(f"并行处理完成，成功率: {success_rate}")
    return results


if __name__ == "__main__":
    parallel_test(10)
