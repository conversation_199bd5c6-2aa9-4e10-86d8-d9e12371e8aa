"""
微软邮箱API模块 - 提供简单的接口用于获取微软邮箱邮件
"""

import logging
from typing import Dict, List, Optional, Any
import re

from .microsoft_email import MicrosoftEmailClient
from .microsoft_email_ext import MicrosoftEmailExtension
from .performance_monitor import PerformanceMonitor
from .email_list_cache import EmailListCache

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)


def verify_access_token(client) -> bool:
    """
    验证访问令牌是否有效
    通过调用一个简单的Microsoft Graph API来验证
    """
    try:
        if not hasattr(client, "access_token") or not client.access_token:
            logger.warning("客户端缺少访问令牌")
            return False

        # 使用客户端的session来保持代理配置
        headers = {
            "Authorization": f"Bearer {client.access_token}",
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36",
        }

        # 使用客户端的session调用用户信息API来验证token
        response = client.session.get(
            "https://graph.microsoft.com/v1.0/me", headers=headers, timeout=15
        )

        if response.status_code == 200:
            logger.info("访问令牌验证成功")
            return True
        elif response.status_code == 401:
            logger.warning("访问令牌已过期或无效")
            return False
        elif response.status_code == 403:
            logger.warning("访问令牌权限不足")
            return False
        else:
            logger.warning(f"令牌验证返回状态码: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"验证访问令牌时发生错误: {str(e)}")
        return False


def get_microsoft_emails_optimized(
    account: str,
    password: str,
    count: int = 10,
    sender_email: Optional[str] = None,
    mailbox: Optional[str] = None,
    proxy: Optional[str] = None,
    request_host: Optional[str] = None,
    username: Optional[str] = None,
    fast_mode: bool = False,  # 新增：快速模式，跳过详细内容获取
) -> Dict[str, Any]:
    """
    优化版本的微软邮箱邮件获取函数
    使用缓存机制提高性能，适用于Vercel无服务器环境
    """
    import time

    start_time = time.time()

    try:
        from .microsoft_email_cache import MicrosoftEmailCacheManager

        with PerformanceMonitor.monitor_operation("获取微软邮箱邮件", account):
            # 先清理过期缓存（异步执行，不阻塞主流程）
            try:
                MicrosoftEmailCacheManager.cleanup_expired_cache()
            except Exception as e:
                logger.warning(f"清理过期缓存失败: {str(e)}")

            # 尝试获取缓存的登录信息
            cached_login = MicrosoftEmailCacheManager.get_cached_login(
                account, password, username or "unknown"
            )

            if cached_login:
                PerformanceMonitor.log_cache_hit(account, "邮箱登录")

                # 使用缓存创建客户端
                client = MicrosoftEmailCacheManager.create_cached_client(
                    cached_login, proxy
                )

                # 验证访问令牌是否有效
                if not verify_access_token(client):
                    logger.warning("访问令牌已失效，清除缓存并重新登录")
                    # 清除无效的缓存
                    MicrosoftEmailCacheManager.clear_login_cache(
                        account, password, username or "unknown"
                    )
                    cached_login = None  # 标记缓存无效，需要重新登录

            if not cached_login:
                PerformanceMonitor.log_cache_miss(account, "邮箱登录")

                # 执行完整的登录流程
                with PerformanceMonitor.monitor_operation("完整登录流程", account):
                    client = MicrosoftEmailClient(proxy=proxy)

                    # 获取授权URL
                    auth_url = client.get_authorization_url()
                    if not auth_url:
                        return {"status": False, "msg": "获取授权URL失败"}

                    # 提交邮箱
                    success, next_url, ppft = client.submit_email(auth_url, account)
                    if not success:
                        return {"status": False, "msg": "提交邮箱失败"}

                    # 提交密码
                    success, next_url, new_ppft, security_params = (
                        client.submit_password(next_url, account, password, ppft)
                    )
                    if not success:
                        return {"status": False, "msg": "提交密码失败"}

                    # 处理安全保护（如果需要）
                    if security_params:
                        # 这里应该处理安全保护，但为了简化，我们假设不需要
                        pass

                    # 获取授权码
                    success, code = client.get_authorization_code(
                        next_url, new_ppft or ppft
                    )
                    if not success:
                        return {"status": False, "msg": "获取授权码失败"}

                    # 获取refresh_token
                    success, refresh_token = client.get_refresh_token(code)
                    if not success:
                        return {"status": False, "msg": "获取refresh_token失败"}

                    # 获取access_token
                    success, access_token, id_token = client.get_access_token(
                        refresh_token
                    )
                    if not success:
                        return {"status": False, "msg": "获取access_token失败"}

                    # 保存登录缓存
                    try:
                        MicrosoftEmailCacheManager.save_login_cache(
                            account=account,
                            password=password,
                            username=username or "unknown",
                            refresh_token=refresh_token,
                            access_token=access_token,
                            id_token=id_token,
                            puid=client.puid,
                            property_set_id=client.property_set_id,
                        )
                        logger.info(f"登录缓存保存成功，账号: {account}")
                    except Exception as e:
                        logger.warning(f"保存登录缓存失败: {str(e)}")

            # 创建扩展实例
            ext = MicrosoftEmailExtension(client)

            # 尝试获取缓存的邮件列表
            cached_emails = EmailListCache.get_cached_list(
                account, count, sender_email, username
            )

            if cached_emails:
                PerformanceMonitor.log_cache_hit(account, "邮件列表")
                emails = cached_emails
            else:
                PerformanceMonitor.log_cache_miss(account, "邮件列表")

                # 获取邮件列表
                with PerformanceMonitor.monitor_operation("获取邮件列表"):
                    success, emails = ext.get_email_list(count)
                    if not success:
                        # 如果是使用缓存的客户端失败，可能是token过期，清除缓存
                        if cached_login:
                            logger.warning(
                                "使用缓存客户端获取邮件失败，可能token已过期，清除缓存"
                            )
                            MicrosoftEmailCacheManager.clear_login_cache(
                                account, password, username or "unknown"
                            )
                        return {"status": False, "msg": "获取邮件列表失败"}

                # 保存邮件列表缓存
                try:
                    EmailListCache.save_list_cache(
                        account, count, emails, sender_email, username
                    )
                except Exception as e:
                    logger.warning(f"保存邮件列表缓存失败: {str(e)}")

            # 如果指定了发件人，筛选邮件
            if sender_email:
                filtered_emails = []
                for email in emails:
                    if sender_email.lower() in email.get("From", "").lower():
                        filtered_emails.append(email)
                emails = filtered_emails

            # 处理邮件详细内容
            detailed_emails = []

            if fast_mode:
                # 快速模式：只返回邮件列表和预览信息，跳过详细内容获取
                logger.info(f"使用快速模式，跳过详细内容获取")
                for email in emails[:count]:
                    # 移除内部字段
                    if "_FamilyId" in email:
                        del email["_FamilyId"]
                    # 添加快速模式标识
                    email["fast_mode"] = True
                    detailed_emails.append(email)
            else:
                # 标准模式：获取详细内容（并发优化）
                with PerformanceMonitor.monitor_operation("获取邮件详细内容"):
                    # 限制并发获取的邮件数量，避免过多请求
                    batch_size = min(count, 3)  # 减少到每批最多3封邮件，提高稳定性

                    for i in range(0, min(len(emails), count), batch_size):
                        batch_emails = emails[i : i + batch_size]

                        for email in batch_emails:
                            family_id = email.get("_FamilyId", "")  # 使用内部字段
                            if family_id:
                                # 添加超时控制
                                try:
                                    success, content = ext.get_email_content(
                                        family_id,
                                        request_host=request_host,
                                        username=username,
                                    )
                                    if success:
                                        # 合并基本信息和详细内容
                                        email.update(content)
                                        # 移除不需要的字段
                                        if "_FamilyId" in email:
                                            del email["_FamilyId"]
                                        detailed_emails.append(email)
                                    else:
                                        # 如果获取详细内容失败，仍然添加基本信息
                                        if "_FamilyId" in email:
                                            del email["_FamilyId"]
                                        detailed_emails.append(email)
                                except Exception as e:
                                    logger.warning(f"获取邮件详细内容失败: {str(e)}")
                                    # 添加基本信息，不阻塞整个流程
                                    if "_FamilyId" in email:
                                        del email["_FamilyId"]
                                    detailed_emails.append(email)

                        # 如果是批量处理，添加小延迟避免过于频繁的请求
                        if len(batch_emails) > 1 and i + batch_size < min(
                            len(emails), count
                        ):
                            import time

                            time.sleep(0.05)  # 减少到50ms延迟

            # 记录性能摘要
            end_time = time.time()
            duration = end_time - start_time
            PerformanceMonitor.log_performance_summary(
                total_emails=len(detailed_emails),
                new_emails=0,  # 这里暂时设为0，实际应该从调用方传入
                cached=cached_login is not None,
                duration=duration,
                account=account,
            )

            return {
                "status": True,
                "msg": "获取邮件成功",
                "count": len(detailed_emails),
                "emails": detailed_emails,
                "cached": cached_login is not None,  # 标识是否使用了缓存
                "performance": {
                    "duration": round(duration, 2),
                    "cached": cached_login is not None,
                    "email_count": len(detailed_emails),
                },
            }

    except Exception as e:
        logger.error(f"获取微软邮箱邮件时发生错误: {str(e)}")
        return {"status": False, "msg": f"获取邮件失败: {str(e)}"}


def get_microsoft_emails(
    account: str,
    password: str,
    count: int = 10,
    sender_email: Optional[str] = None,
    mailbox: Optional[str] = None,
    proxy: Optional[str] = None,
    request_host: Optional[str] = None,
    username: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取微软邮箱邮件

    Args:
        account: 微软邮箱账号
        password: 微软邮箱密码
        count: 获取邮件数量，默认10
        sender_email: 发件人邮箱地址，用于筛选特定发件人的邮件
        mailbox: 邮箱文件夹，默认为收件箱
        proxy: 代理地址，格式为 "host:port"

    Returns:
        包含状态和邮件列表的字典
    """
    try:
        # 创建微软邮箱客户端
        client = MicrosoftEmailClient(proxy=proxy)

        # 获取授权URL
        auth_url = client.get_authorization_url()
        if not auth_url:
            return {"status": False, "msg": "获取授权URL失败"}

        # 提交邮箱
        success, next_url, ppft = client.submit_email(auth_url, account)
        if not success:
            return {"status": False, "msg": "提交邮箱失败"}

        # 提交密码
        success, next_url, new_ppft, security_params = client.submit_password(
            next_url, account, password, ppft
        )
        if not success:
            return {"status": False, "msg": "提交密码失败"}

        # 处理安全保护（如果需要）
        if security_params:
            # 这里应该处理安全保护，但为了简化，我们假设不需要
            pass

        # 获取授权码
        success, code = client.get_authorization_code(next_url, new_ppft or ppft)
        if not success:
            return {"status": False, "msg": "获取授权码失败"}

        # 获取refresh_token
        success, refresh_token = client.get_refresh_token(code)
        if not success:
            return {"status": False, "msg": "获取refresh_token失败"}

        # 获取access_token
        success, access_token, id_token = client.get_access_token(refresh_token)
        if not success:
            return {"status": False, "msg": "获取access_token失败"}

        # 创建扩展实例
        ext = MicrosoftEmailExtension(client)

        # 获取邮件列表
        success, emails = ext.get_email_list(count)
        if not success:
            return {"status": False, "msg": "获取邮件列表失败"}

        # 如果指定了发件人，筛选邮件
        if sender_email:
            filtered_emails = []
            for email in emails:
                if sender_email.lower() in email.get("From", "").lower():
                    filtered_emails.append(email)
            emails = filtered_emails

        # 获取每封邮件的详细内容
        detailed_emails = []
        for email in emails[:count]:  # 限制处理的邮件数量
            family_id = email.get("_FamilyId", "")  # 使用内部字段
            if family_id:
                success, content = ext.get_email_content(
                    family_id, request_host=request_host, username=username
                )
                if success:
                    # 合并基本信息和详细内容
                    email.update(content)
                    # 移除不需要的字段
                    if "_FamilyId" in email:
                        del email["_FamilyId"]
                    detailed_emails.append(email)
                else:
                    # 如果获取详细内容失败，仍然添加基本信息
                    if "_FamilyId" in email:
                        del email["_FamilyId"]
                    detailed_emails.append(email)

        return {
            "status": True,
            "msg": "获取邮件成功",
            "count": len(detailed_emails),
            "emails": detailed_emails,
        }

    except Exception as e:
        logger.error(f"获取微软邮箱邮件时发生错误: {str(e)}")
        return {"status": False, "msg": f"获取邮件失败: {str(e)}"}


def extract_verification_code(
    content: str, patterns: List[str] = None
) -> Optional[str]:
    """
    从邮件内容中提取验证码

    Args:
        content: 邮件内容
        patterns: 验证码匹配模式列表，默认为常见的验证码格式

    Returns:
        提取的验证码，如果未找到则返回None
    """
    if not patterns:
        patterns = [
            r"验证码[：:]\s*([0-9]{4,6})",
            r"验证码是[：:]\s*([0-9]{4,6})",
            r"验证码为[：:]\s*([0-9]{4,6})",
            r"code[：:]\s*([0-9]{4,6})",
            r"验证码：</span>([0-9]{4,6})",
            r"验证码：</strong>([0-9]{4,6})",
            r"验证码：</b>([0-9]{4,6})",
            r"验证码：<[^>]+>([0-9]{4,6})",
            r"验证码\s*<[^>]+>\s*([0-9]{4,6})",
            r"验证码是\s*<[^>]+>\s*([0-9]{4,6})",
            r"验证码为\s*<[^>]+>\s*([0-9]{4,6})",
            r"[^0-9]([0-9]{6})[^0-9]",
            r"[^0-9]([0-9]{4})[^0-9]",
        ]

    for pattern in patterns:
        match = re.search(pattern, content)
        if match:
            return match.group(1)

    return None
