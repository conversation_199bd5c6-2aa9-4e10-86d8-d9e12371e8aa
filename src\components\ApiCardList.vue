<template>
    <div class="cards-container">
        <!-- 加载中状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-animation"></div>
            <p>加载API数据中...</p>
        </div>

        <!-- 加载错误状态 -->
        <div v-else-if="error" class="error-container">
            <div class="error-icon">❌</div>
            <p>加载API数据失败</p>
            <LoadingButton class="retry-btn" @click="$emit('reload')">重试</LoadingButton>
        </div>

        <!-- API列表 -->
        <div v-else v-for="(api, index) in apis" :key="index" class="card" @click="$emit('openModal', index)">
            <el-popover placement="bottom" :width="300" trigger="hover" popper-class="api-description-popover">
                <template #default>
                    <div class="popover-content">
                        <div class="popover-icon">{{ api.icon }}</div>
                        <div class="popover-text">{{ api.description }}</div>
                        <div class="popover-cost" v-if="extractCost(api.description) !== '未知'">
                            <span class="cost-label">消费点数:</span>
                            <span class="cost-value">{{ extractCost(api.description) }}</span>
                        </div>
                    </div>
                </template>
                <template #reference>
                    <div class="card-content">
                        <h3>{{ api.title }}</h3>
                        <div class="icon">{{ api.icon }}</div>
                        <div class="cost-badge" v-if="extractCost(api.description) !== '未知'">
                            消费点数: <span>{{ extractCost(api.description) }}</span>
                        </div>
                    </div>
                </template>
            </el-popover>
        </div>
    </div>
</template>

<script setup>
import { ElPopover } from 'element-plus'
import LoadingButton from './LoadingButton.vue'

defineProps({
    apis: {
        type: Array,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    },
    error: {
        type: Boolean,
        default: false
    }
})

defineEmits(['openModal', 'reload'])

// 从描述中提取消费点数
const extractCost = (description) => {
    // 从描述中提取消费点数
    if (!description) return '未知';

    // 匹配模式如 "扣费1.0余额", "扣费150.0余额", "每次调用扣费200.0余额" 等
    const match = description.match(/(?:扣费|每次调用扣费)(\d+(?:\.\d+)?)余额/);
    return match ? match[1] : '未知';
}
</script> 