<template>
    <button :class="['loading-button', className]" :disabled="loading || disabled" @click="$emit('click', $event)">
        <div class="button-content">
            <span class="loading-spinner" v-if="loading"></span>
            <slot></slot>
        </div>
    </button>
</template>

<script setup>
defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    disabled: {
        type: Boolean,
        default: false
    },
    className: {
        type: String,
        default: ''
    }
})

defineEmits(['click'])
</script>

<style lang="scss" scoped>
.loading-button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    -webkit-user-select: none;

    &:hover:not(:disabled) {
        transform: translateY(-2px);
    }

    &:disabled {
        opacity: 0.8;
        cursor: not-allowed;
        transform: none !important;
    }

    .button-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        min-height: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .loading-spinner {
        display: inline-block;
        width: 1.2em;
        height: 1.2em;
        border: 2px solid rgba(255, 255, 255, 0.9);
        border-right-color: transparent !important;
        border-radius: 50%;
        animation: spin 0.6s linear infinite;
        opacity: 0.9;
    }

    // 默认按钮样式
    &:not(.reset-token-btn):not(.logout-btn):not(.test-btn) {
        background: linear-gradient(135deg, #1a1a1a 0%, #333 100%);

        &:hover:not(:disabled) {
            background: linear-gradient(135deg, #333 0%, #444 100%);
        }

        &:disabled {
            background: linear-gradient(135deg, #1a1a1a 0%, #333 100%);
        }
    }

    // 测试按钮样式
    &.test-btn {
        background: linear-gradient(135deg, #1a1a1a 0%, #333 100%);

        &:hover:not(:disabled) {
            background: linear-gradient(135deg, #333 0%, #444 100%);
        }

        &:disabled {
            background: linear-gradient(135deg, #1a1a1a 0%, #333 100%);
        }
    }

    // 重置Token按钮样式
    &.reset-token-btn {
        background: linear-gradient(135deg, #1a1a1a 0%, #333 100%);

        &:hover:not(:disabled) {
            background: linear-gradient(135deg, #333 0%, #444 100%);
        }

        &:disabled {
            background: linear-gradient(135deg, #1a1a1a 0%, #333 100%);
        }
    }

    // 退出登录按钮样式
    &.logout-btn {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);

        &:hover:not(:disabled) {
            background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
        }

        &:disabled {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>