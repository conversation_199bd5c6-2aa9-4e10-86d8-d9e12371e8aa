# API包初始化文件
# 这个文件允许Python将目录视为包
# 同时也允许相对导入从.import email_module
from curl_cffi import requests
import uuid
import time
import json


def __third_step4(captchaId, proxy=None):
    """检查验证码类型并尝试直接获取validate"""
    try:
        proxies = None
        if proxy:
            # 如果代理字符串不包含协议前缀，添加http://前缀
            if not proxy.startswith(("http://", "https://")):
                proxy = f"http://{proxy}"
            proxies = {"http": proxy, "https": proxy}
        challenge = str(uuid.uuid4())
        url = (
            "https://gcaptcha4.geetest.com/load?captcha_id="
            + captchaId
            + "&challenge="
            + challenge
            + "&client_type=web&risk_type=ai&lang=zh&callback=geetest_"
            + str(round(time.time() * 1000))
        )

        res = requests.get(url, proxies=proxies).text
        res = json.loads(res[res.index("(") + 1 : res.rindex(")")])
        status = res["status"]
        captcha_type = res["data"]["captcha_type"]
        print(captcha_type)

        if status == "success":
            return ("success", captcha_type)
        else:
            return (status, None)
    except Exception as e:
        print(f"验证码类型检测失败: {str(e)}")
        return ("error", str(e))


def __third_step(gt, challenge, proxy=None):
    """检查验证码类型并尝试直接获取validate"""
    try:
        callback = f"geetest_{int(time.time() * 1000)}"
        payload = {
            "gt": gt,
            "challenge": challenge,
            "lang": "zh-cn",
            "pt": 0,
            "client_type": "web",
            "w": "",
            "callback": callback,
        }
        # 处理代理
        proxies = None
        if proxy:
            # 如果代理字符串不包含协议前缀，添加http://前缀
            if not proxy.startswith(("http://", "https://")):
                proxy = f"http://{proxy}"
            proxies = {"http": proxy, "https": proxy}

        resp_text = requests.get(
            "https://api.geevisit.com/ajax.php", params=payload, proxies=proxies
        ).text
        status = json.loads(resp_text.replace(f"{callback}(", "").replace(")", ""))[
            "status"
        ]
        if status == "success":
            data = json.loads(resp_text.replace(f"{callback}(", "").replace(")", ""))[
                "data"
            ]
            validate = data.get("validate", "")
            if validate != "":
                return ("sense", validate)
            return ("success", data.get("result", ""))
        else:
            return (status, None)
    except Exception as e:
        print(f"验证码类型检测失败: {str(e)}")
        return ("error", str(e))
