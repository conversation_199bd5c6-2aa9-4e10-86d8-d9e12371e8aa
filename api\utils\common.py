from fastapi import Request


def get_client_ip(request: Request) -> str:
    """获取客户端源 IP 地址"""
    return request.client.host if request.client else "unknown"


def get_request_host(request: Request) -> str:
    """获取请求的完整host信息（包含协议和域名）

    Args:
        request: FastAPI Request对象

    Returns:
        str: 完整的host URL，如 "https://api.kedaya.xyz"
    """
    if not request:
        return None

    # 获取协议
    scheme = request.url.scheme or "https"

    # 获取host（包含端口）
    host = request.headers.get("host") or str(request.url.netloc)

    # 如果host为空，尝试从URL获取
    if not host:
        host = request.url.hostname
        if request.url.port and request.url.port not in [80, 443]:
            host = f"{host}:{request.url.port}"

    return f"{scheme}://{host}" if host else None
