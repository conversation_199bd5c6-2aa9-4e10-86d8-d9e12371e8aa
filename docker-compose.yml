services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    container_name: kedaya_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: neondb
      POSTGRES_USER: neondb_owner
      POSTGRES_PASSWORD: npg_0NBVRjas2PHf
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - kedaya_network

  # 应用服务 (后端API + 前端静态文件)
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kedaya_app
    restart: unless-stopped
    environment:
      DATABASE_URL: ********************************************************/neondb?sslmode=disable
      PORT: 8000
      VERCEL: "0"
      ADMIN_KEY: admin-secret-key
    expose:
      - "8000"
    depends_on:
      - postgres
    networks:
      - kedaya_network

  # Nginx 反向代理 (可选，用于生产环境)
  nginx:
    image: nginx:alpine
    container_name: kedaya_nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    networks:
      - kedaya_network

volumes:
  postgres_data:

networks:
  kedaya_network:
    driver: bridge
