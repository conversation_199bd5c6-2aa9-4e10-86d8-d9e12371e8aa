import os
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import CryptContext
from datetime import datetime, timedelta, timezone
from typing import Optional
from sqlalchemy import select

try:
    from api.models import users
    from api.db import get_db
except:
    from models import users
    from db import get_db

# JWT 配置
SECRET_KEY = os.getenv(
    "SECRET_KEY", "your-secret-key"
)  # 在生产环境中请在 Vercel 环境变量中配置 SECRET_KEY
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 525600

# 密码加密
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__default_rounds=12,
    bcrypt__min_rounds=12,
    bcrypt__max_rounds=12,
)

# 认证方案
security = HTTPBearer()

# Turnstile 配置
TURNSTILE_SECRET_KEY = os.getenv(
    "TURNSTILE_SECRET_KEY", "0x4AAAAAABAsD4sQXsWbaES1gIMYccrrXk0"
)
TURNSTILE_VERIFY_URL = "https://challenges.cloudflare.com/turnstile/v0/siteverify"


# 创建 JWT token
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
        to_encode.update({"exp": expire})
    # 当expires_delta为None时，不添加exp字段，表示永久有效
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


# 获取当前用户的函数，用于验证用户身份
def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    credentials_exception = HTTPException(
        status_code=401,
        detail="无效的认证凭证",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        token = credentials.credentials

        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    with get_db() as db:
        # 优化查询，只获取需要的字段
        result = db.execute(
            select(
                users.c.username,
                users.c.balance,
                users.c.token,
                users.c.inviter,
                users.c.invite_reward,
            ).where(users.c.username == username)
        )
        user = result.fetchone()
        if user is None:
            raise credentials_exception

        # 创建字典以确保可以使用字符串键访问
        user_dict = {}
        columns = [
            "username",
            "balance",
            "token",
            "inviter",
            "invite_reward",
        ]
        for i, column in enumerate(columns):
            user_dict[column] = user[i]

        # 验证传入的 token 是否与数据库中存储的最新 token 匹配
        if user_dict["token"] != token:
            raise HTTPException(
                status_code=401,
                detail="token已失效，请重新获取",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return user_dict


# 获取用户信息的函数 - 通过URL中的token获取
def get_user_by_token(token: str):
    """通过URL中的token获取用户信息"""
    try:
        # 解析token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
    except JWTError:
        return None

    with get_db() as db:
        # 查询用户信息
        result = db.execute(
            select(
                users.c.username,
                users.c.balance,
                users.c.token,
                users.c.inviter,
                users.c.invite_reward,
            ).where(users.c.username == username)
        )
        user = result.fetchone()
        if user is None:
            return None

        # 创建字典
        user_dict = {}
        columns = [
            "username",
            "balance",
            "token",
            "inviter",
            "invite_reward",
        ]
        for i, column in enumerate(columns):
            user_dict[column] = user[i]

        # 验证token是否匹配
        if user_dict["token"] != token:
            return None

        return user_dict


# 获取客户端IP
def get_client_ip(request: Request) -> str:
    """获取客户端源 IP 地址"""
    return request.client.host if request.client else "unknown"


# Turnstile验证函数
async def verify_turnstile(token: str) -> bool:
    import requests

    try:
        response = requests.post(
            TURNSTILE_VERIFY_URL,
            data={"secret": TURNSTILE_SECRET_KEY, "response": token},
        )
        result = response.json()
        print(f"Turnstile验证结果: {result}")
        # 只检查success字段，忽略其他信息
        return result.get("success", False)
    except Exception as e:
        print(f"Turnstile 验证失败: {str(e)}")
        return False
