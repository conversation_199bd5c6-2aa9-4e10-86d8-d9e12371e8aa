import os
import sqlalchemy
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from sqlalchemy import inspect, text
from datetime import datetime, timedelta, timezone
import time
from fastapi import HTTPException
from functools import wraps

try:
    from api.models import metadata, access_logs
except:
    from models import metadata, access_logs

# 检查是否在Vercel环境中运行
IS_VERCEL = os.environ.get("VERCEL", "0") == "1"

# 数据库配置
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require",
)

# 为Vercel环境优化连接池参数
if IS_VERCEL:
    # Vercel函数环境中使用更小的连接池配置
    engine = sqlalchemy.create_engine(
        DATABASE_URL,
        pool_size=5,  # 增加最小连接数
        max_overflow=300,  # 增加最大溢出连接数
        pool_timeout=60,  # 增加超时时间
        pool_recycle=300,  # 缩短连接回收时间
        pool_pre_ping=True,  # 每次使用前ping连接确保连接有效
        poolclass=QueuePool,
    )
else:
    # 开发环境使用原有配置
    engine = sqlalchemy.create_engine(
        DATABASE_URL,
        pool_size=5,
        max_overflow=300,
        pool_timeout=60,
        pool_recycle=300,
        pool_pre_ping=True,
        poolclass=QueuePool,
    )

# 创建会话工厂
SessionLocal = sessionmaker(bind=engine)


# 获取数据库会话
@contextmanager
def get_db():
    db = None
    try:
        db = SessionLocal()
        yield db
    except Exception as e:
        print(f"数据库会话错误: {str(e)}")
        raise
    finally:
        # 确保连接总是被关闭
        if db:
            try:
                db.close()
            except Exception as e:
                print(f"关闭数据库连接错误: {str(e)}")


# 数据库操作重试装饰器
def with_db_retry(max_retries=3):
    """
    数据库操作重试装饰器
    :param max_retries: 最大重试次数
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retry_count = 0
            last_error = None

            while retry_count < max_retries:
                try:
                    return func(*args, **kwargs)
                except sqlalchemy.exc.OperationalError as e:
                    retry_count += 1
                    last_error = e
                    error_msg = str(e)

                    print(
                        f"数据库操作失败 (尝试 {retry_count}/{max_retries}): {error_msg}"
                    )

                    if retry_count >= max_retries:
                        break

                    # 指数退避重试
                    wait_time = 0.2 * (2**retry_count)
                    print(f"等待 {wait_time:.2f}秒 后重试...")
                    time.sleep(wait_time)
                except sqlalchemy.exc.ResourceClosedError as e:
                    # 处理资源已关闭错误
                    print(f"资源已关闭错误: {str(e)}")
                    retry_count += 1
                    last_error = e

                    if retry_count >= max_retries:
                        break
                    time.sleep(0.5)
                except Exception as e:
                    # 记录其他非数据库特定错误
                    print(f"操作错误: {str(e)}")
                    raise HTTPException(
                        status_code=500, detail=f"服务器内部错误: {str(e)}"
                    )

            # 达到最大重试次数后，根据错误类型返回适当的HTTP异常
            if last_error:
                error_msg = str(last_error)
                if "Lock wait timeout" in error_msg:
                    raise HTTPException(status_code=429, detail="系统繁忙，请稍后再试")
                elif "Connection" in error_msg or "QueuePool" in error_msg:
                    raise HTTPException(
                        status_code=503, detail="服务暂时不可用，请稍后再试"
                    )
                else:
                    print(f"数据库操作错误，已达最大重试次数: {error_msg}")
                    raise HTTPException(status_code=500, detail="操作失败，请稍后重试")

            # 如果循环正常结束但未返回结果
            raise HTTPException(status_code=429, detail="请求失败，请稍后重试")

        return wrapper

    return decorator


# 清理过期记录函数
def cleanup_expired_records():
    """
    清理数据库中过期的记录
    - email_history表中expires_at小于当前时间的记录
    - email_creation_logs表中expires_at小于当前时间的记录
    - temp_html_storage表中expires_at小于当前时间的记录
    - microsoft_login_cache表中expires_at小于当前时间的记录
    """
    try:
        with engine.begin() as conn:
            total_cleaned = 0

            # 清理email_history表
            try:
                if inspect(engine).has_table("email_history"):
                    # 检查expires_at字段是否存在
                    column_exists = conn.execute(
                        text(
                            "SELECT EXISTS (SELECT FROM information_schema.columns "
                            "WHERE table_name = 'email_history' AND column_name = 'expires_at')"
                        )
                    ).scalar()

                    if column_exists:
                        result = conn.execute(
                            text("DELETE FROM email_history WHERE expires_at < NOW()")
                        )
                        cleaned_count = result.rowcount
                        total_cleaned += cleaned_count
                        if cleaned_count > 0:
                            print(f"已清理 {cleaned_count} 条过期的email_history记录")
            except Exception as e:
                print(f"清理email_history表时出错: {e}")

            # 清理email_creation_logs表
            try:
                if inspect(engine).has_table("email_creation_logs"):
                    result = conn.execute(
                        text("DELETE FROM email_creation_logs WHERE expires_at < NOW()")
                    )
                    cleaned_count = result.rowcount
                    total_cleaned += cleaned_count
                    if cleaned_count > 0:
                        print(f"已清理 {cleaned_count} 条过期的email_creation_logs记录")
            except Exception as e:
                print(f"清理email_creation_logs表时出错: {e}")

            # 清理temp_html_storage表
            try:
                if inspect(engine).has_table("temp_html_storage"):
                    current_time = int(time.time())
                    result = conn.execute(
                        text(
                            f"DELETE FROM temp_html_storage WHERE expires_at < {current_time}"
                        )
                    )
                    cleaned_count = result.rowcount
                    total_cleaned += cleaned_count
                    if cleaned_count > 0:
                        print(f"已清理 {cleaned_count} 条过期的temp_html_storage记录")
            except Exception as e:
                print(f"清理temp_html_storage表时出错: {e}")

            # 清理microsoft_login_cache表
            try:
                if inspect(engine).has_table("microsoft_login_cache"):
                    result = conn.execute(
                        text(
                            "DELETE FROM microsoft_login_cache WHERE expires_at < NOW()"
                        )
                    )
                    cleaned_count = result.rowcount
                    total_cleaned += cleaned_count
                    if cleaned_count > 0:
                        print(
                            f"已清理 {cleaned_count} 条过期的microsoft_login_cache记录"
                        )
            except Exception as e:
                print(f"清理microsoft_login_cache表时出错: {e}")

            if total_cleaned > 0:
                print(f"总共清理了 {total_cleaned} 条过期记录")

    except Exception as e:
        print(f"清理过期记录时发生错误: {e}")


# 初始化数据库表
def init_db():
    # 创建表
    try:
        with engine.begin() as conn:
            if not inspect(engine).has_table("users"):
                metadata.tables["users"].create(conn)
                print("用户表创建成功")
            if not inspect(engine).has_table("email_history"):
                metadata.tables["email_history"].create(conn)
                print("邮件历史表创建成功")

            # 检查并创建临时HTML存储表
            try:
                if not inspect(engine).has_table("temp_html_storage"):
                    print("创建temp_html_storage表...")
                    metadata.tables["temp_html_storage"].create(conn)
                    print("temp_html_storage表创建成功")

                    # 添加索引
                    conn.execute(
                        text(
                            "CREATE INDEX IF NOT EXISTS idx_temp_html_expires ON temp_html_storage (expires_at)"
                        )
                    )
                    conn.execute(
                        text(
                            "CREATE INDEX IF NOT EXISTS idx_temp_html_content_hash ON temp_html_storage (content_hash)"
                        )
                    )
                    print("temp_html_storage索引创建成功")
                else:
                    # 检查并添加缺少的字段
                    print("检查temp_html_storage表结构...")

                    # 检查username字段
                    username_exists = conn.execute(
                        text(
                            "SELECT EXISTS (SELECT FROM information_schema.columns "
                            "WHERE table_name = 'temp_html_storage' AND column_name = 'username')"
                        )
                    ).scalar()

                    if not username_exists:
                        print("添加username字段到temp_html_storage表...")
                        conn.execute(
                            text(
                                "ALTER TABLE temp_html_storage ADD COLUMN username VARCHAR(50)"
                            )
                        )
                        print("username字段添加成功")

                    # 检查content_hash字段
                    content_hash_exists = conn.execute(
                        text(
                            "SELECT EXISTS (SELECT FROM information_schema.columns "
                            "WHERE table_name = 'temp_html_storage' AND column_name = 'content_hash')"
                        )
                    ).scalar()

                    if not content_hash_exists:
                        print("添加content_hash字段到temp_html_storage表...")
                        conn.execute(
                            text(
                                "ALTER TABLE temp_html_storage ADD COLUMN content_hash VARCHAR(32) NOT NULL DEFAULT ''"
                            )
                        )
                        # 为已有记录生成哈希值
                        conn.execute(
                            text(
                                "UPDATE temp_html_storage SET content_hash = MD5(html_content::bytea)"
                            )
                        )
                        print("content_hash字段添加并填充成功")

                        # 添加内容哈希索引
                        conn.execute(
                            text(
                                "CREATE INDEX IF NOT EXISTS idx_temp_html_content_hash ON temp_html_storage (content_hash)"
                            )
                        )
                        print("content_hash索引添加成功")
            except Exception as e:
                print(f"处理temp_html_storage表时出错: {e}")

            # 直接尝试创建订阅表，不管是否存在
            try:
                metadata.tables["subscriptions"].create(conn, checkfirst=True)
                print("订阅地址表创建成功或已存在")
            except Exception as e:
                print(f"创建订阅表时出错: {e}")

            # 强制创建所有表
            try:
                metadata.create_all(conn, checkfirst=True)
                print("所有表创建完成")
            except Exception as e:
                print(f"创建所有表时出错: {e}")

            # 检查并迁移email_history表，添加expires_at字段
            try:
                # 检查expires_at字段是否已存在
                column_exists = conn.execute(
                    text(
                        "SELECT EXISTS (SELECT FROM information_schema.columns "
                        "WHERE table_name = 'email_history' AND column_name = 'expires_at')"
                    )
                ).scalar()

                if not column_exists:
                    print("为email_history表添加expires_at字段...")
                    # 添加expires_at字段
                    conn.execute(
                        text(
                            "ALTER TABLE email_history "
                            "ADD COLUMN expires_at TIMESTAMP WITH TIME ZONE"
                        )
                    )
                    print("expires_at字段添加成功")

                    # 为现有记录设置过期时间（received_at + 30天）
                    print("为现有记录设置过期时间...")
                    conn.execute(
                        text(
                            "UPDATE email_history "
                            "SET expires_at = received_at + INTERVAL '30 days' "
                            "WHERE expires_at IS NULL"
                        )
                    )
                    print("现有记录过期时间设置成功")

                    # 修改expires_at字段，添加默认值约束
                    conn.execute(
                        text(
                            "ALTER TABLE email_history "
                            "ALTER COLUMN expires_at SET DEFAULT (NOW() + INTERVAL '30 days')"
                        )
                    )
                    print("设置expires_at默认值成功")
            except Exception as e:
                print(f"迁移email_history表时出错: {e}")

            # 检查并创建email_creation_logs表
            try:
                if not inspect(engine).has_table("email_creation_logs"):
                    print("创建email_creation_logs表...")
                    conn.execute(
                        text(
                            "CREATE TABLE email_creation_logs ("
                            "id SERIAL PRIMARY KEY, "
                            "username VARCHAR(50) NOT NULL, "
                            "email VARCHAR(100) NOT NULL, "
                            "created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(), "
                            "expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days')"
                            ")"
                        )
                    )
                    print("email_creation_logs表创建成功")
            except Exception as e:
                print(f"创建email_creation_logs表时出错: {e}")

            # 检查并创建/更新email_sending_logs表
            try:
                if not inspect(engine).has_table("email_sending_logs"):
                    print("创建email_sending_logs表...")
                    conn.execute(
                        text(
                            "CREATE TABLE email_sending_logs ("
                            "id SERIAL PRIMARY KEY, "
                            "username VARCHAR(50) NOT NULL, "
                            "from_email VARCHAR(100) NOT NULL, "
                            "to_email VARCHAR(100) NOT NULL, "
                            "subject VARCHAR(200) NOT NULL, "
                            "content_hash VARCHAR(32) NOT NULL, "
                            "sent_at TIMESTAMP WITH TIME ZONE NOT NULL, "
                            "status BOOLEAN DEFAULT TRUE, "
                            "sent_at_ts INTEGER DEFAULT EXTRACT(EPOCH FROM NOW())"
                            ")"
                        )
                    )
                    print("email_sending_logs表创建成功")
                else:
                    # 检查sent_at_ts字段是否存在
                    sent_at_ts_exists = conn.execute(
                        text(
                            "SELECT EXISTS (SELECT FROM information_schema.columns "
                            "WHERE table_name = 'email_sending_logs' AND column_name = 'sent_at_ts')"
                        )
                    ).scalar()

                    if not sent_at_ts_exists:
                        print("为email_sending_logs表添加sent_at_ts字段...")
                        conn.execute(
                            text(
                                "ALTER TABLE email_sending_logs "
                                "ADD COLUMN sent_at_ts INTEGER DEFAULT EXTRACT(EPOCH FROM NOW())"
                            )
                        )
                        # 为现有记录设置时间戳
                        conn.execute(
                            text(
                                "UPDATE email_sending_logs "
                                "SET sent_at_ts = EXTRACT(EPOCH FROM sent_at) "
                                "WHERE sent_at_ts IS NULL"
                            )
                        )
                        print("sent_at_ts字段添加成功")
            except Exception as e:
                print(f"处理email_sending_logs表时出错: {e}")

            # 检查并创建microsoft_login_cache表
            try:
                if not inspect(engine).has_table("microsoft_login_cache"):
                    print("创建microsoft_login_cache表...")
                    conn.execute(
                        text(
                            "CREATE TABLE microsoft_login_cache ("
                            "id SERIAL PRIMARY KEY, "
                            "account VARCHAR(128) NOT NULL, "
                            "account_hash VARCHAR(64) NOT NULL UNIQUE, "
                            "refresh_token TEXT NOT NULL, "
                            "access_token TEXT, "
                            "id_token TEXT, "
                            "puid VARCHAR(128), "
                            "property_set_id VARCHAR(128), "
                            "login_time TIMESTAMP WITH TIME ZONE NOT NULL, "
                            "expires_at TIMESTAMP WITH TIME ZONE NOT NULL, "
                            "last_used TIMESTAMP WITH TIME ZONE NOT NULL, "
                            "username VARCHAR(50) NOT NULL"
                            ")"
                        )
                    )
                    # 创建索引
                    conn.execute(
                        text(
                            "CREATE INDEX IF NOT EXISTS idx_microsoft_cache_account ON microsoft_login_cache (account)"
                        )
                    )
                    conn.execute(
                        text(
                            "CREATE INDEX IF NOT EXISTS idx_microsoft_cache_expires ON microsoft_login_cache (expires_at)"
                        )
                    )
                    print("microsoft_login_cache表创建成功")
            except Exception as e:
                print(f"创建microsoft_login_cache表时出错: {e}")

            # 在Vercel环境中跳过不必要的序列初始化
            if not IS_VERCEL:
                # 修复所有表的序列
                tables_with_id = [
                    "users",
                    "access_logs",
                    "timeout_refunds",
                    "success_conversations",
                    "email_history",
                    "card_keys",
                    "subscriptions",  # 添加订阅表
                    "email_creation_logs",  # 添加邮箱创建日志表
                    "email_sending_logs",  # 添加邮件发送日志表
                    "microsoft_login_cache",  # 添加微软邮箱登录缓存表
                ]

                for table_name in tables_with_id:
                    try:
                        # 检查表是否存在
                        table_exists = conn.execute(
                            text(
                                f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table_name}')"
                            )
                        ).scalar()

                        if table_exists:
                            # 获取当前表的最大ID
                            max_id_result = conn.execute(
                                text(f"SELECT MAX(id) FROM {table_name}")
                            ).fetchone()
                            max_id = (
                                max_id_result[0]
                                if max_id_result and max_id_result[0] is not None
                                else 0
                            )

                            # 重置序列为最大ID+1
                            conn.execute(
                                text(
                                    f"SELECT setval('{table_name}_id_seq', {max_id + 1}, false)"
                                )
                            )
                            print(f"已重置表 {table_name} 的序列到 {max_id + 1}")
                    except Exception as e:
                        print(f"重置表 {table_name} 序列时出错: {str(e)}")
    except Exception as e:
        print(f"初始化数据库失败: {e}")


# 并发控制装饰器
@contextmanager
def rate_limit(key: str, max_requests: int = 10, expire: int = 3):
    """
    限流控制
    :param key: 限流的键（通常是用户标识或IP）
    :param max_requests: 在时间窗口内允许的最大请求数
    :param expire: 时间窗口大小（秒）
    """
    conn = None
    transaction = None

    try:
        # 获取连接并开始事务
        conn = engine.connect()
        transaction = conn.begin()

        # 清理过期的访问记录
        expire_time = datetime.now(timezone.utc) - timedelta(seconds=expire)
        conn.execute(access_logs.delete().where(access_logs.c.timestamp < expire_time))

        # 查询当前时间窗口内的访问次数和最早的访问时间
        result = conn.execute(
            sqlalchemy.select(
                sqlalchemy.func.count(),
                sqlalchemy.func.min(access_logs.c.timestamp),
            )
            .select_from(access_logs)
            .where(
                sqlalchemy.and_(
                    access_logs.c.key == key,
                    access_logs.c.timestamp > expire_time,
                )
            )
        ).fetchone()

        # 正确处理元组结果
        if result is None:
            count = 0
            earliest_time = None
        else:
            count = result[0]  # 使用索引而不是字符串键
            earliest_time = result[1]

        if count >= max_requests:
            # 提交事务后再抛出异常
            if transaction:
                transaction.commit()
                transaction = None

            # 计算剩余等待时间
            if earliest_time:
                # 确保earliest_time有时区信息
                if earliest_time.tzinfo is None:
                    earliest_time = earliest_time.replace(tzinfo=timezone.utc)
                wait_seconds = (
                    expire
                    - (datetime.now(timezone.utc) - earliest_time).total_seconds()
                )
                wait_seconds = max(0, int(wait_seconds))
                raise HTTPException(
                    status_code=429,
                    detail=f"请求过于频繁，请等待 {wait_seconds} 秒后再试",
                )
            else:
                raise HTTPException(
                    status_code=429,
                    detail=f"请求过于频繁，请等待 {expire} 秒后再试",
                )

        # 在Vercel环境中，限制频繁的数据库写入操作
        if IS_VERCEL and count > max_requests * 0.7:
            # 如果当前请求接近最大数量限制，增加等待时间
            time.sleep(0.1 * count)

        # 记录当前访问
        conn.execute(
            access_logs.insert().values(key=key, timestamp=datetime.now(timezone.utc))
        )

        # 提交事务
        if transaction:
            transaction.commit()
            transaction = None

        # 执行被装饰的函数
        yield

    except Exception as e:
        # 处理异常，确保事务回滚
        if transaction:
            try:
                transaction.rollback()
            except Exception as rollback_error:
                print(f"事务回滚错误: {str(rollback_error)}")

        # 重新抛出HTTP异常
        if isinstance(e, HTTPException):
            raise e

        # 其他异常记录并转换为HTTP异常
        print(f"限流控制错误: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

    finally:
        # 关闭连接
        if conn:
            try:
                conn.close()
            except Exception as close_error:
                print(f"关闭连接错误: {str(close_error)}")
                pass
