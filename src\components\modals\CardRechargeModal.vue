<template>
    <div class="modal" @click="closeModal">
        <div class="modal-content auth-modal" @click.stop>
            <div class="modal-header">
                <h2>卡密充值</h2>
                <button class="modal-close" @click="closeModal">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                </button>
            </div>
            <div class="modal-body">
                <form class="auth-form" @submit.prevent="handleRecharge">
                    <div class="form-group">
                        <label>卡密</label>
                        <input type="text" v-model="cardKey" required placeholder="请输入卡密">
                    </div>
                    <LoadingButton class="submit-btn" :loading="loading" type="submit">充值</LoadingButton>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import LoadingButton from '../LoadingButton.vue'

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['close', 'recharge'])

const cardKey = ref('')

const closeModal = () => {
    emit('close')
    cardKey.value = ''
}

const handleRecharge = () => {
    if (!cardKey.value) return
    emit('recharge', { cardKey: cardKey.value })
}
</script> 