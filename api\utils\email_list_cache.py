"""
邮件列表缓存模块
用于缓存邮件列表，避免频繁请求相同的邮件列表
"""

import hashlib
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional
import sqlalchemy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

try:
    from api.models import temp_html_storage
    from api.db import get_db, with_db_retry
except:
    from models import temp_html_storage
    from db import get_db, with_db_retry


class EmailListCache:
    """邮件列表缓存管理器"""

    # 列表缓存有效期（5分钟）
    LIST_CACHE_MINUTES = 5

    @staticmethod
    def _generate_list_key(account: str, count: int, sender_email: str = None) -> str:
        """
        生成邮件列表缓存键

        Args:
            account: 邮箱账号
            count: 邮件数量
            sender_email: 发件人筛选

        Returns:
            缓存键
        """
        key_data = {
            "account": account.lower(),
            "count": count,
            "sender_email": sender_email.lower() if sender_email else None,
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return f"list_{hashlib.md5(key_str.encode()).hexdigest()}"

    @staticmethod
    @with_db_retry(max_retries=2)
    def get_cached_list(
        account: str, count: int, sender_email: str = None, username: str = None
    ) -> Optional[List[Dict]]:
        """
        获取缓存的邮件列表

        Args:
            account: 邮箱账号
            count: 邮件数量
            sender_email: 发件人筛选
            username: 用户名

        Returns:
            缓存的邮件列表，如果没有有效缓存则返回None
        """
        try:
            list_key = EmailListCache._generate_list_key(account, count, sender_email)
            current_time = datetime.now(timezone.utc)
            cache_expiry = current_time - timedelta(
                minutes=EmailListCache.LIST_CACHE_MINUTES
            )

            with get_db() as db:
                # 查询有效的缓存记录
                stmt = sqlalchemy.select(temp_html_storage).where(
                    sqlalchemy.and_(
                        temp_html_storage.c.email_id == list_key,
                        temp_html_storage.c.created_at > int(cache_expiry.timestamp()),
                    )
                )

                result = db.execute(stmt).fetchone()

                if result:
                    logger.info(f"邮件列表缓存命中: {account}")

                    try:
                        # 解析JSON数据
                        email_list = json.loads(result.html_content)
                        return email_list
                    except Exception as e:
                        logger.warning(f"解析邮件列表缓存失败: {str(e)}")
                        return None

                return None

        except Exception as e:
            logger.error(f"获取邮件列表缓存时发生错误: {str(e)}")
            return None

    @staticmethod
    @with_db_retry(max_retries=2)
    def save_list_cache(
        account: str,
        count: int,
        email_list: List[Dict],
        sender_email: str = None,
        username: str = None,
    ) -> bool:
        """
        保存邮件列表缓存

        Args:
            account: 邮箱账号
            count: 邮件数量
            email_list: 邮件列表
            sender_email: 发件人筛选
            username: 用户名

        Returns:
            是否保存成功
        """
        try:
            list_key = EmailListCache._generate_list_key(account, count, sender_email)
            current_time = int(datetime.now(timezone.utc).timestamp())
            expires_at = current_time + (EmailListCache.LIST_CACHE_MINUTES * 60)

            # 将邮件列表序列化为JSON
            list_json = json.dumps(email_list, ensure_ascii=False)
            content_hash = hashlib.md5(list_json.encode()).hexdigest()

            with get_db() as db:
                # 检查是否已存在
                existing_stmt = sqlalchemy.select(temp_html_storage).where(
                    temp_html_storage.c.email_id == list_key
                )
                existing_result = db.execute(existing_stmt).fetchone()

                if existing_result:
                    # 更新现有记录
                    update_stmt = (
                        temp_html_storage.update()
                        .where(temp_html_storage.c.email_id == list_key)
                        .values(
                            html_content=list_json,
                            content_hash=content_hash,
                            created_at=current_time,
                            expires_at=expires_at,
                            username=username or "unknown",
                        )
                    )
                    db.execute(update_stmt)
                else:
                    # 插入新记录
                    import uuid

                    insert_stmt = temp_html_storage.insert().values(
                        id=str(uuid.uuid4()),
                        email_id=list_key,
                        username=username or "unknown",
                        content_hash=content_hash,
                        html_content=list_json,
                        created_at=current_time,
                        expires_at=expires_at,
                    )
                    db.execute(insert_stmt)

                db.commit()
                logger.info(f"邮件列表缓存保存成功: {account}")
                return True

        except Exception as e:
            logger.error(f"保存邮件列表缓存时发生错误: {str(e)}")
            return False
