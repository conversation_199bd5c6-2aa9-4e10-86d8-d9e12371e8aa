const o=[{title:"获取邮箱",icon:"📧",category:"email",cost:10,description:"获取到不同邮件时扣费10.0余额, 需先请求一次注册邮箱",apiInfo:{endpoint:"/api/v1/email",method:"POST",params:`{
    "email": "kedaya888",// 邮箱前缀 必填 示例值:kedaya888
    "num": "1"// 获取邮件数量 默认为1 示例值:1
}`,response:`{
  "code": 200,
  "status": true,
  "data": {
    "email": "kedaya",
    "result": [
      {
        "From": "瞬连 <<EMAIL>>",
        "To": "<<EMAIL>>",
        "Subject": "瞬连邮箱验证码",
        "Time": 1752280030,
        "FormatTime": "2025-07-12 08:27:10",
        "HtmlPreviewLink": "http://api.kedaya.xyz/api/email/preview/2744a55d-45cb-4bea-a9ec-324eba428df3",
        "Content": "瞬连 邮箱验证码 尊敬的用户您好！ 您的验证码是：329986，请在 5 分钟内进行验证。如果该验证码不为您本人申请，请无视。 返回瞬连 [https://soonlink.gwzxwk.com]"
      }
    ],
    "new_emails": 0,
    "fee": 0,
    "balance": 955910,
    "message": "没有新邮件，不扣费"
  }
}`}},{title:"发送邮箱",icon:"✉️",category:"email",cost:500,description:"发送邮件服务，每次发送扣费500.0余额",apiInfo:{endpoint:"/api/v1/send-email",method:"POST",params:`{
    "from_email": "kedaya888",// 发件人邮箱前缀 必填 示例值:kedaya888
    "to_email": "<EMAIL>",// 收件人完整邮箱地址 必填 示例值:<EMAIL>
    "subject": "测试邮件",// 邮件主题 必填 示例值:测试邮件
    "content": "这是一封测试邮件"// 邮件内容 必填 示例值:这是一封测试邮件
}`,paramsDef:[{name:"from_email",type:"string",required:!0,desc:"发件人邮箱前缀",example:"kedaya888"},{name:"to_email",type:"string",required:!0,desc:"收件人完整邮箱地址",example:"<EMAIL>"},{name:"subject",type:"string",required:!0,desc:"邮件主题",example:"测试邮件"},{name:"content",type:"string",required:!0,desc:"邮件内容",example:"这是一封测试邮件"}],response:`{
  "code": 200,
  "status": true,
  "data": {
    "message": "邮件发送成功",
    "balance": 71984,
    "fee": 500
  }
}`}},{title:"微软邮箱取件",icon:"📮",category:"email",cost:50,description:"获取微软邮箱（Outlook/Hotmail）邮件，每获取一封新邮件扣费50.0余额",apiInfo:{endpoint:"/api/v1/microsoft-email",method:"POST",params:`{
    "account": "<EMAIL>",// 微软邮箱账号 必填 示例值:<EMAIL>
    "password": "147258aa",// 微软邮箱密码 必填 示例值:147258aa
    "num": "10",// 获取邮件数量 可选 默认"10" 示例值:"10"
    "sender_email": "<EMAIL>",// 发件人邮箱地址筛选 可选 示例值:<EMAIL>
    "mailbox": "inbox",// 邮箱文件夹 可选 默认收件箱 示例值:inbox
    "proxy": "127.0.0.1:7890"// 代理地址 可选 格式为host:port 示例值:127.0.0.1:7890
}`,paramsDef:[{name:"account",type:"string",required:!0,desc:"微软邮箱账号",example:"<EMAIL>"},{name:"password",type:"string",required:!0,desc:"微软邮箱密码",example:"147258aa"},{name:"num",type:"string",required:!1,desc:'获取邮件数量，默认"10"',example:"10"},{name:"sender_email",type:"string",required:!1,desc:"发件人邮箱地址筛选",example:"<EMAIL>"},{name:"mailbox",type:"string",required:!1,desc:"邮箱文件夹，默认收件箱",example:"inbox"},{name:"proxy",type:"string",required:!1,desc:"代理地址，格式为host:port",example:"127.0.0.1:7890"}],response:`{
    "code": 200,
    "status": true,
    "data": {
        "account": "<EMAIL>",
        "emails": [{
            "Subject": "欢迎使用你的新 Outlook.com 帐户",
            "Preview": "你好 欢迎使用 Outlook!",
            "From": "<EMAIL>",
            "Time": **********,
            "Content": "你好 欢迎使用 Outlook!我们的电子邮件服务旨在帮助你完成一天的工作。",
            "HtmlPreviewLink": "http://127.0.0.1:8000/api/email/preview/d0a9f4a9-3138-4e98-b860-e1e8a1bec353",
            "FormatTime": "2025-07-15 05:21:23"
        }],
        "count": 1,
        "new_emails": 0,
        "fee": 0,
        "balance": 955670,
        "message": "没有新邮件，不扣费"
    }
}`}},{title:"微软邮箱验证码提取",icon:"🔐",category:"email",cost:0,description:"从微软邮箱邮件中提取验证码的便捷接口，不单独收费但会记录获取的邮件",apiInfo:{endpoint:"/api/v1/microsoft-email/extract-code",method:"POST",params:`{
    "account": "<EMAIL>",// 微软邮箱账号 必填 示例值:<EMAIL>
    "password": "your_password",// 微软邮箱密码 必填 示例值:your_password
    "sender_email": "<EMAIL>",// 发件人邮箱地址筛选 可选 示例值:<EMAIL>
    "proxy": "127.0.0.1:7890"// 代理地址 可选 格式为host:port 示例值:127.0.0.1:7890
}`,paramsDef:[{name:"account",type:"string",required:!0,desc:"微软邮箱账号",example:"<EMAIL>"},{name:"password",type:"string",required:!0,desc:"微软邮箱密码",example:"your_password"},{name:"sender_email",type:"string",required:!1,desc:"发件人邮箱地址筛选",example:"<EMAIL>"},{name:"proxy",type:"string",required:!1,desc:"代理地址，格式为host:port",example:"127.0.0.1:7890"}],response:`{
  "code": 200,
  "status": true,
  "data": {
    "account": "<EMAIL>",
    "verification_codes": [
      {
        "subject": "您的验证码",
        "from": "<EMAIL>",
        "code": "123456",
        "time": "2025-07-15 05:21:23"
      }
    ],
    "count": 1,
    "message": "找到1个验证码"
  }
}`}},{title:"AI对话",icon:"🤖",category:"ai",cost:200,description:"提供AI对话服务, 发起对话扣费200.0余额，查询消息不扣费",apiInfo:{endpoint:"/api/v1/ai",method:"POST",params:`{
    "type": "chat | list",// 类型 必填 chat:对话,list:查询消息 示例值:list
    "symbol": "BTCUSDT",// 币对 必填 type为chat时必填,list时可不传入 示例值:BTCUSDT
    "num": "100",// K线条数 必填 type为chat时必填,list时可不传入,每100条扣500余额 示例值:100
    "time":"1H",// k线粒度 必填 1m(1分钟) 3m(3分钟) 5m(5分钟) 15m(15分钟) 30m(30分钟) 1H(1小时) 4H(4小时) 6H(6小时) 12H(12小时) 1D(1天) 3D (3天) 1W(1周) 1M (月线) type为chat时必填,list时可不传入 示例值:1H
    "conversation": ""// 对话ID 必填 type为list时必填,chat时可不传入 示例值:7480650296734105626
}`,response:`{
    "code": 200,
    "status": true,
    "conversation_id": "7480650296734105626",
    "balance": 11326.4,
    "message": "AI对话请求已提交, 请稍后使用 conversation_id 查询结果",
    "message_type": "pending"
}`}},{title:"极验3",icon:"🧩",category:"captcha",cost:100,description:"极验3识别 无感 滑块, 每次调用扣费100.0余额",apiInfo:{endpoint:"/api/v1/captcha/geetest3",method:"POST",params:`{
    "gt": "956d41eb5b20b44be6a2f738b1410d46",// 网站的gt 必填
    "challenge": "d0f96ce0f0914b33f0883ca3ef552d4d",// 只能使用一次 必填
    "proxy": "127.0.0.1:7890"// 代理ip
}`,response:`{
  "code": 200,
  "status": true,
  "data": "f41de1245cc6653eb0805a76569e9d40",
  "balance": 69239,
  "message": "验证码处理成功"
}`}},{title:"极验4",icon:"🔍",category:"captcha",cost:100,description:"极验4识别 无感 滑块, 每次调用扣费100.0余额",apiInfo:{endpoint:"/api/v1/captcha/geetest4",method:"POST",params:`{
    "captcha_id": "54088bb07d2df3c46b79f80300b0abbe",// captcha_id 必填 示例值:54088bb07d2df3c46b79f80300b0abbe
    "proxy": "127.0.0.1:7890",// 代理ip
}`,response:`{
  "code": 200,
  "status": true,
  "data": {
    "lot_number": "a00ffc7c7fe441b98744ce784c7cf59b",
    "result": "success",
    "seccode": {
      "captcha_id": "54088bb07d2df3c46b79f80300b0abbe",
      "lot_number": "a00ffc7c7fe441b98744ce784c7cf59b",
      "pass_token": "72e3dcf14f7b3647f8a8f23ae6f3e49b...",
      "gen_time": "1747482125",
      "captcha_output": "uSTl4g-okEPwpMYioIwObO2Hm4IC..."
    },
    "score": "1",
    "payload": "AgFD8gWUUuHFx-XvpP7J2c...",
    "process_token": "a0cd55dfa8052481fea68a...",
    "payload_protocol": 1
  },
  "balance": 68829,
  "message": "验证码处理成功"
}`}},{title:"二维码生成",icon:"📱",category:"qrcode",cost:1,description:"URL转二维码，支持多种自定义选项，每次调用扣费1.0余额",apiInfo:{endpoint:"/api/v1/scraping/qrcode",method:"POST",params:`{
    "url": "https://example.com",
    "width": 200,
    "margin": 4,
    "dark": "#000000",
    "light": "#ffffff",
    "type": "png"
}`,paramsDef:[{name:"url",type:"string",required:!0,desc:"需要转换为二维码的URL",example:"https://example.com"},{name:"width",type:"number",required:!1,desc:"二维码宽度(像素)",example:"300"},{name:"margin",type:"number",required:!1,desc:"二维码边距(模块)",example:"4"},{name:"dark",type:"string",required:!1,desc:"二维码暗色部分的颜色",example:"#000000"},{name:"light",type:"string",required:!1,desc:"二维码亮色部分的颜色",example:"#ffffff"},{name:"type",type:"string",required:!1,desc:"输出类型:png、jpeg、webp或string",example:"png"}],response:`{
  "code": 200,
  "status": true,
  "data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACt...(省略部分base64编码内容)",
  "balance": 69238,
  "message": "二维码生成成功"
}`}},{title:"二维码解析",icon:"🔎",category:"qrcode",cost:1,description:"解析图片中的二维码内容，支持URL或base64编码的图片，每次调用扣费1.0余额",apiInfo:{endpoint:"/api/v1/scraping/qrcode-decode",method:"POST",params:`{
    "url": "https://example.com/qrcode.png",
    "base64": ""
}`,paramsDef:[{name:"url",type:"string",required:!1,desc:"包含二维码的图片URL",example:"https://example.com/qrcode.png"},{name:"base64",type:"string",required:!1,desc:"base64编码的图片数据",example:"data:image/png;base64,iVBOR..."}],response:`{
  "code": 200,
  "status": true,
  "text": "https://example.com",
  "balance": 69237,
  "message": "二维码解析成功"
}`}}];function r(){const e=window.location.protocol,a=window.location.host;return`${e}//${a}`}function s(e){return`${r()}${e}`}function i(e){return`Content-Type: application/json
Authorization: Bearer ${(e==null?void 0:e.token)||"<token>"}`}function c(){const e=new Set;return o.forEach(a=>{a.category&&e.add(a.category)}),Array.from(e)}function n(e){return e?o.filter(a=>a.category===e):o}function p(e,a){return o.filter(t=>t.cost?t.cost>=e&&(a===void 0||t.cost<=a):!1)}function m(e){if(!e)return o;const a=e.toLowerCase();return o.filter(t=>t.title.toLowerCase().includes(a)||t.description.toLowerCase().includes(a))}export{o as default,n as filterByCategory,p as filterByCost,i as generateAuthHeader,r as getBaseApiUrl,c as getCategories,s as getFullApiUrl,m as searchApi};
