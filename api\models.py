import sqlalchemy
from sqlalchemy import Table, Column, String, DateTime, Integer, Boolean, MetaData
from datetime import datetime, timezone, timedelta
from pydantic import BaseModel, EmailStr
from typing import Optional, List
import time


# 获取当前时间戳
def get_timestamp():
    return int(time.time())


# 数据库元数据
metadata = sqlalchemy.MetaData()

# 用户表
users = sqlalchemy.Table(
    "users",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("username", sqlalchemy.String(50), unique=True),
    sqlalchemy.Column("password_hash", sqlalchemy.String(100)),
    sqlalchemy.Column("balance", sqlalchemy.Float, default=0.0),
    sqlalchemy.Column("token", sqlalchemy.String(500), unique=True, nullable=True),
    sqlalchemy.Column("inviter", sqlalchemy.String(50), nullable=True),
    sqlalchemy.Column("invite_reward", sqlalchemy.Float, default=0.0),
)

# 访问记录表（用于限流）
access_logs = sqlalchemy.Table(
    "access_logs",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("key", sqlalchemy.String(100)),
    sqlalchemy.Column(
        "timestamp", sqlalchemy.DateTime, default=datetime.now(timezone.utc)
    ),
)

# 超时退款记录表
timeout_refunds = sqlalchemy.Table(
    "timeout_refunds",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("conversation_id", sqlalchemy.String(100), unique=True),
    sqlalchemy.Column("refunded", sqlalchemy.Boolean, default=False),
    sqlalchemy.Column("username", sqlalchemy.String(50)),
    sqlalchemy.Column("amount", sqlalchemy.Float),
    sqlalchemy.Column(
        "created_at", sqlalchemy.DateTime, default=datetime.now(timezone.utc)
    ),
)

# 成功对话记录表
success_conversations = sqlalchemy.Table(
    "success_conversations",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("conversation_id", sqlalchemy.String(100)),
    sqlalchemy.Column("username", sqlalchemy.String(50)),
    sqlalchemy.Column("symbol", sqlalchemy.String(20)),
    sqlalchemy.Column(
        "created_at", sqlalchemy.DateTime, default=datetime.now(timezone.utc)
    ),
)

# 添加邮件历史表定义
email_history = Table(
    "email_history",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("username", String(64), nullable=False),
    Column("email", String(128), nullable=False),
    Column("content_hash", String(64), nullable=False),
    Column("received_at", DateTime, nullable=False),
    Column(
        "expires_at",
        DateTime,
        default=lambda: datetime.now(timezone.utc) + timedelta(days=30),
    ),
)

# 新增卡密表
card_keys = sqlalchemy.Table(
    "card_keys",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("card_key", sqlalchemy.String(64), unique=True, nullable=False),
    sqlalchemy.Column("amount", sqlalchemy.Float, nullable=False),
    sqlalchemy.Column("used", Boolean, default=False),
    sqlalchemy.Column("used_by", sqlalchemy.String(50), nullable=True),
    sqlalchemy.Column("created_at", DateTime, default=datetime.now(timezone.utc)),
    sqlalchemy.Column("used_at", DateTime, nullable=True),
)

# 订阅地址表
subscriptions = sqlalchemy.Table(
    "subscriptions",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("email", sqlalchemy.String(100), nullable=False),
    sqlalchemy.Column("password", sqlalchemy.String(50), nullable=False),
    sqlalchemy.Column("token", sqlalchemy.String(500), nullable=False),
    sqlalchemy.Column(
        "created_at",
        sqlalchemy.DateTime(timezone=True),
        default=lambda: datetime.now(timezone(timedelta(hours=8))),
    ),
    sqlalchemy.Column("username", sqlalchemy.String(50), nullable=False),
)

# 电子邮件创建日志表
email_creation_logs = sqlalchemy.Table(
    "email_creation_logs",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("username", sqlalchemy.String(50), nullable=False),
    sqlalchemy.Column("email", sqlalchemy.String(100), nullable=False),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime, nullable=False),
    sqlalchemy.Column("expires_at", sqlalchemy.DateTime, nullable=False),
)

# 邮件发送日志表
email_sending_logs = sqlalchemy.Table(
    "email_sending_logs",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("username", sqlalchemy.String(50), nullable=False),
    sqlalchemy.Column("from_email", sqlalchemy.String(100), nullable=False),
    sqlalchemy.Column("to_email", sqlalchemy.String(100), nullable=False),
    sqlalchemy.Column("subject", sqlalchemy.String(200), nullable=False),
    sqlalchemy.Column("content_hash", sqlalchemy.String(32), nullable=False),
    sqlalchemy.Column("sent_at", sqlalchemy.DateTime, nullable=False),
    sqlalchemy.Column("status", sqlalchemy.Boolean, default=True),
    sqlalchemy.Column("sent_at_ts", sqlalchemy.Integer, default=get_timestamp),
)

# 临时HTML内容存储表
temp_html_storage = sqlalchemy.Table(
    "temp_html_storage",
    metadata,
    sqlalchemy.Column(
        "id", sqlalchemy.String(36), primary_key=True
    ),  # UUID作为唯一标识
    sqlalchemy.Column("email_id", sqlalchemy.String(50), nullable=True),
    sqlalchemy.Column("username", sqlalchemy.String(50), nullable=True),  # 用户名
    sqlalchemy.Column(
        "content_hash", sqlalchemy.String(32), nullable=False
    ),  # 内容哈希值
    sqlalchemy.Column("html_content", sqlalchemy.Text, nullable=False),
    sqlalchemy.Column("created_at", sqlalchemy.Integer, nullable=False),  # 时间戳
    sqlalchemy.Column("expires_at", sqlalchemy.Integer, nullable=False),  # 过期时间戳
    sqlalchemy.Index("idx_temp_html_expires", "expires_at"),  # 为过期清理创建索引
    sqlalchemy.Index(
        "idx_temp_html_content_hash", "content_hash"
    ),  # 为内容哈希创建索引
)

# 消费记录表（30天过期）
consumption_records = sqlalchemy.Table(
    "consumption_records",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("username", sqlalchemy.String(50), nullable=False),
    sqlalchemy.Column("details", sqlalchemy.String(255), nullable=False),  # 消费详情
    sqlalchemy.Column("amount", sqlalchemy.Float, nullable=False),  # 消费金额
    sqlalchemy.Column(
        "created_at", DateTime, default=datetime.now(timezone.utc)
    ),  # 创建时间
    sqlalchemy.Column(
        "expires_at",
        DateTime,
        default=lambda: datetime.now(timezone.utc) + timedelta(days=30),  # 30天后过期
    ),
)


# Pydantic 模型定义
class UserCreate(BaseModel):
    email: str
    password: str
    inviteCode: str = None


class User(BaseModel):
    username: str
    balance: float
    token: Optional[str] = None


class Token(BaseModel):
    access_token: str
    token_type: str


# 邮箱验证请求模型
class EmailVerifyRequest(BaseModel):
    type: Optional[str] = "get"
    email: str
    num: str = "1"


# 邮件发送请求模型
class EmailSendRequest(BaseModel):
    from_email: str
    to_email: EmailStr
    subject: str
    content: str


# 充值请求模型
class RechargeRequest(BaseModel):
    username: str
    amount: float


# AI对话请求模型
class AIRequest(BaseModel):
    type: str
    symbol: str = ""
    conversation: str = ""
    num: str = ""
    time: str = ""


# 验证码请求模型
class Captcha3Request(BaseModel):
    gt: str
    challenge: str
    proxy: str = None


class Captcha4Request(BaseModel):
    captcha_id: str
    proxy: str = None


# 二维码生成请求类
class QRCodeRequest(BaseModel):
    url: str
    key: str = "7f561ef2-658e-44ae-bf17-5de35ffade2b"
    width: int = 200
    margin: int = 4
    dark: str = "#000000"
    light: str = "#ffffff"
    type: str = "png"


# 二维码解析请求类
class QRCodeDecodeRequest(BaseModel):
    url: Optional[str] = None
    base64: Optional[str] = None
    key: str = "7f561ef2-658e-44ae-bf17-5de35ffade2b"


class CardKeyGenRequest(BaseModel):
    amount: float
    count: int = 1


class CardKeyUseRequest(BaseModel):
    card_key: str


# 微软邮箱登录缓存表
microsoft_login_cache = sqlalchemy.Table(
    "microsoft_login_cache",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.Integer, primary_key=True),
    sqlalchemy.Column("account", sqlalchemy.String(128), nullable=False, index=True),
    sqlalchemy.Column(
        "account_hash", sqlalchemy.String(64), nullable=False, unique=True
    ),  # 账号+密码的哈希值
    sqlalchemy.Column("refresh_token", sqlalchemy.Text, nullable=False),
    sqlalchemy.Column("access_token", sqlalchemy.Text, nullable=True),
    sqlalchemy.Column("id_token", sqlalchemy.Text, nullable=True),
    sqlalchemy.Column("puid", sqlalchemy.String(128), nullable=True),
    sqlalchemy.Column("property_set_id", sqlalchemy.String(128), nullable=True),
    sqlalchemy.Column("login_time", sqlalchemy.DateTime, nullable=False),
    sqlalchemy.Column("expires_at", sqlalchemy.DateTime, nullable=False),  # 1小时后过期
    sqlalchemy.Column("last_used", sqlalchemy.DateTime, nullable=False),
    sqlalchemy.Column(
        "username", sqlalchemy.String(50), nullable=False
    ),  # 使用该缓存的用户
)


# 微软邮箱请求类
class MicrosoftEmailRequest(BaseModel):
    account: str  # 微软邮箱账号
    password: str  # 微软邮箱密码
    num: Optional[str] = "10"  # 获取邮件数量，默认"10"
    sender_email: Optional[str] = None  # 发件人邮箱地址筛选
    mailbox: Optional[str] = None  # 邮箱文件夹
    proxy: Optional[str] = None  # 代理地址
    fast_mode: Optional[bool] = False  # 快速模式，跳过详细内容获取
