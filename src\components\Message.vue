<template>
  <Transition name="message-fade" @after-leave="destroy">
    <div v-if="visible" :class="['message', `message-${type}`]">
      <div class="message-content">
        <i class="message-icon">{{ icon }}</i>
        <span>{{ content }}</span>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'info'
  },
  content: {
    type: String,
    required: true
  },
  duration: {
    type: Number,
    default: 3000
  }
})

const emit = defineEmits(['destroy'])

const visible = ref(false)
const icon = computed(() => {
  switch (props.type) {
    case 'success': return '✓'
    case 'warning': return '⚠'
    case 'error': return '✕'
    default: return 'ℹ'
  }
})

const destroy = () => {
  emit('destroy')
}

onMounted(() => {
  visible.value = true
  if (props.duration > 0) {
    setTimeout(() => {
      visible.value = false
    }, props.duration)
  }
})
</script>

<style scoped>
.message {
  position: relative;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  pointer-events: none;
  margin: 8px 0;
  user-select: none;
  -webkit-user-select: none;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.message-icon {
  font-size: 16px;
}

.message-success {
  background: rgba(103, 194, 58, 0.9);
  color: white;
}

.message-warning {
  background: rgba(230, 162, 60, 0.9);
  color: white;
}

.message-info {
  background: rgba(144, 147, 153, 0.9);
  color: white;
}

.message-error {
  background: rgba(245, 108, 108, 0.9);
  color: white;
}

.message-fade-enter-active,
.message-fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.message-fade-enter-from,
.message-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>