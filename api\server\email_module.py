import pexpect
from fastapi import HTTPException, APIRouter, Body, Query
from pydantic import BaseModel, EmailStr, Field


# 创建API路由
router = APIRouter(prefix="/email", tags=["邮箱管理"])


# 请求和响应模型
class EmailAccountRequest(BaseModel):
    email: str = Field(..., description="邮箱账号名称，不包含@domain部分")


class EmailResponse(BaseModel):
    message: str
    email: str


def execute_container_command(container_name, command, password):
    try:
        child = pexpect.spawn(
            f"docker exec -it {container_name} {command}", encoding="utf-8", timeout=60
        )
        child.expect("Enter password for new user:")
        child.sendline(password)

        child.expect(
            [
                pexpect.EOF,
                "imap: user already exists",
                "success",
                "error",
                pexpect.TIMEOUT,
            ]
        )
        output = child.before.strip()
        if "already exist" in output:
            return output

        return True

    except pexpect.TIMEOUT:
        raise HTTPException(status_code=500, detail="Command execution timeout.")
    except pexpect.exceptions.EOF:
        return "Command execution finished, but no new output."
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error executing command: {str(e)}"
        )


def create_email_account(account_email: str, passwd: str):
    container_name = "1Panel-maddy-mail-VBhJ"
    try:
        full_email = f"{account_email}@kedaya.xyz"
        command_1 = f"maddy creds create {full_email}"
        command_2 = f"maddy imap-acct create {full_email}"

        result_1 = execute_container_command(container_name, command_1, passwd)
        if result_1 is True:
            execute_container_command(container_name, command_2, passwd)
            return True
        else:
            return result_1
    except pexpect.TIMEOUT:
        raise HTTPException(status_code=500, detail="Command execution timeout.")
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error executing command: {str(e)}"
        )


@router.get("/create", response_model=EmailResponse, summary="创建邮箱账号")
async def create_email_account_endpoint(
    email: str = Query(..., description="邮箱账号名称，不包含@domain部分"),
    passwd: str = Query(..., description="邮箱密码"),
):
    """
    创建邮箱账号

    - **email**: 邮箱账号名称（不包含@domain部分）

    返回创建结果信息
    """
    try:
        print(passwd)
        result = create_email_account(email, passwd)
        full_email = f"{email}@kedaya.xyz"
        if result is True:
            return EmailResponse(message=f"邮箱账号创建成功", email=full_email)
        else:
            return EmailResponse(
                message=f"邮箱账号创建失败: {result}", email=full_email
            )
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建邮箱账号失败: {str(e)}")
