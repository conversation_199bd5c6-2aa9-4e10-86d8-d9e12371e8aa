{"name": "modern-vue3-cards", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "vercel-build": "vite build", "postinstall": "npm run build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.1.2", "crypto-js": "^4.2.0", "element-plus": "^2.9.7", "highlight.js": "^11.11.1", "marked": "^15.0.7", "node-rsa": "^1.1.1", "pako": "^2.1.0", "vue": "^3.3.4", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "sass": "^1.89.1", "vite": "^4.3.9"}, "main": "vite.config.js", "keywords": [], "author": "", "license": "ISC", "description": "KedayaAPI Frontend", "engines": {"node": ">=16.0.0"}}