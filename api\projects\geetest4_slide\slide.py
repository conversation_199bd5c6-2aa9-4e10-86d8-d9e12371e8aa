# -*- coding: utf-8 -*-
import time
import uuid
import json
from curl_cffi import requests
from .gap import get_gap
from .traces import get_track
import hashlib
import base64
import binascii
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from cryptography.hazmat.primitives.asymmetric import padding as crypto_padding
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend
import random


def get_image(captchaId, challenge):
    url = (
        "https://gcaptcha4.geetest.com/load?captcha_id="
        + captchaId
        + "&challenge="
        + challenge
        + "&client_type=web&risk_type=slide&pt=1&lang=zho&callback=geetest_"
        + str(round(time.time() * 1000))
    )
    res = requests.get(url).text
    res = json.loads(res[res.index("(") + 1 : res.rindex(")")])
    # 获取图片字节而不是下载
    bg_bytes = requests.get("https://static.geetest.com/" + res["data"]["bg"]).content
    slide_bytes = requests.get(
        "https://static.geetest.com/" + res["data"]["slice"]
    ).content

    # 直接使用图片字节进行识别
    distance = get_gap(slide_bytes, bg_bytes)
    track = get_track(distance)
    return res, distance, track


def get_key():
    """生成随机密钥"""
    s4 = ""
    for i in range(4):
        s4 = s4 + hex(int((1 + random.random()) * 65536))[2:].zfill(4)[:4]
    return s4


def md5_encrypt(text):
    """MD5加密"""
    return hashlib.md5(text.encode()).hexdigest()


def aes_encrypt(key, text):
    """AES加密"""
    # 使用pycryptodome库实现AES加密
    text_bytes = text.encode("utf-8")
    key_bytes = key.encode("utf-8")
    iv = b"0000000000000000"

    cipher = AES.new(key_bytes, AES.MODE_CBC, iv)
    padded_text = pad(text_bytes, AES.block_size)
    encrypted = cipher.encrypt(padded_text)

    # 转为base64然后再转为hex
    base64_encrypted = base64.b64encode(encrypted).decode()
    hex_encrypted = binascii.hexlify(base64.b64decode(base64_encrypted)).decode()

    return hex_encrypted


def rsa_encrypt(data):
    """RSA加密"""
    public_key_1 = "00C1E3934D1614465B33053E7F48EE4EC87B14B95EF88947713D25EECBFF7E74C7977D02DC1D9451F79DD5D1C10C29ACB6A9B4D6FB7D0A0279B6719E1772565F09AF627715919221AEF91899CAE08C0D686D748B20A3603BE2318CA6BC2B59706592A9219D0BF05C9F65023A21D2330807252AE0066D59CEEFA5F2748EA80BAB81"
    public_key_2 = "10001"

    # 将十六进制字符串转换为整数
    n = int(public_key_1, 16)
    e = int(public_key_2, 16)

    # 创建RSA公钥
    public_numbers = rsa.RSAPublicNumbers(e, n)
    public_key = public_numbers.public_key(default_backend())

    # 加密数据
    encrypted = public_key.encrypt(data.encode(), crypto_padding.PKCS1v15())

    # 返回十六进制字符串
    return encrypted.hex()


def get_w(captchaId, lot_number, detail_time, distance, passtime, track):
    """生成w参数，替代JavaScript中的get_w函数"""
    random_key = get_key()
    pow_msg = f"1|0|md5|{detail_time}|{captchaId}|{lot_number}||{random_key}"

    data = {
        "setLeft": distance,
        "track": track,
        "passtime": passtime,
        "userresponse": distance / (0.8876 * 340 / 300),
        "device_id": "D00D",
        "lot_number": lot_number,
        "pow_msg": pow_msg,
        "pow_sign": md5_encrypt(pow_msg),
        "geetest": "captcha",
        "lang": "zh",
        "ep": "123",
        "cuel": "632729377",
        "em": {"ph": 0, "cp": 0, "ek": "11", "wd": 1, "nt": 0, "si": 0, "sc": 0},
    }

    json_data = json.dumps(data, separators=(",", ":"))
    w = aes_encrypt(random_key, json_data) + rsa_encrypt(random_key)
    return w


def geetest4_slide(captchaId):
    try:
        challenge = str(uuid.uuid4())

        res, distance, track = get_image(captchaId, challenge)
        lot_number = res["data"]["lot_number"]
        passtime = track[-1][-1]
        detail_time = res["data"]["pow_detail"]["datetime"]

        # 使用Python实现的get_w函数
        try:
            w = get_w(captchaId, lot_number, detail_time, distance, passtime, track)

            url = "https://gcaptcha4.geetest.com/verify"
            params = {
                "callback": "geetest_" + str(round(time.time() * 1000)),
                "captcha_id": captchaId,
                "client_type": "web",
                "lot_number": lot_number,
                "risk_type": "slide",
                "payload": res["data"]["payload"],
                "process_token": res["data"]["process_token"],
                "payload_protocol": "1",
                "pt": "1",
                "w": w,
            }
            res = requests.get(url, params=params).text
            res = json.loads(res[res.index("(") + 1 : res.rindex(")")])
            return res
        except Exception as e:
            print(f"加密过程出错: {e}")
    except Exception as e:
        print(f"程序执行错误: {e}")


if __name__ == "__main__":
    captchaId = "54088bb07d2df3c46b79f80300b0abbe"
    geetest4_slide(captchaId)
